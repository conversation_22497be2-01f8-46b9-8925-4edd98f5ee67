# WebView非HTTP URL处理改进方案

## 功能概述

改进了WebView中非HTTP URL的处理逻辑，实现了以下需求：

1. **非HTTP URL总是返回true**：对于所有非`http://`和`https://`开头的URL，`shouldOverrideUrlLoading`总是返回`true`并尝试处理
2. **智能应用检测**：检测系统中可以处理该URL的应用
3. **美观的确认对话框**：显示应用图标、名称，让用户选择是否打开
4. **系统级文本**：使用系统默认的按钮文本（OK/Cancel），无需依赖应用内翻译
5. **Material Design风格**：对话框样式与应用主题匹配

## 实现架构

### 核心组件

1. **UrlIntentHandler** - URL处理核心逻辑
2. **AppLaunchDialog** - 自定义应用启动对话框
3. **SnifferDelegate** - WebView客户端集成

### 处理流程

```
WebView URL → 检测非HTTP → 创建Intent → 查找应用 → 显示对话框 → 用户选择 → 启动应用
```

## 支持的URL类型

### 常见应用协议
- `mailto:` - 邮件应用 (Gmail, Outlook等)
- `tel:` - 电话应用
- `sms:` - 短信应用
- `whatsapp://` - WhatsApp
- `telegram://` - Telegram
- `intent://` - Android Intent URL

### 示例URL
```
mailto:<EMAIL>
tel:+1234567890
sms:+1234567890?body=Hello
whatsapp://send?text=Hello%20World
intent://scan/#Intent;scheme=zxing;package=com.google.zxing.client.android;end
```

## 对话框特性

### 视觉设计
- **应用图标**：显示目标应用的图标（120x120dp）
- **应用名称**：显示应用的真实名称
- **Material Design**：遵循Material Design规范
- **响应式布局**：适配不同屏幕尺寸

### 用户体验
- **系统按钮文本**：使用`android.R.string.ok`和`android.R.string.cancel`
- **多语言支持**：自动适配系统语言
- **错误处理**：对话框失败时自动降级到系统选择器
- **安全检查**：防止在Activity销毁时显示对话框

## 代码示例

### 基本使用
```kotlin
// 在WebViewClient中
override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
    return handleIntentUrl(url)
}

private fun handleIntentUrl(url: String?): Boolean {
    if (url != null && !url.startsWith("http://") && !url.startsWith("https://")) {
        UrlIntentHandler.handleNonHttpUrl(url)
        return true
    }
    return false
}
```

### 自定义对话框
```kotlin
AppLaunchDialog.show(
    activity = activity,
    appName = "Gmail",
    appIcon = gmailIcon,
    intent = mailtoIntent
)
```

## 错误处理机制

### 多层降级策略
1. **首选**：自定义对话框（显示图标和名称）
2. **备用**：系统Intent选择器
3. **最终**：直接启动应用（如果只有一个可用应用）

### 异常处理
- `ActivityNotFoundException` - 没有应用可以处理该URL
- `SecurityException` - 权限不足
- `IllegalArgumentException` - URL格式错误

## 配置选项

### 日志级别
```kotlin
// 在UrlIntentHandler中可以调整日志级别
private val log = newLog("UrlIntentHandler")
```

### 对话框样式
```kotlin
// 可以自定义对话框主题
AlertDialog.Builder(activity, android.R.style.Theme_Material_Dialog_Alert)
```

## 测试验证

### 单元测试
- URL类型检测
- Intent创建逻辑
- 应用信息获取

### 集成测试
- 真实应用启动
- 对话框显示
- 错误场景处理

## 性能优化

### 内存管理
- 及时释放Drawable资源
- 避免Activity泄漏
- 合理的缓存策略

### 响应速度
- 异步查询应用信息
- 快速失败机制
- 最小化主线程阻塞

## 兼容性

### Android版本
- 最低支持：Android 5.0 (API 21)
- 推荐版本：Android 8.0+ (API 26+)
- 测试覆盖：Android 5.0 - 14.0

### 应用兼容
- 支持所有标准Intent协议
- 兼容第三方应用的自定义协议
- 处理应用未安装的情况

## 安全考虑

### URL验证
- 防止恶意URL注入
- 验证Intent参数
- 限制可启动的应用类型

### 权限检查
- 检查应用启动权限
- 验证Intent权限
- 防止权限提升攻击

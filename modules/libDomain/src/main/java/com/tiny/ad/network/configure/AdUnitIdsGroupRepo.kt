package com.tiny.ad.network.configure

import com.tiny.domain.ext.logEvent
import com.tiny.domain.ext.logI
import com.tiny.domain.ext.rValue
import com.tiny.domain.util.DeviceUtil
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.arrayList
import com.tinypretty.component.json
import com.tinypretty.component.str
import com.tinypretty.event.CommonEventKey
import com.tinypretty.event.CommonEventName
import org.json.JSONObject

object AdUnitIdsGroupRepo {
    private const val TAG = "AdUnitIdsGroupRepo"
    private const val KEY_AD_IDS = "ad_ids"
    private const val KEY_AD_COUNTRY_TIER = "ad_country_tier"
    private const val FETCH_VALUE_TIMEOUT = 10000L
    var mCachedAdUnitIds: JSONObject? = null
    const val TYPE_BANNER = "banner"
    const val TYPE_RECT = "rect"
    const val TYPE_APP_OPEN = "appOpen"
    const val TYPE_POP_UP = "interstitial"
    const val TYPE_REWARD = "reward"

    fun getGroupIds(type: String): ArrayList<JSONObject>? {
        initDefaultAdUnitIds()
        val ids = mCachedAdUnitIds?.arrayList<JSONObject>(type) ?: return null
        TAG.logI("getGroupIds", "type" to type, "ids" to ids, "mCachedAdUnitIds" to mCachedAdUnitIds)
        return ids
    }

    suspend fun updateAdUnitIds() {
        val adIds = KEY_AD_IDS.rValue("", FETCH_VALUE_TIMEOUT)
        val adCountryTier = KEY_AD_COUNTRY_TIER.rValue("", FETCH_VALUE_TIMEOUT)
        if (adIds.isNotEmpty()) {
            TAG.logI("updateAdUnitIds start", "mCachedAdUnitIds" to mCachedAdUnitIds)
            setAdUnitIds(adIds, adCountryTier)
        } else {
            TAG.logI("updateAdUnitIds ignore adIds is Empty", "mCachedAdUnitIds" to mCachedAdUnitIds)
        }
    }

    private fun initDefaultAdUnitIds() {
        if (mCachedAdUnitIds != null) {
            TAG.logI("initDefaultAdUnitIds ignore", "mCachedAdUnitIds" to mCachedAdUnitIds)
            return
        }

        fun readAssetFile(fileName: String): String {
            return GlobalModule.mApp.assets.open(fileName).bufferedReader().use { it.readText() }
        }

        val adIdsDefault = readAssetFile("ad/$KEY_AD_IDS.json")
        val adCountryTierDefault = readAssetFile("ad/$KEY_AD_COUNTRY_TIER.json")
        val adIds = KEY_AD_IDS.rValue(adIdsDefault)
        val adCountryTier = KEY_AD_COUNTRY_TIER.rValue(adCountryTierDefault)
        setAdUnitIds(adIds, adCountryTier)
        TAG.logI("setDefaultAdUnitIds", "mCachedAdUnitIds" to mCachedAdUnitIds)
    }

    private fun setAdUnitIds(adIds: String, adCountryTier: String) {
        // 过滤条件
        val tier = getTier(adCountryTier)
        val activeTime = DeviceUtil.getSystemCurrentTime() - DeviceUtil.appInstallTime

        val adList = adIds.json().arrayList<JSONObject>(tier)
        val targetAdGroup = adList.lastOrNull {
            activeTime >= it.optLong("maxActiveTime")
        } ?: adList.firstOrNull()
        CommonEventName.EVENT_AD_INIT_CONFIG.logEvent("tier" to tier, "activeTime" to activeTime, "ad_size" to (targetAdGroup?.length() ?: 0))
        mCachedAdUnitIds = targetAdGroup
    }

    private fun getTier(adCountryTier: String): String {
        val countryCode = DeviceUtil.getCountryCodeFromLocale()

        CommonEventName.EVENT_APP_CONFIG_COUNTRY_CODE.logEvent(CommonEventKey.KEY to countryCode)
        adCountryTier.arrayList<JSONObject>().forEach {
            if (countryCode in it.arrayList<String>("country")) {
                return it.str("tier")
            }
        }
        return "t4"
    }
}
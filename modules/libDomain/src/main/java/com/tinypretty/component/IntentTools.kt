package com.tinypretty.component

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.core.app.ShareCompat
import androidx.core.content.FileProvider
import com.tiny.domain.util.AdConfigure.mAnalysis
import com.tiny.domain.util.MarketHelper
import com.tinypretty.component.GlobalModule.mApp
import java.io.File


object IntentTools {
    fun Context.openSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        val uri = Uri.fromParts("package", packageName, null)
        intent.data = uri
        startActivity(intent)
    }

    fun shareImageFile(context: Context, file: File, emailSubject: String, emailBody: String) {
        // Use FileProvider to get a Uri for the file
        val uri: Uri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.provider",  // Use your app's package name followed by .provider
            file
        )

        // Create an Intent with the action ACTION_SEND
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_STREAM, uri)
            putExtra(Intent.EXTRA_SUBJECT, emailSubject)  // Add email subject
            putExtra(Intent.EXTRA_TEXT, emailBody)  // Add email body
            type = "message/rfc822"  // Use this MIME type to share emails
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)  // Add this flag to give the receiving app permission to read the Uri
        }

        // Start the share Intent
        context.startActivity(Intent.createChooser(shareIntent, "Send Email"))
    }

    fun shareImageFile(context: Context, file: File) {
        // Use FileProvider to get a Uri for the file
        val uri: Uri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.provider",  // Use your app's package name followed by .provider
            file
        )

        // Create an Intent with the action ACTION_SEND
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_STREAM, uri)
            type = "image/*"  // Use this MIME type to share images
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)  // Add this flag to give the receiving app permission to read the Uri
        }

        // Start the share Intent
        context.startActivity(Intent.createChooser(shareIntent, null))
    }

    fun shareFile(context: Activity?, uri: Uri) {
        context ?: return
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_STREAM, uri)
            type = context.contentResolver.getType(uri) // 获取文件的MIME类型
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION) // 添加这个标志以给接收的应用读取URI的权限
        }
        runCatching {
            context.startActivity(Intent.createChooser(shareIntent, "Share File"))
        }.onFailure {
            GlobalModule.toast("No app found to share file,${it.message}")
        }
    }

    fun fileUri(ctx: Context?, file: File): Uri? {
        var result: Uri? = null
        try {
            ctx?.let {
                result =
                    FileProvider.getUriForFile(it, "${it.packageName}.mandiprovider", file)
            }
        } catch (e: Exception) {
        }

        return result
    }

    fun sharePdf() {

        fun handleFile(
            activity: Activity?,
            filePath: String,
            action: String,
            title: String,
            inUri: Uri? = null
        ) {
            activity?.run {
                var file = File(filePath)
                var uri = fileUri(activity, file)
                if (inUri != null) {
                    uri = inUri
                }
                if (uri != null) {

                    val intent = ShareCompat.IntentBuilder.from(this)
                        .setStream(uri) // uri from FileProvider
                        .intent
                        .setAction(Intent.ACTION_VIEW) //Change if needed
                        .setDataAndType(uri, "application/pdf")
                        .addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                        .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

//                Intent(action).let {
//                    it.data = uri
//                    activity.grantUriPermission(
//                        packageName,
//                        uri,
//                        Intent.FLAG_GRANT_WRITE_URI_PERMISSION or Intent.FLAG_GRANT_READ_URI_PERMISSION
//                    )
//
////                    grantUriPermission(activity.packageName,uri,Intent.FLAG_GRANT_READ_URI_PERMISSION)
//                    it.putExtra(Intent.EXTRA_STREAM, uri)
//                    it.type = getMimeType(filePath)
//                    it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//                    it.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    startActivity(Intent.createChooser(intent, title))
                }
            }
        }
    }

    fun shareFile(activity: Activity?, filePath: String) {
        handleFile(activity, filePath, Intent.ACTION_SEND, "share to")
    }

    //    不能view pdf 很奇怪
    fun openFile(activity: Activity?, file: File, mimeType: String? = null) {
        handleFile(activity, file.absolutePath, Intent.ACTION_VIEW, "view")

//        try {
//            activity?.run {
//                var uri = fileUri(activity,file)
//
//                if (uri != null) {
//                    Intent(Intent.ACTION_VIEW).let {
//                        it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//                        it.data = uri
//                        it.setDataAndType(uri, mimeType?:getMimeType(file.absolutePath))
////                        L.d { "handleFile ${it.data} ${it.type}" }
//                        startActivity(it)
//                    }
//                }
//            }
//        } catch (e: Exception) {
//            activity?.run {
//                Toast.makeText(this, e.message, Toast.LENGTH_SHORT).show()
//            }
//        }
    }

    fun openFile(activity: Activity?, filePath: String, mimeType: String? = null) {
        openFile(activity, File(filePath), mimeType)
    }

    fun handleFile(
        activity: Activity?,
        filePath: String,
        action: String,
        title: String,
        inUri: Uri? = null
    ) {
        activity?.run {
            var file = File(filePath)
            var uri = fileUri(activity, file)
            if (inUri != null) {
                uri = inUri
            }
            if (uri != null) {
                Intent(action).let {
                    it.data = uri
                    it.putExtra(Intent.EXTRA_STREAM, uri)
                    it.type = getMimeType(filePath)
                    it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    it.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    startActivity(Intent.createChooser(it, title))
                }
            }
        }
    }

    fun isVideo(filePath: String): Boolean {
        var url = filePath.lowercase()
        var ends = "mpeg;mpg;avi;mov;mpg4;mp4;flv;mp3;m3u8;wmv;ogg;m3u;wav;acc".split(";")
        for (end in ends) {
            if (url.endsWith(".$end")) {
//                L.d {"isVideo = true"}
                return true
            }
        }
//        L.d {"isVideo = false $filePath"}

        var type = getMimeType(filePath)
//        L.d { "getMimeType = $type" }
        return (type.lowercase().contains("video"))
    }

    fun appIntent(packageName: String): Intent? {
        return mApp.packageManager.getLaunchIntentForPackage(packageName)
    }

    fun openApp(intent: Intent?) {
        intent?.let {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            mApp.startActivity(intent)
        }
    }


    fun getMimeType(filePath: String?): String {
        val mmr = MediaMetadataRetriever()
        var mime = "*/*"
        if (filePath != null) {
            try {
                mmr.setDataSource(filePath)
                mime = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_MIMETYPE) ?: ""
            } catch (e: IllegalStateException) {
                return mime
            } catch (e: IllegalArgumentException) {
                return mime
            } catch (e: RuntimeException) {
                return mime
            }

        }
        return mime
    }

    fun shareApp(ctx: Context, shareTitle: String, shareContent: String) {
        try {
            Intent(Intent.ACTION_SEND).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_TITLE, shareTitle)
                putExtra(Intent.EXTRA_TEXT, shareContent)
                ctx.startActivity(Intent.createChooser(this, shareTitle))
            }
        } catch (e: Exception) {
        }
    }


    fun packageNameToUri(packageName: String): String =
        Uri.decode("market://details?id=$packageName")

    fun openGP(ctx: Context, appPackageName: String = ctx.packageName) {
        mL.d { "openGP appPackageName = $appPackageName" }
        var market = MarketHelper.getTargetMarket()
        if (market != null) {
            MarketHelper.click(ctx, appPackageName, market)
        } else {
            openGPSelect(ctx, packageNameToUri(appPackageName))
        }
    }

    fun openGPSelect(ctx: Context, uri: String, marketPackageName: String = "com.android.vending") {
        val marketUri = Uri.parse(uri)
        var succeed = true
        try {
            val intent = Intent(Intent.ACTION_VIEW, marketUri)
            if (marketPackageName.isNotBlank()) {
//                intent.setPackage("com.android.vending")
//                L.d { "openGP google play" }
            } else {
//                L.d { "openGP any market" }
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            ctx.startActivity(intent)
        } catch (ex: android.content.ActivityNotFoundException) {
            succeed = false
        }

        if (succeed) {
            return
        }

        try {
            val intent = Intent(Intent.ACTION_VIEW, marketUri)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            ctx.startActivity(intent)
        } catch (ex: android.content.ActivityNotFoundException) {
            Toast.makeText(ctx, "Couldn't find PlayStore on this device", Toast.LENGTH_SHORT)
                .show()
        }

    }


//    compos
//    fun openActivity(intent:Intent, activithInfo: Pair<String, Drawable?>) {
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//        activity?.startActivity(intent)
//
//        try {
//            activity?.let {
//                mL.d { "intentUtils openActivity,${activity}, ${intent.`package`} $intent" }
//                var infoNew: Pair<String, Drawable?>? = null
//                it.runOnUiThread {
//                    CS.ui({ infoNew = CommonUtil.appLabel(intent) }) {
//                        val info = infoNew
//                        info?.run {
//                            L.record_event("openActivity", "${info.first}")
//                            AlertDialog.Builder(it).apply {
//                                setTitle("OPEN ${info.first} ?")
//                                setIcon(info.second)
//                                setItems(
//                                    arrayOf<CharSequence>("YES", "NO")
//                                ) { dialog, which ->
//                                    if (which == 0) {
//                                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//                                        activity?.startActivity(intent)
//                                    }
//                                }.apply {
//                                    createBarView.invoke(activity, false,false)?.run {
//                                        setView(this)
//                                    }
//                                }
//                            }.create().run {
//                                if (!activity.isFinishing) {
//                                    show()
//                                }
//                            }
//                        }
//                    }
//
//
//                }
//            }
//        } catch (e: ActivityNotFoundException) {
//            L.e { "ActivityNotFoundException" }
//        }
//    }

    fun startWebBrowser(activity: Context?, url: String) {
        startActivity(activity, intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME))
    }

    fun startActivity(activity: Context?, url: String, onSucceed: () -> Unit = {}) {
        try {
            // 以下固定写法
            val intent = Intent(
                Intent.ACTION_VIEW,
                Uri.parse(url)
            )
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
            startActivity(activity, intent, onSucceed)
        } catch (e: Exception) {
        }

    }


    fun AppAvaible(url: String): Boolean {
        val intent = Intent(
            Intent.ACTION_VIEW,
            Uri.parse(url)
        )
        return IntentTools.appLabel(intent) != null
    }


    fun startActivity(activity: Context?, intent: Intent, onSucceed: () -> Unit = {}) {
        intent.flags =
            Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP

        appLabel(intent)?.let {
            mL.d { "start_activity ${intent.data} ${it.first}" }
            mAnalysis.onEvent("start_activity", it.first)
            onSucceed.invoke()
            activity?.startActivity(intent)
        }
    }

    fun appLabel(intent: Intent): Pair<String, Drawable?>? {
        val pm = mApp.packageManager
        val handlers = pm.queryIntentActivities(
            intent,
            PackageManager.MATCH_DEFAULT_ONLY
        )
        if (handlers == null || handlers.isEmpty()) {
            return null
        }

        var ai: ApplicationInfo?
        try {
            ai = pm.getApplicationInfo(handlers.first().activityInfo.packageName, 0)

        } catch (e: PackageManager.NameNotFoundException) {
            mL.d { "APP LABEL ${e}" }
            ai = null
        }

        val applicationName = if (ai != null) pm.getApplicationLabel(ai) else "unknown"
        val icon: Drawable? = if (ai != null) pm.getApplicationIcon(ai) else null
        return Pair(applicationName.toString(), icon)
    }

    //    fun existWX() = intentIsAvailable("weixin://dl/business/?t=HQN6wXo2Z5")
    //
    //  需要添加权限<uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
    //  tools:ignore="QueryAllPackagesPermission" />
    fun existPDD() = intentIsAvailable("pinduoduo://com.xunmeng.pinduoduo/duo_coupon_landing.html")

    fun intentIsAvailable(url: String): Boolean {
        if (url.isBlank())
            return false

        var intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME)
        val mgr = mApp.packageManager
        val list = mgr.queryIntentActivities(
            intent!!,
            PackageManager.MATCH_DEFAULT_ONLY
        )
        return (list.size > 0).let {
            mL.d { "intentIsAvailable $url = $this" }
            it
        }
    }

    fun startActivity(url: String?): Boolean {
        if (url != null && listOf("intent", "weixin").any { url.startsWith("$it://") }) {
            return try {
                val intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                GlobalModule.activity.invoke()?.startActivity(intent)
                true
            } catch (e: Exception) {
                Log.e("IntentTools", "startActivity: $e")
                false
            } catch (e: ActivityNotFoundException) {
                false
            }
        }
        return false
    }

    fun launchAppWithPackageNameAndClassName(
        context: Context,
        packageName: String,
        className: String
    ) {
        val intent = Intent().apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        intent.setClassName(packageName, className)
        if (intent.resolveActivity(context.packageManager) != null) {
            context.startActivity(intent)
        } else {
            Toast.makeText(context, "无法启动应用", Toast.LENGTH_SHORT).show()
        }
    }
}
package com.tinypretty.component

import android.content.pm.ApplicationInfo
import android.widget.Toast
import androidx.activity.ComponentActivity

object GlobalModule {
    val mApp by KoinTools.injectApplication()
    val mAdFactory by KoinTools.injectAdFactory()
    val mL by  KoinTools.injectLog("MandiLog")
    fun testLog(msg: String) {
        mLog.d { "log $msg" }
    }

    val mDebugUtil = object : IDebugUtil {
        override fun isDebug(): Boolean {
            return 0 != (mApp.applicationInfo.flags and ApplicationInfo.FLAG_DEBUGGABLE)
        }

        override fun getTestDeviceId(): String {
            return ""
        }
    }

    private val mLog by KoinTools.injectLog("MandiLog")

    fun newLog(tag: String) = mLog.setTag(tag)
    var activity: () -> ComponentActivity? = { null }
    fun toast(msg: String) {
        activity.invoke()?.let {
            it.runOnUiThread {
                Toast.makeText(it, msg, Toast.LENGTH_SHORT).show()
            }
        }
    }

    val eventLogger by KoinTools.injectEvent()
    val remoteConfig by KoinTools.injectRemoteConfigure()
}
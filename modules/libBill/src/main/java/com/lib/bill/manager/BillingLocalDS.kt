package com.lib.bill.manager

import com.lib.bill.manager.bean.SubscriptionStatus
import com.tiny.domain.ext.getValue
import com.tiny.domain.ext.saveValue
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.text.SimpleDateFormat
import java.util.*

class BillingLocalDS {

    var subscribed: Boolean
        get() = getValue("billing_manager_subscribed", false) ?: false
        set(value) = value.saveValue("billing_manager_subscribed")

    /**
     * 订阅到期时间戳（毫秒）
     */
    var subscriptionExpirationTime: Long?
        get() = getValue("billing_manager_expiration_time", 0L)?.takeIf { it > 0 }
        set(value) = (value ?: 0L).saveValue("billing_manager_expiration_time")

    private val _subscribedStateFlow: MutableStateFlow<Boolean> = MutableStateFlow(subscribed)
    val subscribedStateFlow: StateFlow<Boolean> = _subscribedStateFlow

    fun onSubscribed(expirationTime: Long? = null) {
        _subscribedStateFlow.value = true
        subscribed = true
        if (expirationTime != null) {
            subscriptionExpirationTime = expirationTime
        }
    }

    fun onExpired() {
        _subscribedStateFlow.value = false
        subscribed = false
        // 保留到期时间，用于显示上次订阅的到期时间
    }

    /**
     * 获取订阅状态详细信息
     */
    fun getSubscriptionStatus(): SubscriptionStatus {
        val isSubscribed = subscribed
        val expirationTimestamp = subscriptionExpirationTime
        val expirationDateString = expirationTimestamp?.let { timestamp ->
            try {
                val dateFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())
                dateFormat.format(Date(timestamp))
            } catch (e: Exception) {
                null
            }
        }

        return SubscriptionStatus(
            isSubscribed = isSubscribed,
            expirationTimestamp = expirationTimestamp,
            expirationDateString = expirationDateString
        )
    }
}
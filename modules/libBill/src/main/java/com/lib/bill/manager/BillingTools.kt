package com.lib.bill.manager

import com.tinypretty.component.GlobalModule
import java.time.Instant
import java.time.LocalDate
import java.time.Period
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.time.temporal.ChronoUnit
import java.util.Locale

object BillingTools {
    val log = GlobalModule.newLog("BillingTools")

    fun calculateExpiryDate(startDate: LocalDate, billingPeriod: String): LocalDate {
        // 解析订阅的周期
        val period = Period.parse(billingPeriod)

        // 计算订阅的到期日期
        val endDate = startDate.plus(period)

        return endDate
    }

    fun formatLocalDate(date: LocalDate, locale: Locale): String {
        val formatter = DateTimeFormatter.ofLocalizedDate(FormatStyle.MEDIUM).withLocale(locale)
        return date.format(formatter)
    }

    fun getStartEndTime(billingPeriod: String): Pair<String, String> {
        val startDate = formatLocalDate(LocalDate.now(), Locale.getDefault()) // 假设订阅开始的日期是今天
        val endDate = formatLocalDate(calculateExpiryDate(LocalDate.now(), billingPeriod), Locale.getDefault())
        return Pair(startDate, endDate)
    }


    fun getEndTimeMillis(startTimestamp: Long, billingPeriod: String): Long {
        // 将开始的时间戳转换为Instant对象
        val startInstant = Instant.ofEpochMilli(startTimestamp)

        // 解析订阅的周期
        val period = Period.parse(billingPeriod)

        // 计算订阅的到期日期
        val endInstant = startInstant.plus(period.get(ChronoUnit.DAYS), ChronoUnit.DAYS)

        // 将结束的Instant对象转换为时间戳
        val endTimestamp = endInstant.toEpochMilli()

        return endTimestamp
    }

    fun test() {
        val startDate = LocalDate.now() // 假设订阅开始的日期是今天
        val billingPeriods = listOf("P1W", "P1M", "P3M", "P6M", "P1Y") // 订阅的周期

        billingPeriods.forEach { billingPeriod ->
            val endDate = calculateExpiryDate(startDate, billingPeriod)
            log.i { "订阅周期为 $billingPeriod, 到期日期为 ${formatLocalDate(endDate, Locale.getDefault())}" }
        }
    }
}
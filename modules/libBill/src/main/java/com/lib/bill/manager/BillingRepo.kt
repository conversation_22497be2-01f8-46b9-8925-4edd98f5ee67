package com.lib.bill.manager

import CoroutineTask
import android.app.Activity
import com.android.billingclient.api.ProductDetails
import com.lib.bill.manager.bean.SubscriptionStatus
import kotlinx.coroutines.flow.StateFlow

/**
 * <AUTHOR>
 * @Since 2024/04/20
 */
object BillingRepo {
    private var mBillRemoteDS: BillingRemoteDS? = null
    private var mBillLocalDS: BillingLocalDS = BillingLocalDS()

    var subscribedStateFlow: StateFlow<Boolean> = mBillLocalDS.subscribedStateFlow

    // 必须调用
    fun init(activity: Activity) = apply {
        mBillRemoteDS = BillingRemoteDS(activity)
        // 启动时同步网络时间
        CoroutineTask("syncNetworkTime").io().launch {
            NetworkTimeManager.syncNetworkTime()
        }
    }

    fun isSubscribed(): Boolean = mBillLocalDS.subscribed

    fun onSubscribed(expirationTime: Long? = null) = mBillLocalDS.onSubscribed(expirationTime)

    fun onExpired() = mBillLocalDS.onExpired()

    fun checkUserInSubscriptionPeriod(from: String) = mBillRemoteDS?.checkUserInSubscriptionPeriod(from)

    suspend fun queryProductDetails(from: String, productIdList: List<String>) = mBillRemoteDS?.queryProductDetails(from, productIdList) ?: emptyList()

    suspend fun purchase(from: String, productDetails: ProductDetails, offer: ProductDetails.SubscriptionOfferDetails?, activity: Activity) = mBillRemoteDS?.purchase(from, productDetails, offer, activity)

    /**
     * 获取订阅状态详细信息
     * @return SubscriptionStatus 包含订阅状态、到期时间戳、本地日期格式字符串
     */
    fun getSubscriptionStatus(): SubscriptionStatus = mBillLocalDS.getSubscriptionStatus()

    // =============================== debug part ===============================
    fun onSubscribedByDebug() = mBillLocalDS.onSubscribed()
}
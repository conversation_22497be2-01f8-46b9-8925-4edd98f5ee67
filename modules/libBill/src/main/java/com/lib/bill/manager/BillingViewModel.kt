package com.lib.bill.manager

import CoroutineTask
import android.app.Activity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.android.billingclient.api.ProductDetails
import com.lib.bill.manager.bean.AppProductDetails
import com.tiny.domain.ext.logEvent
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.GlobalModule.mDebugUtil
import com.tinypretty.component.GlobalModule.newLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import task


sealed class BillState {
    data object Idle : BillState()
    data object Loading : BillState()
    data class ProductsLoadSucceed(val products: List<AppProductDetails>) : BillState()
    data class ProductsLoadFailed(val msg: String) : BillState()
}

sealed class BillIntent {
    data class LoadProducts(val from: String) : BillIntent()
    data class PurchaseClick(val from: String, val product: ProductDetails?, val offer: ProductDetails.SubscriptionOfferDetails?) : BillIntent()
    data class CheckUserInSubscriptionPeriod(val from: String) : BillIntent()
}

/**
 * <AUTHOR>
 * @Since 2024/02/25
 */
class BillingViewModel(produceID: String = "remove_ad") : ViewModel() {
    private val log = newLog("BillingViewModel")
    private val mProduceIDList = listOf(produceID)
    private val _productDetails = mutableListOf<AppProductDetails>()
    private val _state = MutableStateFlow<BillState>(BillState.Idle)
    val state: StateFlow<BillState> = _state
    var billingPeriod = ""

    init {
        BillEvent.SUBSCRIPTION_INIT_STATUS.logEvent(BillKey.SUBSCRIBED to BillingRepo.subscribedStateFlow.value)
    }

    fun process(intent: BillIntent) {
        when (intent) {
            is BillIntent.CheckUserInSubscriptionPeriod -> {
                runCatching {
                    BillingRepo.checkUserInSubscriptionPeriod(intent.from)
                }
            }

            is BillIntent.LoadProducts -> {
                CoroutineTask("LoadProducts").io().launch {
                    loadProductList(intent.from)
                    BillEvent.SUBSCRIPTION_PURCHASE_SCREEN_SHOW.logEvent(BillKey.FROM to intent.from, BillKey.SUBSCRIBED to BillingRepo.isSubscribed(), BillKey.OFFER_COUNT to _productDetails.size)
                }
            }

            is BillIntent.PurchaseClick -> {
                BillEvent.SUBSCRIPTION_PURCHASE_OFFER_CLICK.logEvent(BillKey.FROM to intent.from, BillKey.SUBSCRIBED to BillingRepo.isSubscribed(), BillKey.OFFER_BILLING_PERIOD to intent.offer?.pricingPhases?.pricingPhaseList?.first()?.billingPeriod)
                purchase(intent.from, GlobalModule.activity.invoke(), intent.product, intent.offer)
            }
        }
    }

    private suspend fun loadProductList(from: String) {
        if (_state.value == BillState.Loading) {
            return
        }

        if (_productDetails.isNotEmpty()) {
            _state.value = BillState.ProductsLoadSucceed(_productDetails)
            return
        }

        _state.value = BillState.Loading
        _productDetails.addAll(BillingRepo.queryProductDetails(from, mProduceIDList).map { AppProductDetails(System.currentTimeMillis(), it) })

        if (_productDetails.isEmpty() && mDebugUtil.isDebug()) {
            _productDetails.addAll(List(5) { AppProductDetails(System.currentTimeMillis(), null) })
        }
        if (_productDetails.isEmpty()) {
            _state.value = BillState.ProductsLoadFailed("list is empty")
        } else {
            _state.value = BillState.ProductsLoadSucceed(_productDetails)
        }
    }

    private fun purchase(from: String, activity: Activity?, product: ProductDetails?, offer: ProductDetails.SubscriptionOfferDetails?) {
        if (activity == null) return
        log.i { "billingManager.buyProduct $mProduceIDList" }
        viewModelScope.task("buyProduct").io().launch {
            if (product == null) {
                delay(2000)
                BillingRepo.onSubscribedByDebug()
                BillEvent.SUBSCRIPTION_PURCHASE_OFFER_END_SUCCEED.logEvent(Pair(BillKey.FROM, "${from}_test_buy"), Pair(BillKey.VALUE, BillValue.SUBSCRIPTION_RESULT_SUBSCRIBED), BillKey.SUBSCRIBED to BillingRepo.isSubscribed())
            } else {
                BillingRepo.purchase(from, product, offer, activity)
            }
        }
    }
}
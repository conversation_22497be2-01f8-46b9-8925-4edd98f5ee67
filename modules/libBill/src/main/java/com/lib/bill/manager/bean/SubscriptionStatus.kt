package com.lib.bill.manager.bean

/**
 * 订阅状态信息
 * <AUTHOR>
 * @Since 2024/12/19
 */
data class SubscriptionStatus(
    /**
     * 是否已订阅
     */
    val isSubscribed: Boolean,
    
    /**
     * 订阅到期时间戳（毫秒）
     * 如果未订阅或无法获取到期时间，则为null
     */
    val expirationTimestamp: Long?,
    
    /**
     * 订阅到期时间的本地日期格式字符串（精确到年月日）
     * 格式：yyyy年MM月dd日
     * 如果未订阅或无法获取到期时间，则为null
     */
    val expirationDateString: String?
)

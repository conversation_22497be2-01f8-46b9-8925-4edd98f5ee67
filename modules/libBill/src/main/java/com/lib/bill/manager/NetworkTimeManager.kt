package com.lib.bill.manager

import com.tiny.domain.ext.getValue
import com.tiny.domain.ext.logEvent
import com.tiny.domain.ext.saveValue
import com.tinypretty.component.GlobalModule.newLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*

/**
 * 网络时间管理器
 * 提供多个备份方案获取网络时间，提高成功率
 * <AUTHOR>
 * @Since 2024/12/19
 */
object NetworkTimeManager {
    private val log = newLog("NetworkTimeManager")

    // 时间校正值存储key
    private const val TIME_OFFSET_KEY = "network_time_offset"
    private const val LAST_SYNC_TIME_KEY = "last_network_sync_time"

    // 网络时间API列表（按优先级排序）
    private val timeApis = listOf(
        TimeApi("http://worldtimeapi.org/api/ip", TimeApiType.WORLD_TIME_API),
        TimeApi("http://worldtimeapi.org/api/timezone/Etc/UTC", TimeApiType.WORLD_TIME_API),
        TimeApi("https://timeapi.io/api/Time/current/zone?timeZone=UTC", TimeApiType.TIME_API_IO),
        TimeApi("http://worldclockapi.com/api/json/utc/now", TimeApiType.WORLD_CLOCK_API),
        TimeApi("https://api.timezonedb.com/v2.1/get-time-zone?key=demo&format=json&by=zone&zone=UTC", TimeApiType.TIMEZONE_DB)
    )

    /**
     * 获取网络时间校正值（网络时间 - 本地时间）
     */
    private var timeOffset: Long
        get() = getValue(TIME_OFFSET_KEY, 0L) ?: 0L
        set(value) = value.saveValue(TIME_OFFSET_KEY)

    /**
     * 上次同步网络时间的时间戳
     */
    private var lastSyncTime: Long
        get() = getValue(LAST_SYNC_TIME_KEY, 0L) ?: 0L
        set(value) = value.saveValue(LAST_SYNC_TIME_KEY)

    /**
     * 获取当前时间（优先使用校正后的时间）
     * @return 当前时间戳（毫秒）
     */
    fun getCurrentTime(): Long {
        val localTime = System.currentTimeMillis()
        return if (timeOffset != 0L) {
            localTime + timeOffset
        } else {
            localTime
        }
    }

    /**
     * 同步网络时间（应用启动时调用一次）
     * @return 是否同步成功
     */
    suspend fun syncNetworkTime(): Boolean = withContext(Dispatchers.IO) {
        log.i { "开始同步网络时间..." }
        BillEvent.NETWORK_TIME_SYNC_START.logEvent()

        val startTime = System.currentTimeMillis()
        var syncSuccess = false

        try {
            // 并发请求多个时间API，提高成功率
            val networkTime = getNetworkTimeWithFallback()

            if (networkTime != null) {
                val localTime = System.currentTimeMillis()
                val offset = networkTime - localTime

                timeOffset = offset
                lastSyncTime = localTime
                syncSuccess = true

                log.i { "网络时间同步成功 - 网络时间: ${formatTime(networkTime)}, 本地时间: ${formatTime(localTime)}, 校正值: ${offset}ms" }
                BillEvent.NETWORK_TIME_SYNC_SUCCESS.logEvent(
                    BillKey.NETWORK_TIME to networkTime,
                    BillKey.LOCAL_TIME to localTime,
                    BillKey.TIME_OFFSET to offset,
                    BillKey.COST to (System.currentTimeMillis() - startTime)
                )
            } else {
                log.e { "网络时间同步失败 - 所有API都无法获取时间" }
                BillEvent.NETWORK_TIME_SYNC_FAILED.logEvent(
                    BillKey.ERROR_MSG to "All APIs failed",
                    BillKey.COST to (System.currentTimeMillis() - startTime)
                )
            }
        } catch (e: Exception) {
            log.e { "网络时间同步异常: ${e.message}" }
            BillEvent.NETWORK_TIME_SYNC_ERROR.logEvent(
                BillKey.ERROR_MSG to e.message,
                BillKey.COST to (System.currentTimeMillis() - startTime)
            )
        }

        syncSuccess
    }

    /**
     * 使用多个备份方案获取网络时间
     */
    private suspend fun getNetworkTimeWithFallback(): Long? = withContext(Dispatchers.IO) {
        // 并发请求前3个API
        val deferredResults = timeApis.take(3).map { api ->
            async {
                withTimeoutOrNull(5000) {
                    getTimeFromApi(api)
                }
            }
        }

        // 等待第一个成功的结果
        for (deferred in deferredResults) {
            try {
                val result = deferred.await()
                if (result != null) {
                    log.i { "网络时间获取成功，使用API: ${result.first.url}" }
                    return@withContext result.second
                }
            } catch (e: Exception) {
                log.e { "网络时间API请求异常: ${e.message}" }
            }
        }

        // 如果前3个都失败，尝试剩余的API
        for (api in timeApis.drop(3)) {
            try {
                val result = withTimeoutOrNull(8000) {
                    getTimeFromApi(api)
                }
                if (result != null) {
                    log.i { "网络时间获取成功（备用API）: ${result.first.url}" }
                    return@withContext result.second
                }
            } catch (e: Exception) {
                log.e { "备用网络时间API请求异常: ${e.message}" }
            }
        }

        null
    }

    /**
     * 从指定API获取时间
     */
    private suspend fun getTimeFromApi(api: TimeApi): Pair<TimeApi, Long>? = withContext(Dispatchers.IO) {
        try {
            log.d { "尝试从API获取时间: ${api.url}" }
            val url = URL(api.url)
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 5000
            connection.readTimeout = 5000
            connection.setRequestProperty("User-Agent", "Android-App-TimeSync/1.0")

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val reader = BufferedReader(InputStreamReader(connection.inputStream))
                val response = reader.readText()
                reader.close()

                val timestamp = parseTimeResponse(response, api.type)
                if (timestamp != null) {
                    log.d { "API ${api.url} 返回时间: ${formatTime(timestamp)}" }
                    return@withContext Pair(api, timestamp)
                }
            } else {
                log.e { "API ${api.url} 返回错误码: $responseCode" }
            }

            connection.disconnect()
        } catch (e: Exception) {
            log.e { "API ${api.url} 请求失败: ${e.message}" }
        }

        null
    }

    /**
     * 解析不同API的时间响应
     */
    private fun parseTimeResponse(response: String, type: TimeApiType): Long? {
        return try {
            when (type) {
                TimeApiType.WORLD_TIME_API -> {
                    val json = JSONObject(response)
                    json.getLong("unixtime") * 1000
                }
                TimeApiType.TIME_API_IO -> {
                    val json = JSONObject(response)
                    val dateTimeStr = json.getString("dateTime")
                    // 解析ISO 8601格式时间
                    SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US).parse(dateTimeStr)?.time
                }
                TimeApiType.WORLD_CLOCK_API -> {
                    val json = JSONObject(response)
                    val dateTimeStr = json.getString("currentDateTime")
                    SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'", Locale.US).parse(dateTimeStr)?.time
                }
                TimeApiType.TIMEZONE_DB -> {
                    val json = JSONObject(response)
                    json.getLong("timestamp") * 1000
                }
            }
        } catch (e: Exception) {
            log.e { "解析时间响应失败: $response" }
            null
        }
    }

    /**
     * 格式化时间戳为可读字符串
     */
    private fun formatTime(timestamp: Long): String {
        return SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date(timestamp))
    }

    /**
     * 时间API配置
     */
    private data class TimeApi(
        val url: String,
        val type: TimeApiType
    )

    /**
     * 时间API类型
     */
    private enum class TimeApiType {
        WORLD_TIME_API,
        TIME_API_IO,
        WORLD_CLOCK_API,
        TIMEZONE_DB
    }
}

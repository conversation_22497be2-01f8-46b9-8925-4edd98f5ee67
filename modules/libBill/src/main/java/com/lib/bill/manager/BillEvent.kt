package com.lib.bill.manager

/**
 * <AUTHOR>
 * @Since 2024/04/20
 */
object BillEvent {
    // 启动时的订阅状态
    const val SUBSCRIPTION_INIT_STATUS = "subscription_init_result"

    // 订购购买打点
    const val SUBSCRIPTION_PURCHASE_SCREEN_SHOW = "subscription_purchase_show"
    const val SUBSCRIPTION_PURCHASE_OFFER_CLICK = "subscription_purchase_offer_click"
    const val SUBSCRIPTION_PURCHASE_OFFER_END_SUCCEED = "subscription_purchase_offer_end_succeed"
    const val SUBSCRIPTION_PURCHASE_OFFER_END_PENDING = "subscription_purchase_offer_end_pending"
    const val SUBSCRIPTION_PURCHASE_OFFER_END_UNKNOWN = "subscription_purchase_offer_end_unknown"

    const val SUBSCRIPTION_PURCHASE_ACKNOWLEDGED_SUCCEED = "subscription_purchase_acknowledged_succeed"
    const val SUBSCRIPTION_PURCHASE_ACKNOWLEDGED_FAILED = "subscription_purchase_acknowledged_failed"

    // 订阅状态检查打点
    const val SUBSCRIPTION_CHECK_SUBSCRIBED = "subscription_check_subscribed"
    const val SUBSCRIPTION_CHECK_UN_SUBSCRIBED = "subscription_check_un_subscribed"

    const val SUBSCRIPTION_CHECK_SUBSCRIBED_IN_PERIOD = "subscription_check_subscribed_in_period"
    const val SUBSCRIPTION_CHECK_UN_SUBSCRIBED_IN_PERIOD = "subscription_check_un_subscribed_in_period"
    const val SUBSCRIPTION_CHECK_GET_PERIOD_END_TIME = "subscription_check_get_period_end_time"

    const val SUBSCRIPTION_CHECK_SUBSCRIBED_EXPIRE_NOT_AUTO_RENEWING = "subscription_check_subscribed_expire_not_auto_renewing"
    const val SUBSCRIPTION_CHECK_AUTO_RENEWING_ = "subscription_check_auto_renewing_"
    const val SUBSCRIPTION_CHECK_BILLING_PERIOD_ = "subscription_check_billing_period_"

    // 订阅CONNECT
    const val SUBSCRIPTION_CONNECT_START = "subscription_connect_start"
    const val SUBSCRIPTION_CONNECT_END = "subscription_connect_end"
    const val SUBSCRIPTION_CONNECT_DISCONNECT = "subscription_connect_disconnect"

    // 订阅QUERY
    const val SUBSCRIPTION_QUERY_START = "subscription_query_start"
    const val SUBSCRIPTION_QUERY_END = "subscription_query_end"

    // 网络时间同步
    const val NETWORK_TIME_SYNC_START = "network_time_sync_start"
    const val NETWORK_TIME_SYNC_SUCCESS = "network_time_sync_success"
    const val NETWORK_TIME_SYNC_FAILED = "network_time_sync_failed"
    const val NETWORK_TIME_SYNC_ERROR = "network_time_sync_error"

    // 订阅状态检查详细流程
    const val SUBSCRIPTION_CHECK_START = "subscription_check_start"
    const val SUBSCRIPTION_CHECK_QUERY_SUCCESS = "subscription_check_query_success"
    const val SUBSCRIPTION_CHECK_QUERY_FAILED = "subscription_check_query_failed"
    const val SUBSCRIPTION_CHECK_PRODUCTS_EMPTY = "subscription_check_products_empty"
    const val SUBSCRIPTION_CHECK_AUTO_RENEWING_TRUE = "subscription_check_auto_renewing_true"
    const val SUBSCRIPTION_CHECK_AUTO_RENEWING_FALSE = "subscription_check_auto_renewing_false"
    const val SUBSCRIPTION_CHECK_NETWORK_TIME_SUCCESS = "subscription_check_network_time_success"
    const val SUBSCRIPTION_CHECK_NETWORK_TIME_FAILED = "subscription_check_network_time_failed"
    const val SUBSCRIPTION_CHECK_USE_LOCAL_TIME = "subscription_check_use_local_time"
    const val SUBSCRIPTION_CHECK_STILL_VALID = "subscription_check_still_valid"
    const val SUBSCRIPTION_CHECK_EXPIRED = "subscription_check_expired"
    const val SUBSCRIPTION_CHECK_KEEP_PREVIOUS_STATUS = "subscription_check_keep_previous_status"
}

object BillKey {
    const val FROM = "from"
    const val VALUE = "value"
    const val COUNT = "count"
    const val COST = "cost"
    const val ERROR_MSG = "error_msg"
    const val PRODUCT_ID = "product_id"
    const val SUBSCRIBED = "subscribed"
    const val RESPONSE_CODE = "response_code"
    const val OFFER_COUNT = "offer_count"
    const val OFFER_BILLING_PERIOD = "offer_billing_period"

    // 网络时间相关
    const val NETWORK_TIME = "network_time"
    const val LOCAL_TIME = "local_time"
    const val TIME_OFFSET = "time_offset"

    // 订阅状态检查相关
    const val CURRENT_TIME = "current_time"
    const val EXPIRATION_TIME = "expiration_time"
    const val TIME_DIFFERENCE = "time_difference"
    const val AUTO_RENEWING = "auto_renewing"
    const val PURCHASE_COUNT = "purchase_count"
}

object BillValue {
    const val SUBSCRIPTION_RESULT_SUBSCRIBED = "subscribed"
    const val SUBSCRIPTION_RESULT_NOT_SUBSCRIBED = "not_subscribed"
    const val SUBSCRIPTION_RESULT_PENDING = "pending"
    const val SUBSCRIPTION_RESULT_UNSPECIFIED_STATE = "unspecified_state"
    const val SUBSCRIPTION_RESULT_EMPTY_LIST = "empty_list"
}
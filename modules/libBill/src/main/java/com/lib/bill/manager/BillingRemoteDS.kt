package com.lib.bill.manager

/**
 * <AUTHOR>
 * @Since 2024/02/25
 */

import CoroutineTask
import android.app.Activity
import com.android.billingclient.api.AcknowledgePurchaseParams
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams
import com.android.billingclient.api.queryProductDetails
import com.android.billingclient.api.queryPurchasesAsync
import com.tiny.domain.ext.logEvent
import com.tiny.domain.ext.rValue
import com.tiny.domain.ext.safeResume
import com.tiny.domain.util.DeviceUtil
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.configure.ConfigureKeys
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext

class BillingRemoteDS(activity: Activity) {
    private var isChecking = false
    private var mPurchaseFrom = ""
    private var mCheckSubsStatusWithProductPeriod = false
    private val purchasesUpdatedListener =
        PurchasesUpdatedListener { billingResult, purchases ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
                handlePurchase(mPurchaseFrom, purchases)
            }
        }
    private var billingClient = BillingClient.newBuilder(activity)
        .setListener(purchasesUpdatedListener)
        .enablePendingPurchases()
        .build()
    private val allProducts = hashMapOf<String, ProductDetails>()

    companion object {
        private val log = newLog("BillingManager")
    }

    suspend fun queryProductDetails(from: String, productId: List<String>): List<ProductDetails> {
        BillEvent.SUBSCRIPTION_QUERY_START.logEvent(BillKey.PRODUCT_ID to productId.joinToString(","), BillKey.FROM to from)

        ensureConnected()
        val queryProductDetailsParams =
            QueryProductDetailsParams.newBuilder()
                .setProductList(
                    productId.map {
                        QueryProductDetailsParams.Product.newBuilder()
                            .setProductId(it)
                            .setProductType(BillingClient.ProductType.SUBS)
                            .build()
                    }.toMutableList().apply {
                        log.i { "queryProductDetails list=${this.size}" }
                    }
                )
                .build()
        val startTime = System.currentTimeMillis()
        val productDetailsResult = withContext(Dispatchers.IO) {
            billingClient.queryProductDetails(queryProductDetailsParams)
        }

        val costTime = System.currentTimeMillis() - startTime
        val result = productDetailsResult.productDetailsList ?: emptyList()
        log.i { "queryProductDetails result=$result,debugMessage=${productDetailsResult.billingResult.debugMessage},responseCode=${productDetailsResult.billingResult.responseCode}" }
        BillEvent.SUBSCRIPTION_QUERY_END.logEvent(
            BillKey.PRODUCT_ID to productId.joinToString(","),
            BillKey.FROM to from,
            BillKey.COST to costTime,
            BillKey.COUNT to result.size,
            BillKey.ERROR_MSG to productDetailsResult.billingResult.debugMessage
        )

        result.forEach {
            allProducts[it.productId] = it
        }
        return result
    }

    suspend fun purchase(from: String, productDetails: ProductDetails, offer: ProductDetails.SubscriptionOfferDetails?, activity: Activity) {
        mPurchaseFrom = from
        log.i { "buy start ${productDetails.subscriptionOfferDetails?.size}" }
        ensureConnected()
        withContext(Dispatchers.Main) {
            val productDetailsParamsList = listOf(
                BillingFlowParams.ProductDetailsParams.newBuilder()
                    // retrieve a value for "productDetails" by calling queryProductDetailsAsync()
                    .setProductDetails(productDetails)
                    // For One-time product, "setOfferToken" method shouldn't be called.
                    // For subscriptions, to get an offer token, call ProductDetails.subscriptionOfferDetails()
                    // for a list of offers that are available to the user
                    .setOfferToken(offer?.offerToken ?: "")
                    .build()
            )

            val billingFlowParams = BillingFlowParams.newBuilder()
                .setProductDetailsParamsList(productDetailsParamsList)
                .build()
            billingClient.launchBillingFlow(activity, billingFlowParams)
            log.i { "buy end" }
        }
    }

    fun checkUserInSubscriptionPeriod(checkFrom: String) {
        if (isChecking) {
            log.i { "订阅状态检查正在进行中，跳过本次检查 - from: $checkFrom" }
            return
        }

        CoroutineTask("checkUserInSubscriptionPeriod").io().launch {
            isChecking = true
            log.i { "开始订阅状态检查 - from: $checkFrom" }
            BillEvent.SUBSCRIPTION_CHECK_START.logEvent(BillKey.FROM to checkFrom)

            try {
                // 步骤1: 调用 BillingClient 获取订阅列表
                ensureConnected()
                val purchasesResult = withContext(Dispatchers.IO) {
                    val params = QueryPurchasesParams.newBuilder()
                        .setProductType(BillingClient.ProductType.SUBS)
                    billingClient.queryPurchasesAsync(params.build())
                }

                // 步骤2: 检查订阅查询是否成功
                if (purchasesResult.billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    log.i { "订阅查询成功 - from: $checkFrom, 订阅数量: ${purchasesResult.purchasesList.size}" }
                    BillEvent.SUBSCRIPTION_CHECK_QUERY_SUCCESS.logEvent(
                        BillKey.FROM to checkFrom,
                        BillKey.PURCHASE_COUNT to purchasesResult.purchasesList.size
                    )
                    checkPurchaseWithNewLogic(from = checkFrom, purchasesResult.purchasesList)
                } else {
                    log.e { "订阅查询失败 - from: $checkFrom, responseCode: ${purchasesResult.billingResult.responseCode}, debugMessage: ${purchasesResult.billingResult.debugMessage}" }
                    BillEvent.SUBSCRIPTION_CHECK_QUERY_FAILED.logEvent(
                        BillKey.FROM to checkFrom,
                        BillKey.RESPONSE_CODE to purchasesResult.billingResult.responseCode,
                        BillKey.ERROR_MSG to purchasesResult.billingResult.debugMessage
                    )
                    // 查询失败，保留上次订阅状态
                    BillEvent.SUBSCRIPTION_CHECK_KEEP_PREVIOUS_STATUS.logEvent(BillKey.FROM to checkFrom)
                }
            } catch (e: Exception) {
                log.e { "订阅状态检查异常 - from: $checkFrom, error: ${e.message}" }
                BillEvent.SUBSCRIPTION_CHECK_QUERY_FAILED.logEvent(
                    BillKey.FROM to checkFrom,
                    BillKey.ERROR_MSG to e.message
                )
                // 异常情况，保留上次订阅状态
                BillEvent.SUBSCRIPTION_CHECK_KEEP_PREVIOUS_STATUS.logEvent(BillKey.FROM to checkFrom)
            } finally {
                isChecking = false
                log.i { "订阅状态检查结束 - from: $checkFrom" }
            }
        }
    }

    /**
     * 按照新流程图逻辑检查订阅状态
     */
    private suspend fun checkPurchaseWithNewLogic(from: String, purchaseList: List<Purchase>) {
        // 步骤3: 解析订阅数据，过滤出已购买状态的订阅
        val purchased = purchaseList.filter { it.purchaseState == Purchase.PurchaseState.PURCHASED }

        // 步骤4: 检查订阅产品是否为空
        if (purchased.isEmpty()) {
            log.i { "订阅产品为空，订阅失效 - from: $from" }
            BillEvent.SUBSCRIPTION_CHECK_PRODUCTS_EMPTY.logEvent(BillKey.FROM to from)
            BillingRepo.onExpired()
            return
        }

        log.i { "找到 ${purchased.size} 个已购买的订阅 - from: $from" }

        // 确认购买
        purchased.forEach { acknowledgePurchase(it) }

        // 步骤5: 检查是否存在 autoRenewing = true 的订阅
        val autoRenewingSubscriptions = purchased.filter { it.isAutoRenewing }
        if (autoRenewingSubscriptions.isNotEmpty()) {
            log.i { "存在自动续费订阅，订阅有效 - from: $from, 自动续费订阅数量: ${autoRenewingSubscriptions.size}" }
            BillEvent.SUBSCRIPTION_CHECK_AUTO_RENEWING_TRUE.logEvent(
                BillKey.FROM to from,
                BillKey.AUTO_RENEWING to true,
                BillKey.COUNT to autoRenewingSubscriptions.size
            )

            // 获取最晚的到期时间
            val latestExpirationTime = getLatestExpirationTime(from, purchased)
            BillingRepo.onSubscribed(latestExpirationTime)
            return
        }

        log.i { "所有订阅都不是自动续费，需要检查到期时间 - from: $from" }
        BillEvent.SUBSCRIPTION_CHECK_AUTO_RENEWING_FALSE.logEvent(BillKey.FROM to from)

        // 步骤6: 获取网络时间
        val networkTime = NetworkTimeManager.getCurrentTime()
        val isNetworkTimeAvailable = NetworkTimeManager.syncNetworkTime()

        val currentTime = if (isNetworkTimeAvailable) {
            log.i { "使用网络时间进行比较 - from: $from, 网络时间: ${formatTime(networkTime)}" }
            BillEvent.SUBSCRIPTION_CHECK_NETWORK_TIME_SUCCESS.logEvent(
                BillKey.FROM to from,
                BillKey.CURRENT_TIME to networkTime
            )
            networkTime
        } else {
            val localTime = System.currentTimeMillis()
            log.i { "网络时间获取失败，使用本地时间 - from: $from, 本地时间: ${formatTime(localTime)}" }
            BillEvent.SUBSCRIPTION_CHECK_NETWORK_TIME_FAILED.logEvent(BillKey.FROM to from)
            BillEvent.SUBSCRIPTION_CHECK_USE_LOCAL_TIME.logEvent(
                BillKey.FROM to from,
                BillKey.CURRENT_TIME to localTime
            )
            localTime
        }

        // 步骤7: 获取订阅到期时间并比较
        val latestExpirationTime = getLatestExpirationTime(from, purchased)

        if (latestExpirationTime != null) {
            val timeDifference = latestExpirationTime - currentTime
            log.i { "订阅到期时间比较 - from: $from, 当前时间: ${formatTime(currentTime)}, 到期时间: ${formatTime(latestExpirationTime)}, 时间差: ${timeDifference}ms" }

            // 步骤8: 判断当前是否仍在有效期内
            if (currentTime <= latestExpirationTime) {
                log.i { "订阅仍在有效期内 - from: $from" }
                BillEvent.SUBSCRIPTION_CHECK_STILL_VALID.logEvent(
                    BillKey.FROM to from,
                    BillKey.CURRENT_TIME to currentTime,
                    BillKey.EXPIRATION_TIME to latestExpirationTime,
                    BillKey.TIME_DIFFERENCE to timeDifference
                )
                BillingRepo.onSubscribed(latestExpirationTime)
            } else {
                log.i { "订阅已过期 - from: $from" }
                BillEvent.SUBSCRIPTION_CHECK_EXPIRED.logEvent(
                    BillKey.FROM to from,
                    BillKey.CURRENT_TIME to currentTime,
                    BillKey.EXPIRATION_TIME to latestExpirationTime,
                    BillKey.TIME_DIFFERENCE to timeDifference
                )
                BillingRepo.onExpired()
            }
        } else {
            log.e { "无法获取订阅到期时间，使用自动续费状态判断 - from: $from" }
            // 如果无法获取到期时间，根据是否有自动续费订阅来判断
            if (purchased.any { it.isAutoRenewing }) {
                BillingRepo.onSubscribed()
            } else {
                BillingRepo.onExpired()
            }
        }
    }

    private suspend fun checkPurchase(from: String, purchaseList: List<Purchase>) {
        val purchased = purchaseList.filter { it.purchaseState == Purchase.PurchaseState.PURCHASED }
        if (purchased.isEmpty()) {
            BillEvent.SUBSCRIPTION_CHECK_UN_SUBSCRIBED.logEvent(BillKey.FROM to from)
            BillingRepo.onExpired()
            return
        } else {
            BillEvent.SUBSCRIPTION_CHECK_SUBSCRIBED.logEvent(BillKey.FROM to from)
            BillingRepo.onSubscribed()
        }

        purchased.forEach {
            acknowledgePurchase(it)
        }

        val expiredTime = purchased.mapNotNull { getExpiredTimeByProductDetail(from, it) }.maxOrNull()

        if (purchased.isNotEmpty()) {
            BillEvent.SUBSCRIPTION_CHECK_GET_PERIOD_END_TIME.logEvent(BillKey.FROM to from, BillKey.VALUE to expiredTime)
        }
        // 通用方案, 根据产品的过期及时来计算订阅状态
        if (ConfigureKeys.SUBSCRIPTION_AUTO_CHECK_STATUS_WITH_PERIOD.rValue(false)) {
            val currentTime = DeviceUtil.getRemoteCurrentTime()
            if (!mCheckSubsStatusWithProductPeriod && expiredTime != null && currentTime != null) {
                mCheckSubsStatusWithProductPeriod = true
                if (expiredTime <= currentTime) {
                    BillingRepo.onExpired()
                    BillEvent.SUBSCRIPTION_CHECK_UN_SUBSCRIBED_IN_PERIOD.logEvent(BillKey.FROM to from)
                } else {
                    BillingRepo.onSubscribed()
                    BillEvent.SUBSCRIPTION_CHECK_SUBSCRIBED_IN_PERIOD.logEvent(BillKey.FROM to from)
                }
                return
            }
        } else {// todo by shawn delete later
            if (purchased.isNotEmpty()) {
                (BillEvent.SUBSCRIPTION_CHECK_GET_PERIOD_END_TIME + "_" + expiredTime).logEvent(BillKey.FROM to from, BillKey.VALUE to expiredTime)
            }
        }

        // 兜底方案 根据 autoRenewing 判断是订阅过期
        val existAutoAutoRenewing: Boolean = purchased.any { it.isAutoRenewing }
        val purchaseTime = purchased.maxOfOrNull { it.purchaseTime } ?: 0L
        "${BillEvent.SUBSCRIPTION_CHECK_AUTO_RENEWING_}${existAutoAutoRenewing}".logEvent(BillKey.FROM to from)
        if (purchaseTime > 1715961600000) {
            if (!existAutoAutoRenewing) {
                BillEvent.SUBSCRIPTION_CHECK_SUBSCRIBED_EXPIRE_NOT_AUTO_RENEWING.logEvent(BillKey.FROM to from)
                BillingRepo.onExpired()
            }
        } else {
            (BillEvent.SUBSCRIPTION_CHECK_SUBSCRIBED_EXPIRE_NOT_AUTO_RENEWING + "_ERROR_TIME").logEvent(BillKey.FROM to from)
        }
    }

    private fun handlePurchase(from: String, purchaseList: List<Purchase>) {
        CoroutineTask("handlePurchase").io().launch {
            purchaseList.forEach { purchase ->
                when (purchase.purchaseState) {
                    Purchase.PurchaseState.PURCHASED -> {
                        // 获取购买的订阅到期时间
                        val expirationTime = getExpiredTimeByProductDetail(from, purchase)
                        BillingRepo.onSubscribed(expirationTime)
                        BillEvent.SUBSCRIPTION_PURCHASE_OFFER_END_SUCCEED.logEvent(
                            BillKey.FROM to from,
                            BillKey.EXPIRATION_TIME to expirationTime
                        )
                        acknowledgePurchase(purchase)
                    }

                    Purchase.PurchaseState.PENDING -> {
                        BillEvent.SUBSCRIPTION_PURCHASE_OFFER_END_PENDING
                            .logEvent(
                                BillKey.FROM to from,
                                BillKey.VALUE to BillValue.SUBSCRIPTION_RESULT_PENDING
                            )
                    }

                    Purchase.PurchaseState.UNSPECIFIED_STATE -> {
                        BillEvent.SUBSCRIPTION_PURCHASE_OFFER_END_UNKNOWN
                            .logEvent(
                                BillKey.FROM to from,
                                BillKey.VALUE to BillValue.SUBSCRIPTION_RESULT_UNSPECIFIED_STATE
                            )
                    }
                }
            }
        }
    }

    /**
     * 获取最晚的订阅到期时间
     */
    private fun getLatestExpirationTime(from: String, purchaseList: List<Purchase>): Long? {
        val expirationTimes = purchaseList.mapNotNull { getExpiredTimeByProductDetail(from, it) }
        return expirationTimes.maxOrNull()
    }

    /**
     * 格式化时间戳为可读字符串
     */
    private fun formatTime(timestamp: Long): String {
        return try {
            java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(timestamp))
        } catch (e: Exception) {
            timestamp.toString()
        }
    }

    private fun getExpiredTimeByProductDetail(from: String, purchase: Purchase): Long? {
        val purchaseProductID = purchase.products.firstOrNull() ?: return null
        val purchaseTime = purchase.purchaseTime
        val productDetails = allProducts[purchaseProductID] ?: return null

        val billingPeriodList = productDetails.subscriptionOfferDetails?.mapNotNull { it.pricingPhases.pricingPhaseList.first() }?.map { it.billingPeriod } ?: return null
        val days = billingPeriodList.map { bp -> bp to BillingTools.getEndTimeMillis(purchaseTime, bp) }.maxBy { it.second }
        val billingPeriodAll = billingPeriodList.joinToString("_")
        (BillEvent.SUBSCRIPTION_CHECK_BILLING_PERIOD_ + billingPeriodAll).logEvent(
            BillKey.FROM to from,
            BillKey.VALUE to days.first + "_" + days.second,
        )
        return days.second
    }

    private suspend fun ensureConnected() {
        log.i { "ensureConnected -> billingClient.isReady = ${billingClient.isReady}" }
        if (!billingClient.isReady) {
            BillEvent.SUBSCRIPTION_CONNECT_START.logEvent()
            val startTime = System.currentTimeMillis()
            suspendCancellableCoroutine { continuation ->
                billingClient.startConnection(object : BillingClientStateListener {
                    override fun onBillingSetupFinished(billingResult: BillingResult) {
                        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                            continuation.safeResume(Unit)
                        } else {
                            continuation.safeResume(Unit)
                        }
                        val cost = System.currentTimeMillis() - startTime
                        BillEvent.SUBSCRIPTION_CONNECT_END.logEvent(
                            BillKey.RESPONSE_CODE to billingResult.responseCode,
                            BillKey.COST to cost
                        )
                    }

                    override fun onBillingServiceDisconnected() {
                        continuation.safeResume(Unit)
                        BillEvent.SUBSCRIPTION_CONNECT_DISCONNECT.logEvent()
                    }
                })
            }
        }
    }

    private suspend fun acknowledgePurchase(purchase: Purchase) = withContext(Dispatchers.IO) {
        if (purchase.purchaseState != Purchase.PurchaseState.PURCHASED) {
            return@withContext
        }
        if (purchase.isAcknowledged) {
            return@withContext
        }

        ensureConnected()

        val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder().setPurchaseToken(purchase.purchaseToken)

        billingClient.acknowledgePurchase(acknowledgePurchaseParams.build()) { billingResult ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                BillEvent.SUBSCRIPTION_PURCHASE_ACKNOWLEDGED_SUCCEED.logEvent(
                    BillKey.VALUE to billingResult.responseCode
                )
            } else {
                BillEvent.SUBSCRIPTION_PURCHASE_ACKNOWLEDGED_FAILED.logEvent(
                    BillKey.VALUE to billingResult.responseCode
                )
            }

        }
    }
}
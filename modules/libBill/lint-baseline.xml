<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.1.2" type="baseline" client="gradle" dependencies="true" name="AGP (8.1.2)" variant="all" version="8.1.2">

    <issue
        id="IntentReset"
        message="Calling `setType` after calling `setData` will clear the data: Call `setDataAndType` instead?"
        errorLine1="                    it.type = getMimeType(filePath)"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/tinypretty/component/IntentTools.kt"
            line="197"
            column="24"/>
        <location
            file="src/main/java/com/tinypretty/component/IntentTools.kt"
            line="195"
            column="21"
            message="Originally set here"/>
    </issue>

    <issue
        id="QueryPermissionsNeeded"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details"
        errorLine1="        val handlers = pm.queryIntentActivities("
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/tinypretty/component/IntentTools.kt"
            line="401"
            column="27"/>
    </issue>

    <issue
        id="QueryPermissionsNeeded"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details"
        errorLine1="        val list = mgr.queryIntentActivities("
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/tinypretty/component/IntentTools.kt"
            line="435"
            column="24"/>
    </issue>

    <issue
        id="QueryPermissionsNeeded"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details"
        errorLine1="        if (intent.resolveActivity(context.packageManager) != null) {"
        errorLine2="                   ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/tinypretty/component/IntentTools.kt"
            line="472"
            column="20"/>
    </issue>

    <issue
        id="QueryPermissionsNeeded"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details"
        errorLine1="            val activities = mApp.applicationContext.packageManager.queryIntentActivities("
        errorLine2="                                                                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/tiny/domain/util/MarketHelper.kt"
            line="43"
            column="69"/>
    </issue>

    <issue
        id="QueryPermissionsNeeded"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details"
        errorLine1="            val activities = context.packageManager.queryIntentActivities("
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/tiny/domain/util/MarketHelper.kt"
            line="91"
            column="53"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.android.billingclient:billing than 7.0.0 is available: 7.1.1"
        errorLine1="    api(&quot;com.android.billingclient:billing:$billingVersion&quot;)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="26"
            column="9"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.android.billingclient:billing-ktx than 7.0.0 is available: 7.1.1"
        errorLine1="    api(&quot;com.android.billingclient:billing-ktx:$billingVersion&quot;)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="27"
            column="9"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`)."
        errorLine1="        return context.resources.getIdentifier(resourceName, &quot;raw&quot;, context.packageName)"
        errorLine2="                                 ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/tiny/domain/util/DeviceUtil.kt"
            line="64"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        return TextView(context).apply { setText(&quot;Video Player No Inject&quot;) }"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/tinypretty/component/KoinDefMoudles.kt"
            line="73"
            column="51"/>
    </issue>

</issues>

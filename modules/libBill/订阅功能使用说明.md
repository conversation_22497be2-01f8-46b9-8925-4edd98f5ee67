# 订阅功能使用说明

## 概述

本次优化按照提供的流程图重构了订阅逻辑，增加了详细的日志记录、网络时间同步、订阅状态详细信息获取等功能。

## 主要改进

### 1. 网络时间同步
- 应用启动时自动同步网络时间
- 支持多个备份时间API，提高成功率
- 自动计算网络时间与本地时间的校正值
- 详细的同步日志记录

### 2. 订阅状态详细信息
- 新增 `getSubscriptionStatus()` 接口
- 返回订阅状态、到期时间戳、本地日期格式字符串
- 支持精确到年月日的日期显示

### 3. 优化的订阅检查逻辑
按照流程图实现的新逻辑：
1. 启动 App
2. 调用 BillingClient 获取订阅列表
3. 检查订阅查询是否成功
4. 解析订阅数据，检查订阅产品是否为空
5. 检查是否存在 autoRenewing = true 的订阅
6. 获取网络时间（失败则使用本地时间）
7. 比较订阅过期时间
8. 判断当前是否仍在有效期内

### 4. 详细的日志记录
- 每个关键步骤都有详细的日志
- 包含时间戳、状态、错误信息等
- 便于问题排查和数据分析

## 使用方法

### 1. 初始化
```kotlin
// 在 Application 或 Activity 中初始化
BillingRepo.init(activity)
```

### 2. 检查订阅状态
```kotlin
// 检查是否已订阅
val isSubscribed = BillingRepo.isSubscribed()

// 获取详细的订阅状态信息
val subscriptionStatus = BillingRepo.getSubscriptionStatus()
println("是否订阅: ${subscriptionStatus.isSubscribed}")
println("到期时间戳: ${subscriptionStatus.expirationTimestamp}")
println("到期日期: ${subscriptionStatus.expirationDateString}")
```

### 3. 监听订阅状态变化
```kotlin
// 使用 StateFlow 监听订阅状态变化
BillingRepo.subscribedStateFlow.collect { isSubscribed ->
    // 处理订阅状态变化
    if (isSubscribed) {
        // 用户已订阅
    } else {
        // 用户未订阅
    }
}
```

### 4. 主动检查订阅状态
```kotlin
// 从指定来源检查订阅状态
BillingRepo.checkUserInSubscriptionPeriod("app_launch")
```

## 新增的事件日志

### 网络时间同步相关
- `network_time_sync_start`: 开始同步网络时间
- `network_time_sync_success`: 网络时间同步成功
- `network_time_sync_failed`: 网络时间同步失败
- `network_time_sync_error`: 网络时间同步异常

### 订阅状态检查相关
- `subscription_check_start`: 开始订阅状态检查
- `subscription_check_query_success`: 订阅查询成功
- `subscription_check_query_failed`: 订阅查询失败
- `subscription_check_products_empty`: 订阅产品为空
- `subscription_check_auto_renewing_true`: 存在自动续费订阅
- `subscription_check_auto_renewing_false`: 无自动续费订阅
- `subscription_check_network_time_success`: 网络时间获取成功
- `subscription_check_network_time_failed`: 网络时间获取失败
- `subscription_check_use_local_time`: 使用本地时间
- `subscription_check_still_valid`: 订阅仍有效
- `subscription_check_expired`: 订阅已过期
- `subscription_check_keep_previous_status`: 保留上次订阅状态

## 网络时间API列表

系统使用以下时间API（按优先级排序）：
1. `http://worldtimeapi.org/api/ip`
2. `http://worldtimeapi.org/api/timezone/Etc/UTC`
3. `https://timeapi.io/api/Time/current/zone?timeZone=UTC`
4. `http://worldclockapi.com/api/json/utc/now`
5. `https://api.timezonedb.com/v2.1/get-time-zone?key=demo&format=json&by=zone&zone=UTC`

## 注意事项

1. **网络时间同步**：应用启动时会自动同步一次网络时间，后续使用校正值计算当前时间
2. **订阅状态保持**：如果订阅查询失败，会保留上次的订阅状态
3. **自动续费优先**：如果存在自动续费的订阅，直接判定为有效，无需检查到期时间
4. **时间比较**：优先使用网络时间，失败时使用本地时间进行比较
5. **详细日志**：所有关键操作都有详细的日志记录，便于问题排查

## 数据结构

### SubscriptionStatus
```kotlin
data class SubscriptionStatus(
    val isSubscribed: Boolean,           // 是否已订阅
    val expirationTimestamp: Long?,      // 订阅到期时间戳（毫秒）
    val expirationDateString: String?    // 订阅到期时间的本地日期格式字符串
)
```

## 流程图

```mermaid
flowchart TD
    A[启动 App] --> B[调用 BillingClient 获取订阅列表]

    B --> C{订阅查询成功?}
    C -->|否| X1[保留上次订阅状态] --> Z[结束]
    C -->|是| D[解析订阅数据]

    D --> E{订阅产品是否为空?}
    E -->|是| X2[订阅失效] --> L[isSubscribed = false] --> Z

    E -->|否| F{是否存在 autoRenewing = true?}
    F -->|是| Q1[订阅有效] --> N[isSubscribed = true] --> Z
    F -->|否| G[获取网络时间]

    G --> H{网络时间获取成功?}
    H -->|否| I[使用本地时间]
    H -->|是| J[使用网络时间]

    I --> K[比较订阅过期时间]
    J --> K

    K --> M{当前是否仍在有效期内?}
    M -->|是| Q2[订阅有效] --> N
    M -->|否| X3[订阅已过期] --> L
```

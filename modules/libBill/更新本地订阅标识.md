# 更新本地订阅标识

```mermaid
flowchart TD
    A[开始] --> C[获取订阅列表]
    
    C --> D{获取订阅列表是否成功?}
    D -->|失败| E[检查失败]
    D -->|成功| J[解析订阅数据]
    
    J --> K{订阅产品列表是否为空?}
    K -->|是| P1[订阅失效]
    K -->|否| M{是否存在自动续订产品?}
    
    M -->|是| Q1[订阅有效]
    M -->|否| F[获取网络时间]
    
    F --> G{网络时间获取是否成功?}
    G -->|成功| H[使用网络时间判断]
    G -->|失败| I[使用本地时间判断]
    
    H --> O{订阅是否已过期?}
    I --> O
    
    O -->|是| P1
    O -->|否| Q1
    
    P1 --> L[设置订阅状态标志为false]
    Q1 --> N[设置订阅状态标志为true]
    
    L --> Z
    N --> Z
    
    E --> Z[结束]
```

## 流程说明

1. **初始化验证**：App启动后直接获取订阅列表
2. **订阅列表验证**：
   - 如果验证失败，则标记为检查失败并结束流程（不改变本地订阅状态标志）
   - 如果验证成功，继续解析订阅数据

3. **订阅列表判断**：
   - 如果订阅产品列表为空，则判定为订阅失效
   - 如果存在自动续订产品，则判定为订阅有效
   - 如果需要判断订阅是否过期，则获取时间

4. **时间获取**（仅用于判断订阅是否过期）：
   - 优先获取网络时间
   - 如果网络时间获取失败，则使用本地时间

5. **订阅过期判断**：
   - 如果订阅已过期，则判定为订阅失效
   - 如果订阅未过期，则判定为订阅有效

6. **订阅状态持久化**：
   - 订阅失效时：将订阅状态标志持久化存储为false
   - 订阅有效时：将订阅状态标志持久化存储为true

7. **处理结果**：
   - 检查失败：无法获取订阅列表，不改变本地订阅状态标志
   - 订阅失效：订阅产品列表为空或订阅已过期，将订阅状态标志设为false
   - 订阅有效：存在有效期内的订阅或自动续订产品，将订阅状态标志设为true

## 本地持久化处理

- **检查失败时**：保持本地订阅状态标志不变
- **订阅有效时**：将订阅状态标志(isSubscribed)持久化存储为true
- **订阅失效时**：将订阅状态标志(isSubscribed)持久化存储为false

## 特殊情况处理

- **自动续订产品**：如果订阅是自动续订的，则视为有效订阅
- **过期处理**：如果返回的已订阅产品列表非空，且订阅已过期，则判定为订阅失效
- **时间验证**：判断是否过期时优先使用网络时间，网络时间获取失败时使用本地时间 
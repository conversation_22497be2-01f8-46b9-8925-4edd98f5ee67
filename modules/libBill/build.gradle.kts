plugins {
    alias(libs.plugins.nowinandroid.android.library)
}
android {
    namespace = "com.lib.bill.manager"

    defaultConfig {
        // Add manifest placeholders
        manifestPlaceholders["MANDI_CHANNEL_VALUE"] = "google"
        manifestPlaceholders["MANDI_PUBLISH_TIME_VALUE"] = "20240601"
    }

    lint {
        // Disable lint checks for missing classes
        disable += "MissingClass"
        // Create a baseline to ignore existing issues
        baseline = file("lint-baseline.xml")
        // Abort on errors
        abortOnError = false
    }
}

dependencies {
    val billingVersion = "7.0.0"
    api("com.android.billingclient:billing:$billingVersion")
    api("com.android.billingclient:billing-ktx:$billingVersion")

    api(project(":libDomain"))
}
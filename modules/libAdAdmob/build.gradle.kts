plugins {
    alias(libs.plugins.nowinandroid.android.library)
}
android {
    namespace = "com.lib.ad.admob"
}
dependencies {
    implementation(project(":libDomain"))
    /** Admob核心 **/
    implementation("com.google.android.gms:play-services-ads:24.2.0")
//    implementation("com.google.ads.mediation:facebook:$rootProject.admob_fb")
//    implementation("com.google.ads.mediation:applovin:$rootProject.admob_applovin")
//    implementation("com.google.ads.mediation:mintegral:$rootProject.admob_mintegral")
//    implementation("com.google.ads.mediation:vungle:$rootProject.admob_vungle")
//    implementation("com.google.ads.mediation:pangle:$rootProject.admob_pangle")
}
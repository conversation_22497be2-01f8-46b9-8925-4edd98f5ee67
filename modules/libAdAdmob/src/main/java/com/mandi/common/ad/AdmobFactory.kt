package com.mandi.common.ad

import android.app.Activity
import android.util.Size
import android.view.ViewGroup
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.ads.appopen.AppOpenAd
import com.google.android.gms.ads.appopen.AppOpenAd.AppOpenAdLoadCallback
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import com.google.android.gms.ads.rewarded.RewardedAd
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback
import com.google.android.gms.ads.rewardedinterstitial.RewardedInterstitialAd
import com.google.android.gms.ads.rewardedinterstitial.RewardedInterstitialAdLoadCallback
import com.tiny.ad.network.AdGroupType
import com.tiny.ad.network.AdObject
import com.tiny.ad.network.AdResultDelegate
import com.tiny.ad.network.AdType
import com.tiny.ad.network.AdUnitInfo
import com.tiny.ad.network.IAdBehavior
import com.tiny.ad.network.configure.AdUnitIdsGroupRepo
import com.tiny.ad.old.AdShowResult
import com.tiny.domain.ext.logE
import com.tiny.domain.ext.logEvent
import com.tiny.domain.ext.logI
import com.tiny.domain.ext.safeAddView
import com.tiny.domain.ext.safeComplete
import com.tiny.domain.ext.safeGetCompleteValue
import com.tiny.domain.util.InitDelegate
import com.tinypretty.component.IAdFactory
import com.tinypretty.component.KoinTools
import com.tinypretty.component.str
import com.tinypretty.event.CommonEventName
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @Since 2023/12/27
 * 新应用必须
 * 1 admob 审核
 * 2 隐私政策增加 GDPR 隐私网址配置
 * 才能请求到广告
 * https://apps.admob.com/v2/privacymessaging
 */
object AdmobFactory : IAdFactory {
    private const val TAG = "AdmobFactory"
    private const val TAG_CP = "AdmobFactoryCp"
    private val mGoogleAdConfigure by KoinTools.injectGoogleAdConfigure()
    private val mTestDeviceIDList: MutableList<String> = mutableListOf()
    private const val TAG_BANNER = "AdmobFactoryBanner"
    private const val TAG_REWARD = "AdmobFactoryReward"
    private const val TAG_APP_OPEN = "AdmobFactoryAppOpen"
    private const val TAG_NATIVE = "AdmobFactoryNative"

    private val mL by KoinTools.injectLog("AdFactory")


    override fun getAdGroup(type: AdGroupType, adPixSize: Size?): List<AdUnitInfo> {
        return emptyList()
    }

//    开屏广告	ca-app-pub-3940256099942544/9257395921
//    自适应横幅广告	ca-app-pub-3940256099942544/9214589741
//    固定尺寸横幅广告	ca-app-pub-3940256099942544/6300978111
//    插页式广告	ca-app-pub-3940256099942544/1033173712
//    插页式视频广告	ca-app-pub-3940256099942544/8691691433
//    激励广告	ca-app-pub-3940256099942544/5224354917
//    插页式激励广告	ca-app-pub-3940256099942544/5354046379
//    原生高级广告	ca-app-pub-3940256099942544/2247696110
//    原生高级视频广告	ca-app-pub-3940256099942544/1044960115

//    private const val INTERSTITIAL_ID: String = "ca-app-pub-3940256099942544/1033173712"
//    private const val REWARD_ID: String = "ca-app-pub-3940256099942544/5224354917"
//    private const val BANNER_ID: String = "ca-app-pub-3940256099942544/9214589741"
//    private const val APP_OPEN_ID: String = "ca-app-pub-3940256099942544/9257395921"
    // fixbanner	ca-app-pub-3940256099942544/2934735716

    private var mTestIDList = mutableListOf<String>()
    private val mInitDelegate = InitDelegate().apply {
        timeOut = 10000
        initResultListener = {
            it.onSuccess {
                CommonEventName.EVENT_AD_INIT_SUCCEED + "_TRUE".logEvent()
            }.onFailure { throwable ->
                CommonEventName.EVENT_AD_INIT_SUCCEED + "_FALSE".logEvent("msg" to throwable.message)
            }
        }
        initAction = { activity ->
            if (activity == null) {
                onInitFailed()
            } else {
                withContext(Dispatchers.IO) {
                    TAG.logI("initSdk")
                    val job = launch { AdUnitIdsGroupRepo.updateAdUnitIds() }
                    TAG.logI("initGDPR")
                    mGoogleAdConfigure.initGDPR(activity, mTestDeviceIDList)
                    TAG.logI("initMobileAds")
                    MobileAds.initialize(activity)
                    job.join()
                    onInitSucceed()
                }
            }
        }
    }

    private fun createAdList(groupKey: String) =
        AdUnitIdsGroupRepo.getGroupIds(groupKey)?.mapNotNull {
            val type = it.str("type")
            when (type) {
                "interstitial" -> cp(it.str("id"), it.str("name"))
                "reward" -> reward(it.str("id"), it.str("name"))
                "appOpen" -> appOpen(it.str("id"), it.str("name"))
                "banner" -> banner(it.str("id"), it.str("name"))
                else -> null
            }
        }.apply { TAG.logI("createAdList -> $groupKey ${AdUnitIdsGroupRepo.mCachedAdUnitIds} ${this?.map { "${it.id}:${it.type}:${it.cacheKey}" }}") } ?: emptyList()

    fun appOpenGroup() = createAdList(AdUnitIdsGroupRepo.TYPE_APP_OPEN)
    fun popAdGroup() = createAdList(AdUnitIdsGroupRepo.TYPE_POP_UP)
    fun rewardAdGroup() = createAdList(AdUnitIdsGroupRepo.TYPE_REWARD)
    fun bannerAdGroup() = createAdList(AdUnitIdsGroupRepo.TYPE_BANNER)
    fun nativeAdGroup() = createAdList(AdUnitIdsGroupRepo.TYPE_RECT)

    private fun cp(id: String, cacheKey: String) = AdUnitInfo(id, AdType.INTERSTITIAL, cacheKey, 0, object : IAdBehavior {
        override suspend fun load(activity: Activity, adInfo: AdUnitInfo): MutableList<AdObject> {
            mInitDelegate.waitWhenInit(activity)
            val adResult = AdResultDelegate(TAG, adInfo)
            InterstitialAd.load(activity, id, AdRequest.Builder().build(), object : InterstitialAdLoadCallback() {
                override fun onAdLoaded(interstitialAd: InterstitialAd) {
                    TAG_CP.logI("load succeed ${interstitialAd::class.java.simpleName}")
                    adResult.succeed(interstitialAd)
                }

                override fun onAdFailedToLoad(adError: LoadAdError) {
                    TAG_CP.logI("load failed -> ${adError.message}")
                    adResult.failed()
                }
            })
            return adResult.getResult()
        }

        override suspend fun show(
            activity: Activity,
            adObject: AdObject,
            viewGroup: ViewGroup?
        ): AdShowResult {
            val resultDeferred = CompletableDeferred<AdShowResult>()
            val ad = adObject.ad as? InterstitialAd ?: return AdShowResult(false, "ad is null")

            ad.fullScreenContentCallback = object : FullScreenContentCallback() {
                override fun onAdShowedFullScreenContent() {
                    TAG_CP.logI("onAdShowed")
                }

                override fun onAdDismissedFullScreenContent() {
                    TAG_CP.logI("onAdDismissed")
                    resultDeferred.safeComplete(AdShowResult(true))
                }

                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    TAG_CP.logE("onAdFailedToShow -> ${adError.message}")
                    resultDeferred.safeComplete(AdShowResult(false, adError.message))
                }
            }

            withContext(Dispatchers.Main) {
                ad.show(activity)
            }
            return resultDeferred.safeGetCompleteValue() ?: AdShowResult(false, "show failed")
        }

        override fun destroy(adObject: AdObject) {
            adObject.ad.let {
                TAG_CP.logI("destroy ad")
                if (it is InterstitialAd) {
                    it.fullScreenContentCallback = null
                }
            }
        }
    })


    private fun banner(id: String, cacheKey: String) = AdUnitInfo(id, AdType.BANNER, cacheKey, 0, object : IAdBehavior {
        override suspend fun load(activity: Activity, adInfo: AdUnitInfo): MutableList<AdObject> =
            withContext(Dispatchers.Main) {
                mInitDelegate.waitWhenInit(activity)
                val adResult = AdResultDelegate(TAG, adInfo)
                AdView(activity).also { adView ->
                    adView.adUnitId = adInfo.id
                    adView.setAdSize(AdSize(AdSize.FULL_WIDTH, AdSize.AUTO_HEIGHT))
                    adView.adListener = object : AdListener() {
                        override fun onAdLoaded() {
                            super.onAdLoaded()
                            adResult.succeed(adView)
                        }

                        override fun onAdFailedToLoad(adError: LoadAdError) {
                            super.onAdFailedToLoad(adError)
                            adResult.failed()
                        }
                    }
                    adView.loadAd(AdRequest.Builder().build())
                }
                adResult.getResult()
            }

        override suspend fun show(activity: Activity, adObject: AdObject, viewGroup: ViewGroup?): AdShowResult = withContext(Dispatchers.Main) {
            val adView = adObject.ad as? AdView
            viewGroup.safeAddView(adView)
            AdShowResult(adView != null)
        }

        override fun destroy(adObject: AdObject) {
            val adView = adObject.ad as? AdView
            adView?.destroy()
        }
    })

    private fun reward(id: String, cacheKey: String) = AdUnitInfo(id, AdType.REWARDED, cacheKey, 0, object : IAdBehavior {
        override suspend fun load(activity: Activity, adInfo: AdUnitInfo): MutableList<AdObject> {
            mInitDelegate.waitWhenInit(activity)
            val adDeferred = AdResultDelegate(TAG, adInfo)

            // 判断是否是插页式激励广告
            val isRewardedInterstitial = cacheKey.contains("interstitial", ignoreCase = true) ||
                                         id.contains("5354046379") // 插页式激励广告测试ID

            if (isRewardedInterstitial) {
                // 加载插页式激励广告
                RewardedInterstitialAd.load(activity, id, AdRequest.Builder().build(), object : RewardedInterstitialAdLoadCallback() {
                    override fun onAdLoaded(rewardedInterstitialAd: RewardedInterstitialAd) {
                        super.onAdLoaded(rewardedInterstitialAd)
                        TAG_REWARD.logI("load succeed (RewardedInterstitial)")
                        adDeferred.succeed(rewardedInterstitialAd)
                    }

                    override fun onAdFailedToLoad(adError: LoadAdError) {
                        TAG_REWARD.logI("load failed (RewardedInterstitial) -> ${adError.message}")
                        adDeferred.failed()
                    }
                })
            } else {
                // 加载普通激励广告
                RewardedAd.load(activity, id, AdRequest.Builder().build(), object : RewardedAdLoadCallback() {
                    override fun onAdLoaded(rewardedAd: RewardedAd) {
                        super.onAdLoaded(rewardedAd)
                        TAG_REWARD.logI("load succeed")
                        adDeferred.succeed(rewardedAd)
                    }

                    override fun onAdFailedToLoad(adError: LoadAdError) {
                        TAG_REWARD.logI("load failed -> ${adError.message}")
                        adDeferred.failed()
                    }
                })
            }
            return adDeferred.getResult()
        }

        override suspend fun show(
            activity: Activity,
            adObject: AdObject,
            viewGroup: ViewGroup?
        ): AdShowResult {
            val resultDeferred = CompletableDeferred<AdShowResult>()
            var gainReward = false

            // 处理不同类型的激励广告
            when (val ad = adObject.ad) {
                is RewardedAd -> {
                    ad.fullScreenContentCallback = object : FullScreenContentCallback() {
                        override fun onAdShowedFullScreenContent() {
                            TAG_REWARD.logI("onAdShowed")
                        }

                        override fun onAdDismissedFullScreenContent() {
                            TAG_REWARD.logI("onAdDismissed")
                            resultDeferred.safeComplete(AdShowResult(true))
                        }

                        override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                            TAG_REWARD.logE("onAdFailedToShow -> ${adError.message}")
                            resultDeferred.safeComplete(AdShowResult(false, adError.message))
                        }
                    }
                    withContext(Dispatchers.Main) { ad.show(activity) { gainReward = true } }
                }
                is RewardedInterstitialAd -> {
                    ad.fullScreenContentCallback = object : FullScreenContentCallback() {
                        override fun onAdShowedFullScreenContent() {
                            TAG_REWARD.logI("onAdShowed (RewardedInterstitial)")
                        }

                        override fun onAdDismissedFullScreenContent() {
                            TAG_REWARD.logI("onAdDismissed (RewardedInterstitial)")
                            resultDeferred.safeComplete(AdShowResult(true))
                        }

                        override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                            TAG_REWARD.logE("onAdFailedToShow (RewardedInterstitial) -> ${adError.message}")
                            resultDeferred.safeComplete(AdShowResult(false, adError.message))
                        }
                    }
                    withContext(Dispatchers.Main) { ad.show(activity) { gainReward = true } }
                }
                else -> {
                    return AdShowResult(false, "ad is null or unsupported type")
                }
            }

            return safeAwaitShowResult(TAG_REWARD, resultDeferred) { gainReward }
        }

        override fun destroy(adObject: AdObject) {
            adObject.ad.let {
                TAG_REWARD.logI("destroy ad")
                when (it) {
                    is RewardedAd -> {
                        it.fullScreenContentCallback = null
                    }
                    is RewardedInterstitialAd -> {
                        it.fullScreenContentCallback = null
                    }
                }
            }
        }
    })

    private fun appOpen(id: String, cacheKey: String) = AdUnitInfo(id, AdType.APP_OPEN, cacheKey, 0, object : IAdBehavior {
        override suspend fun load(activity: Activity, adInfo: AdUnitInfo): MutableList<AdObject> {
            mInitDelegate.waitWhenInit(activity)
            val adResult = AdResultDelegate(TAG, adInfo)
            AppOpenAd.load(activity, id, AdRequest.Builder().build(), object : AppOpenAdLoadCallback() {
                override fun onAdLoaded(appOpenAd: AppOpenAd) {
                    super.onAdLoaded(appOpenAd)
                    TAG_APP_OPEN.logI("load succeed")
                    adResult.succeed(appOpenAd)
                }

                override fun onAdFailedToLoad(adError: LoadAdError) {
                    TAG_APP_OPEN.logI("load failed -> ${adError.message}")
                    adResult.failed()
                }
            })
            return adResult.getResult()
        }

        override suspend fun show(
            activity: Activity,
            adObject: AdObject,
            viewGroup: ViewGroup?
        ): AdShowResult {
            val resultDeferred = CompletableDeferred<AdShowResult>()
            val ad = adObject.ad as? AppOpenAd ?: return AdShowResult(false, "ad is null")
            ad.fullScreenContentCallback = object : FullScreenContentCallback() {
                override fun onAdShowedFullScreenContent() {
                    TAG_APP_OPEN.logI("onAdShowed")
                }

                override fun onAdDismissedFullScreenContent() {
                    TAG_APP_OPEN.logI("onAdDismissed")
                    resultDeferred.safeComplete(AdShowResult(true))
                }

                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    TAG_APP_OPEN.logE("onAdFailedToShow -> ${adError.message}")
                    resultDeferred.safeComplete(AdShowResult(false, adError.message))
                }
            }

            withContext(Dispatchers.Main) { ad.show(activity) }
            return safeAwaitShowResult(TAG_APP_OPEN, resultDeferred)
        }

        override fun destroy(adObject: AdObject) {
            adObject.ad.let {
                TAG_APP_OPEN.logI("destroy ad")
                if (it is AppOpenAd) {
                    it.fullScreenContentCallback = null
                }
            }
        }
    })

    // gainReward: 是否获得奖励,除了激励视频,其他都强制返回true
    private suspend fun safeAwaitShowResult(tag: String, resultDeferred: CompletableDeferred<AdShowResult>, gainReward: () -> Boolean? = { true }): AdShowResult {
        val result = resultDeferred.safeGetCompleteValue()?.apply {
            rewarded = gainReward() ?: false
        } ?: AdShowResult(false, "show failed")
        tag.logI("safeAwaitShowResult -> $result")
        return result
    }
}
package com.tinypretty.ui.componets

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.text.HtmlCompat
import com.tinypretty.component.KoinTools
import com.tinypretty.ui.theme.C
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.T
import com.tinypretty.util.CoilImageGetter

val app by KoinTools.injectApplication()


@Composable
fun HtmlText(
    text: String,
    color: Color = MT.C().onBackground,
    style: TextStyle = MT.T().bodySmall,
    iconHeight: Dp = 96.dp,
    textAlign: TextAlign? = null,
    maxLines: Int = 1000, // 设置默认最大行数，避免过大内容导致布局问题
    modifier: Modifier = Modifier
) {
//    var testColor =
//        MT.C().onBackground.copy(0.9f).hashCode()

    AndroidView(
        modifier = modifier,
        factory = { context ->
            TextView(context).apply {
                // 始终设置最大行数，防止无限制导致布局问题
                this.maxLines = if (maxLines <= 0) 1000 else maxLines

                // 设置文本视图的布局参数，限制最大高度
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )

                textSize = style.fontSize.value
                setTextColor(color.hashCode())
                textAlign?.let {
                    if (it == TextAlign.Center) {
                        textAlignment = View.TEXT_ALIGNMENT_CENTER
                    }
                }
            }
        },
        update = { it ->
            var imageGetter = CoilImageGetter(it)
//            var imageGetter = Html.ImageGetter {
//                var drawable: Drawable? = null
//                runCatching {
//                    drawable =if (it.startsWith("http")){
//                        AssertUtil.drawableFromUrl(it)
//                    } else{
//                            Drawable.createFromStream(app.applicationContext.assets.open(it), null)
//                                ?.apply {
//                                    var w = iconHeight.value.toInt()
//                                    setBounds(0, 0, w, w * intrinsicWidth / intrinsicHeight)
////                                setColorFilter(testColor,
////                                    PorterDuff.Mode.SRC_ATOP
////                                )
//                                }
//                    }
//                }
//                drawable
//            }

            try {
                it.text = HtmlCompat.fromHtml(
                    text,
                    HtmlCompat.FROM_HTML_MODE_COMPACT,
                    imageGetter
                ) { b, s, editable, xmlReader ->
                    var dd = "$b $s"
                }
            } catch (e: Exception) {
                // 如果HTML解析失败，至少显示纯文本
                it.text = text
            }
        }
    )
}
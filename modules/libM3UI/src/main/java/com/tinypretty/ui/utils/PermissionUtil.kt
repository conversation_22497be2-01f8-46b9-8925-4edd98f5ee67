package com.tinypretty.ui.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.tiny.domain.util.AdConfigure
import com.tiny.domain.util.ChannelUtil
import com.tiny.domain.util.enabledAfterLongTime
import com.tiny.domain.util.forEachGroup
import com.tiny.domain.util.runThenFreeze
import com.tinypretty.component.IntentTools.openSettings
import com.tinypretty.component.KoinTools
import com.tinypretty.component.findActivity
import com.tinypretty.ui.DocPrivateUI
import com.tinypretty.ui.componets.AppButton
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.RowSplit
import com.tinypretty.ui.componets.SpacerFix
import com.tinypretty.ui.componets.ad.CollapsingToolBar
import com.tinypretty.ui.dialogs.alertContent
import com.tinypretty.ui.dialogs.closeableDlg
import com.tinypretty.ui.mConfig
import com.tinypretty.ui.navigation.NavHostControllerHolder
import com.tinypretty.ui.theme.C
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.S
import com.tinypretty.ui.theme.T
import com.tinypretty.ui.theme.big
import com.tinypretty.ui.theme.clip
import com.tinypretty.ui.utils.PermissionUtil.isGrant

val mL by KoinTools.injectLog("PermissionsUtil")

object PermissionUtil {
    val allAdPermission by lazy {
        permissionReadWrite.toTypedArray()
    }


    val permissionReadWrite by lazy {
        arrayListOf<String>().let {
            it.addAll(permissionStorage)
            it.addAll(permissionRead)
            it
        }
    }

    val permissionStorage by lazy {
        arrayListOf<String>().let {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
                it.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
            it
        }
    }


    val permissionRead by lazy {
        arrayListOf<String>().let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                it.add(Manifest.permission.READ_MEDIA_IMAGES)
                it.add(Manifest.permission.READ_MEDIA_VIDEO)
//                it.add(Manifest.permission.READ_MEDIA_AUDIO)
            } else {
                it.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
            it
        }
    }

    fun appendAllAdPermission(p: Array<String>): Array<String> {
        var result = arrayListOf<String>().apply {
            addAll(p)
        }
        if (enabledAfterLongTime()) {
            allAdPermission.forEach {
                if (!p.contains(it) && !result.contains(it)) {
                    result.add(it)
                }
            }
        }
        return result.toTypedArray()
    }

    fun isGrant(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            permission
        ) == PackageManager.PERMISSION_GRANTED
    }
}


@Composable
fun ContentPermissionsNecessary(
    permissions: Array<String>,
    content: @Composable () -> Unit
) {
    mL.d { "ContentPermissionsNecessary $permissions" }
    val context = LocalContext.current
    var showSetting: MutableState<Boolean> = remember { mutableStateOf(false) }


    fun createRequiredPermissionList(): ArrayList<String> {
        var result = arrayListOf<String>()
        permissions.forEach { permission ->
            if (!isGrant(context, permission)) {
                result.add(permission)
            }
        }
        return result
    }
    // 获取所需的所有权限
    var allRequiredPermissionList = createRequiredPermissionList()
    var granted = remember { mutableStateOf(true) }
    granted.value = allRequiredPermissionList.isEmpty()

    if (granted.value) {
        content.invoke()
        return
    }

    // 所有需要获取的权限.toString()为一个状态, 在onResume的时候刷新状态
    var permissionState = remember {
        mutableStateOf(allRequiredPermissionList.toString())
    }

    onStartAndOnStop(onStart = {
        permissionState.value = createRequiredPermissionList().toString()
        mL.d { "onStart ${permissionState.value}" }
    }, onStop = {
        mL.d { "onStop" }
    })

    if (permissionState.value.isNotBlank()) {
        RequestMultiplePermissionsHandler(
            onPermissionResult = { isGranted ->
                if (isGranted.all { it.value }) {
                    granted.value = true
                } else {
                    showSetting.value = true
                }
            }) { activityResultLauncher ->
            var canRun = runThenFreeze("requestPermission", 3) { true }
            if (canRun == true) {
                SideEffect {
                    activityResultLauncher.launch(permissions)
                }
            }
            fun getPermission() {
                if (showSetting.value) {
                    context.openSettings()
                    AdConfigure.onFullAdShow(true)
                } else {
                    activityResultLauncher.launch(permissions)
                }
            }

            Column(
                Modifier
                    .clip()
                    .padding(MT.pd.big())
                    .fillMaxSize()
                    .clickable {
                        getPermission()
                    },
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                permissions.forEach { permission ->
                    Row(
                        modifier = Modifier
                            .padding(top = 6.dp)
                            .clickable {
                                getPermission()
                            }
                            .clip()
                            .background(
                                color = MT.C().onBackground,
                                shape = MT.S().large
                            )
                            .fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        var fixed = permission !in allRequiredPermissionList
                        if (!fixed) {
                            ImageApp(
                                data = "res/ic_warn.png", modifier = Modifier
                                    .size(32.dp)
                                    .padding(6.dp),
                                color = MT.C().error
                            )
                        }

                        Text(
                            modifier = Modifier
                                .weight(1f)
                                .padding(start = 6.dp),
                            color = MT.C().background.copy(if (fixed) 1f else 0.6f),
                            text = permission.replace(
                                "android.permission.",
                                ""
                            ),
                            style = MT.T().titleMedium,
                            maxLines = 1
                        )
                        Box(
                            Modifier
                                .padding(6.dp)
                                .clip(CircleShape)
                                .background(MT.C().background.copy(if (fixed) 1f else 1f))
                        ) {
                            ImageApp(
                                data = if (fixed) "res/ic_selected.png" else "res/ic_add.png",
                                modifier = Modifier
                                    .size(32.dp)
                                    .padding(6.dp),
                                color = MT.C().onBackground
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ContentRequiredMultiplePermissions(
    requestPermissionGap: Long,
    ignoreAble: Boolean = false,
    permissions: Array<String>,
    permissionHints: String? = null,
    content: @Composable () -> Unit
) {
    mL.d { "ContentRequiredMultiplePermissions $permissions" }
    val canrequestPermission =
        runThenFreeze("ContentRequiredMultiplePermissions", requestPermissionGap) {
            true
        }

    if (canrequestPermission != true) {
        content.invoke()
        return
    }


    val context = LocalContext.current
    val showSetting: MutableState<Boolean> = remember { mutableStateOf(false) }

    mL.d { "ContentRequiredMultiplePermissions redraw" }

    val ignorePermissionGrant = remember { mutableStateOf(false) }
    if (ignorePermissionGrant.value) {
        content.invoke()
        return
    }

    fun createRequiredPermissionList(): ArrayList<String> {
        val result = arrayListOf<String>()
        permissions.forEach { permission ->
            if (!isGrant(context, permission)) {
                result.add(permission)
            }
        }
        return result
    }
    // 获取所需的所有权限
    val allRequiredPermissionList = createRequiredPermissionList()
    val granted = remember { mutableStateOf(true) }
    granted.value = allRequiredPermissionList.isEmpty()

    if (granted.value) {
        content.invoke()
        return
    }

    val isAgreedPermissionHint = remember { mutableStateOf(permissionHints == null) }
    if (!isAgreedPermissionHint.value) {
        val state = remember { mutableStateOf(true) }
        alertContent(state = state, onDismiss = {
            NavHostControllerHolder.back()
        }) {
            Column(
                Modifier
                    .background(MT.color.background)
                    .clip()
                    .padding(MT.pd.big())
                    .fillMaxSize()
                    .clickable {
                        state.value = false
                        isAgreedPermissionHint.value = true
                    },
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = permissionHints ?: "",
                    style = MT.T().titleMedium,
                    color = MT.C().onBackground
                )

                AppButton(msg = "申请", onClick = {
                    state.value = false
                    isAgreedPermissionHint.value = true
                })

                AppButton(msg = "取消", onClick = {
                    state.value = false
                    isAgreedPermissionHint.value = false
                    NavHostControllerHolder.back()
                })
            }
        }
        return
    }

    // 所有需要获取的权限.toString()为一个状态, 在onResume的时候刷新状态
    val permissionState = remember {
        mutableStateOf(allRequiredPermissionList.toString())
    }

    onStartAndOnStop(onStart = {
        permissionState.value = createRequiredPermissionList().toString()
        mL.d { "onStart ${permissionState.value}" }
    }, onStop = {
        mL.d { "onStop" }
    })

    if (permissionState.value.isNotBlank()) {
        RequestMultiplePermissionsHandler(
            onPermissionResult = { isGranted ->
                if (isGranted.all { it.value }) {
                    granted.value = true
                } else {
                    showSetting.value = true
                }
            }) { activityResultLauncher ->

            var permissionHintContent: @Composable () -> Unit = {
                Column(
                    Modifier
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.primary,
                                    MaterialTheme.colorScheme.background
                                )
                            )
                        )
                        .padding(MT.pd.big())
                        .fillMaxSize()
                        .clickable {
                            activityResultLauncher.launch(permissions)
                        },
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CollapsingToolBar(
                        {
                            Column(
                                Modifier
                                    .clip()
                                    .background(MT.C().onBackground.copy(0.6f))
                                    .padding(6.dp)
                                    .fillMaxWidth(),
                                verticalArrangement = Arrangement.Center
                            ) {
                                12.SpacerFix()
                                Text(
                                    text = "continue to use ${ChannelUtil.mAppName}\n${if (ignoreAble) " recommend" else " need"} Apply for the following permissions",
                                    modifier = Modifier
                                        .fillMaxWidth(),
                                    textAlign = TextAlign.Center,
                                    color = MT.C().background,
                                    style = MT.T().labelSmall
                                )
                                12.SpacerFix()
                                allRequiredPermissionList.forEachGroup(2) { group ->
                                    RowSplit(columnCount = 2) {
                                        group.getOrNull(it)?.let { permission ->
                                            Box(contentAlignment = Alignment.Center,
                                                modifier = Modifier
                                                    .alpha(0.8f)
                                                    .clickable {
                                                        activityResultLauncher.launch(permissions)
                                                    }
                                                    .background(
                                                        color = MT.C().background,
                                                        shape = MT.S().large
                                                    )
                                                    .fillMaxWidth()
                                            ) {
                                                Text(
                                                    modifier = Modifier
                                                        .padding(
                                                            top = 6.dp,
                                                            bottom = 6.dp,
                                                            start = 2.dp,
                                                            end = 2.dp
                                                        )
                                                        .alpha(0.9f),
                                                    color = MT.C().onBackground,
                                                    text = permission.replace(
                                                        "android.permission.",
                                                        ""
                                                    ),
                                                    style = MT.T().labelSmall,
                                                    maxLines = 1
                                                )
                                            }
                                        }
                                    }
                                }
                                12.SpacerFix()
                            }
                        }
                    ) {
                        Column(
                            Modifier
                                .fillMaxWidth()
                                .padding(top = 12.dp)
                                .weight(1F)
                        ) {

                            if (ignoreAble) {
                                Row(
                                    Modifier
                                        .fillMaxWidth()
                                ) {
                                    OutlinedButton(
                                        onClick = {
                                            context.findActivity()?.finish()
                                        },
                                        Modifier
                                            .weight(1f)
                                            .padding(0.dp)
                                    ) {
                                        Text(text = "Not in use yet", style = MT.T().labelSmall)
                                    }
                                    Spacer(modifier = Modifier.size(12.dp))
                                    Button(onClick = {
                                        mConfig.putBoolean("doc_private", true)
                                        ignorePermissionGrant.value = true
                                    }, Modifier.weight(1f)) {
                                        Text(
                                            text = "continue to use",
                                            color = MT.C().onPrimary,
                                            style = MT.T().labelSmall
                                        )
                                    }
                                }
                            }
                            Button(onClick = {
                                if (showSetting.value) {
                                    context.openSettings()
                                    AdConfigure.onFullAdShow(true)
                                } else {
                                    activityResultLauncher.launch(permissions)
                                }
                            }, modifier = Modifier.fillMaxWidth()) {
                                Text(
                                    text = "Apply for required permissions",
                                    color = MT.C().onPrimary,
                                    style = MT.T().labelSmall
                                )
                            }

                            12.SpacerFix()
                            DocPrivateUI(modifier = Modifier.weight(1f))
                        }
                    }
                }
            }
            if (ignoreAble) {
                closeableDlg(contentState = remember {
                    mutableStateOf(true)
                },
                    onClose = {
                        ignorePermissionGrant.value = true
                    }
                ) {
                    permissionHintContent.invoke()
                }
            } else {
                permissionHintContent.invoke()
            }

            var canRun = runThenFreeze("requestPermission", 3) { true }
            if (canRun == true) {
                SideEffect {
                    activityResultLauncher.launch(permissions)
                }
            }
        }
    }
}

@Composable
fun onStartAndOnStop(
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onStart: () -> Unit, // Send the 'started' analytics event
    onStop: () -> Unit   // Send the 'stopped' analytics event
) {
    // Safely update the current lambdas when a new one is provided
    val currentOnStart by rememberUpdatedState(onStart)
    val currentOnStop by rememberUpdatedState(onStop)

    // If `lifecycleOwner` changes, dispose and reset the effect
    DisposableEffect(lifecycleOwner) {
        // Create an observer that triggers our remembered callbacks
        // for sending analytics events
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_START) {
                currentOnStart()
            } else if (event == Lifecycle.Event.ON_STOP) {
                currentOnStop()
            }
        }

        // Add the observer to the lifecycle
        lifecycleOwner.lifecycle.addObserver(observer)

        // When the effect leaves the Composition, remove the observer
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

@Composable
fun RequestMultiplePermissionsHandler(
    onPermissionResult: (Map<String, Boolean>) -> Unit,
    content: @Composable (ActivityResultLauncher<Array<String>>) -> Unit
) {
    val requestPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { isGranted: Map<String, Boolean> ->
        onPermissionResult(isGranted)
    }

    content(requestPermissionLauncher)
}
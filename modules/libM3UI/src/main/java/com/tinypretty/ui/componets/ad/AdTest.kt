package com.tinypretty.ui.componets.ad

import android.content.Intent
import android.provider.Settings
import android.view.WindowManager
import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.clickable
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.tiny.domain.util.AdConfigure
import com.tiny.domain.util.AdConfigure.showReward
import com.tinypretty.component.KoinTools
import com.tinypretty.component.findActivity
import com.tinypretty.ui.componets.AppButton
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.theme.C
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.alpha

val mPm by KoinTools.injectPreferencesMgr()

@Composable
fun KeepScreenOnBtn(modifier: Modifier) {
    var keepScreenOn = remember { mutableStateOf(mPm.getBoolean("KeepScreenOnBtn", false)) }
    var toaskFun: (Boolean) -> Unit = {}

    LocalContext.current.findActivity()?.run {
        toaskFun = {
            Toast.makeText(this, "屏幕常亮${if (it) "开启" else "关闭"}", Toast.LENGTH_SHORT).show()
        }
        mL.d { "keepScreenOn.value = ${keepScreenOn.value}" }
        if (keepScreenOn.value) {
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            startActivity(intent)
        } else {
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    ImageApp(data = "res/keepScreenOn.png", modifier = modifier.clickable {
        keepScreenOn.value = (!keepScreenOn.value).apply {
            mPm.putBoolean("KeepScreenOnBtn", this)
        }
        toaskFun.invoke(keepScreenOn.value)
    }, color = MT.C().secondary.alpha(enable = keepScreenOn.value))
}

@Composable
fun testBtn(btnText: String, view: @Composable () -> Unit) {
    var visible = remember { mutableStateOf(false) }
    AppButton(msg = btnText) {
        visible.value = !visible.value
    }

    if (visible.value) {
        view.invoke()
    }
}

@Composable
fun adTest() {
    var splashVisible = remember {
        mutableStateOf(false)
    }

    if (splashVisible.value) {
        splash() {
            Text(text = "splash done")
        }
        BackHandler() {
            splashVisible.value = false
        }
        return
    }
    var rewardResult = remember { mutableStateOf("") }

    LazyColumn {
        item {
            testBtn(btnText = "banner2") {
                banner2(place = "test")
            }
        }

        item {
            testBtn(btnText = "nativeSelf") {
                nativeSelf(place = "test")
            }
        }

        item {
            testBtn(btnText = "native") {
                native(place = "test")
            }
        }
        item {
            testBtn(btnText = "nativeBanner") {
                nativeBanner(place = "test")
            }

        }

        item {
            Text(text = rewardResult.value)
        }

        item {
            testBtn(btnText = "reward test") {
                rewardButton("test", "reward test") {
                    showReward("show reward") {
                        rewardResult.value = "reward $it"
                    }
                }
            }
        }
        item {
            testBtn(btnText = "cp") {
                AdConfigure.showCp("测试插屏", 1000) {}
            }
        }

        item {
            var splash = remember {
                mutableStateOf(false)
            }

            testBtn(btnText = "splash") {
                com.tinypretty.ui.componets.ad.splash() {
                    Text(text = "viewDone")
                }
            }
        }
    }
}
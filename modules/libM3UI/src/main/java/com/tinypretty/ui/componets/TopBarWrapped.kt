package com.tinypretty.ui.componets

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.unit.dp
import com.tiny.domain.util.ChannelUtil
import com.tiny.domain.util.mL
import com.tinypretty.ui.Setting
import com.tinypretty.ui.theme.C
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.T


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopBarWrapped(content: @Composable () -> Unit) {
    var showSetting = remember {
        mutableStateOf(false)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Scaffold(
            modifier = Modifier.background(MT.C().primary),
            topBar = {
                TopAppBar(
                    title = {
                        Text(
                            text = ChannelUtil.mAppName,
                            color = MT.C().onPrimary.copy(0.8f),
                            modifier = Modifier.background(MT.C().primary)
                        )
                    },
//                    colors = TopAppBarDefaults.topAppBarColors(containerColor = MT.C().primary),
                    modifier = Modifier.background(MT.C().primary),
                    actions = {
                        ImageApp(
                            data = "res/ic_settings.png", modifier = Modifier
                                .size(32.dp)
                                .padding(2.dp)
                                .clickable {
                                    showSetting.value = true
                                }, color = MT.C().onPrimary.copy(0.8f)
                        )
                    }
                )
            },
            content = { padding ->
                mL.d { "$padding" }
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.primary,
                                    MaterialTheme.colorScheme.background
                                )
                            )
                        )
                ) {
                    content.invoke()
                }
            },
        )
    }

    Setting(display = showSetting)
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopBar() {
    var showSetting = remember {
        mutableStateOf(false)
    }
    TopAppBar(
        title = {
            Text(
                ChannelUtil.mAppName,
                color = MT.C().onPrimary.copy(0.8f)
            )
        },
//        colors = TopAppBarDefaults.topAppBarColors(containerColor = MT.C().primary),
        actions = {
            ImageApp(
                data = "res/ic_settings.png", modifier = Modifier
                    .size(32.dp)
                    .padding(2.dp)
                    .clickable {
                        showSetting.value = true
                    }, color = MT.C().onPrimary.copy(0.8f)
            )
        }
    )
    Setting(display = showSetting)
}


@Composable
fun TopBarWrappedWhite(content: @Composable () -> Unit) {
    var showSetting = remember {
        mutableStateOf(false)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MT.C().background)
    ) {
        Row(
            Modifier
                .fillMaxWidth()
                .height(48.dp)
                .background(MT.C().background)
                .padding(start = 12.dp, end = 12.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                ChannelUtil.mAppName,
                color = MT.C().onBackground.copy(0.8f),
                style = MT.T().labelSmall
            )
            SpacerWeight()
            ImageApp(
                data = "res/ic_settings.png", modifier = Modifier
                    .size(32.dp)
                    .padding(2.dp)
                    .clickable {
                        showSetting.value = true
                    }, color = MT.C().onBackground.copy(0.8f)
            )
        }

        content.invoke()
    }

    Setting(display = showSetting)
}
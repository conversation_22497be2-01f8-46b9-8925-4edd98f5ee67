package com.tinypretty.ui.componets

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tinypretty.ui.theme.C
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.S
import com.tinypretty.ui.theme.T
import com.tinypretty.ui.theme.clip
import com.tinypretty.util.EmojiUtils
import java.text.DecimalFormat

@Composable
fun AnimateWrap(
    content: @Composable () -> Unit
) {
    content.invoke()
}

@Composable
fun AppAnimateText(
    title: String,
    modifier: Modifier = Modifier.padding(6.dp),
    animateEnable: Boolean = true,
    style: TextStyle = MT.T().labelSmall,
    content: @Composable (title: String) -> Unit = { title ->
        Text(
            modifier = modifier,
            text = title,
            style = style,
            color = MT.C().onPrimary.copy(0.8f)
        )
    },
) {
    AnimateWrap {
        Box(
            modifier = modifier
                .alpha(if (animateEnable) scaleAnimate(targetValue = 0.5f, repeatCount = 5) else 1f)
                .padding(2.dp)
                .background(
                    color = MT.C().primary,
                    shape = MT.S().large,
                )

//                .border(1.dp, MT.C().onPrimary.copy(0.8f), shape = MT.S().large)
        ) {
            content.invoke(title)
        }
    }
}

@Composable
fun AppTextButton(
    msg: String, modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    TextButton(onClick = onClick, modifier) {
        Text(text = msg)
    }
}

@Composable
fun AppTextTiny(
    msg: String,
    color: androidx.compose.ui.graphics.Color = androidx.compose.ui.graphics.Color.Unspecified,
    modifier: Modifier = Modifier,
    maxLines: Int = 100
) {
    Text(
        text = msg,
        color = color,
        style = MT.T().labelSmall,
        modifier = modifier,
        maxLines = maxLines
    )
}

val ButtonContentPaddingZero = PaddingValues(
    start = 0.dp,
    top = 0.dp,
    end = 0.dp,
    bottom = 0.dp
)

inline fun String.display(boolean: Boolean) = if (boolean) this else ""

inline fun Int?.fairWhenOverZero() = this.fair(valid = { it > 0 })

inline fun Long?.fairWhenOverZero() = this.fair(valid = { it > 0L })

inline fun <T> T?.fair(
    invalidValue: HashSet<T> = hashSetOf(),
    valid: (content: T) -> Boolean = { content ->
        true
    }
): T? {
    if (this == null) {
        return null
    }

    if (invalidValue.contains(this)) {
        return null
    }
    if (!valid.invoke(this))
        return null

    return this
}


inline fun String.fair(
    invalidValue: HashSet<String> = hashSetOf(),
    valid: (content: String) -> Boolean = { content ->
        true
    }
): String? {
    if (this == null || this.isBlank() || this == "null") {
        return null
    }

    if (invalidValue.contains(this)) {
        return null
    }
    if (!valid.invoke(this))
        return null

    return this
}

@Composable
fun Spacer(dpValue: Int) {
    Spacer(modifier = Modifier.size(dpValue.dp))
}
@Composable
fun Spacer(dp: Dp) {
    Spacer(modifier = Modifier.size(dp))
}

@Composable
fun Int.SpacerFix(modifier: Modifier = Modifier) {
    Spacer(modifier = modifier.size(this.dp))
}

fun String.formatToValidFileName(): String {
    return getValidAndroidFileName(this)

    var result = this.replace(Regex("""["*/:<>?\\|]+""")) { "" }
    result = EmojiUtils.replaceEmoji(result)
//    "\\s*|\t|\r|\n"
    result = result.replace(Regex("[\\s*|\t|\r|\n]+"), " ")

    if (result.isNullOrBlank()) {
        result = "no_name"
    }

    return result.let {
        if (it.length > 60) it.substring(0, 60) else it
    }

//    return result.let {
//        var byte = it.toByteArray()
//        String(if (byte.size > 20) byte.copyOfRange(0,20) else byte)
////        mL.d { "validFileName = $result" }
//    }
}


fun getValidAndroidFileName(input: String): String {
    val invalidChars =
        listOf('/', '\n', '\r', '\t', '\u0000', '\\', ':', '*', '?', '\"', '<', '>', '|')
    val invalidNames = listOf(".", "..")

    // !! Android文件名的最大长度为255个字节
    // 英文字母一个字母一字节  ， 阿拉伯一个字2字节，中文1个字三字节
    val maxLength = 230

    // 去除字符串的前导和尾随空格
    var fileName = input.trim()

    // 将文件名中的非法字符替换为空格
    fileName = fileName.replace(Regex("[${invalidChars.joinToString(separator = "")}]"), " ")

    // 去除文件名中的多个空格
    fileName = fileName.replace(Regex("\\s+"), " ")

    // 如果文件名是一个非法名称，则用"_"代替
    if (fileName in invalidNames) {
        fileName = "_"
    }

    // 如果文件名长度大于最大长度，则截取前面的部分，并在结尾添加"_"

    // 将字符串编码为UTF-8，以保留所有字符


    val stringBuilder = StringBuilder()

    fileName.forEach {
        if (stringBuilder.toString().toByteArray().size < maxLength) {
            stringBuilder.append(it)
        }
    }
    // 返回最终的文件名
    return stringBuilder.toString()
}


fun Long.sizePretty(): String {
    var fileSize = this
    val df = DecimalFormat("#.00")
    var fileSizeString = ""
    val wrongSize = "0B"
    if (fileSize == 0L) {
        return wrongSize
    }
    if (fileSize < 1024) {
        fileSizeString = df.format(fileSize.toDouble()) + "B"
    } else if (fileSize < 1048576) {
        fileSizeString = df.format(fileSize.toDouble() / 1024) + "KB"
    } else if (fileSize < 1073741824) {
        fileSizeString = df.format(fileSize.toDouble() / 1048576) + "MB"
    } else {
        fileSizeString = df.format(fileSize.toDouble() / 1073741824) + "GB"
    }
    return fileSizeString
}

@Composable
fun ColumnScope.SpacerWeight() {
    Spacer(modifier = Modifier.weight(1f))
}

@Composable
fun SpacerFillMaxWidth() {
    Spacer(modifier = Modifier.fillMaxWidth())
}

@Composable
fun RowScope.SpacerWeight() {
    Spacer(modifier = Modifier.weight(1f))
}


@Composable
fun FullWidthMiddleButton(text: String, padding: Int = 6, onClick: () -> Unit = {}) {
    Box(
        Modifier
            .fillMaxWidth()
            .background(Color.Black.copy(0.6f))
            .padding(padding.dp), contentAlignment = Alignment.Center
    ) {
        MiddleButton(text = text) {
            onClick.invoke()
        }
    }
}

@Composable
fun MiddleButton(
    text: String, color: Color = MT.C().onPrimary,
    backgroundColor: Color = MT.C().primary, onClick: () -> Unit
) {
    BaseButton(
        text = text,
        color = color,
        backgroundColor = backgroundColor,
        style = MT.T().bodySmall,
        paddingValues = PaddingValues(12.dp, 6.dp)
    ) { onClick.invoke() }
}

@Composable
fun BigButton(
    text: String, color: Color = MT.C().onPrimary,
    backgroundColor: Color = MT.C().primary, onClick: () -> Unit
) {
    BaseButton(
        text = text,
        color = color,
        backgroundColor = backgroundColor,
        style = MT.T().labelSmall,
        paddingValues = PaddingValues(12.dp, 6.dp)
    ) { onClick.invoke() }
}

@Composable
fun SmallButton(text: String, onClick: () -> Unit) {
    BaseButton(text = text) { onClick.invoke() }
}


@Composable
fun BaseButton(
    text: String,
    paddingValues: PaddingValues = PaddingValues(6.dp, 2.dp),
    color: Color = MT.C().onPrimary,
    backgroundColor: Color = MT.C().primary,
    style: TextStyle = MT.T().labelSmall,
    onClick: () -> Unit = {}
) {
    Text(
        text = text,
        color = color,
        style = style,
        modifier = Modifier
            .clip()
            .background(backgroundColor)
            .padding(paddingValues)
            .clickable {
                onClick.invoke()
            }
    )
}

@Composable
fun AppButton(
    msg: String, iconSrc: Any? = null, enabled: Boolean = true, modifier: Modifier = Modifier
        .fillMaxWidth()
        .padding(10.dp),
    maxLines: Int = 1,
    contentPadding: PaddingValues = ButtonDefaults.ContentPadding,
    onClick: () -> Unit
) {
    Button(
        onClick = { onClick.invoke() },
        contentPadding = contentPadding,
        enabled = enabled, shape = MT.S().large, modifier = modifier
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Text(
                text = msg,
                style = MT.T().labelSmall,
                maxLines = maxLines,
                color = MT.C().onPrimary,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(top = 8.dp, bottom = 8.dp),
            )
            if (iconSrc != null) {
                ImageApp(
                    data = iconSrc,
                    modifier = Modifier.height(32.dp),
                    color = MT.C().onPrimary,
                    contentScale = ContentScale.Fit
                )
            }
        }
    }
}

@Composable
fun AppOutLinedButton(msg: String, modifier: Modifier = Modifier, onClick: () -> Unit) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(10.dp)
    ) {
        OutlinedButton(onClick = { onClick.invoke() }, shape = MT.S().large) {
            Text(
                text = msg,
                style = MT.T().titleMedium,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(10.dp),
            )
        }
    }
}

package com.tinypretty.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Snackbar
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tinypretty.ui.componets.SpacerWeight
import com.tinypretty.ui.dialogs.closeButton
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clipBorder

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeedbackScreen(display: MutableState<Boolean>) {
    if (!display.value) return

    val feedbackText = remember { mutableStateOf("") }
    val showSnackbar = remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .clipBorder()
            .background(MT.color.background)
            .padding(16.dp)
    ) {
        OutlinedTextField(
            value = feedbackText.value,
            onValueChange = { feedbackText.value = it },
            label = { Text("请输入您的反馈") }
        )

        Button(
            onClick = {
                // Handle the submit action here
                // For now, we just show a snackbar
                showSnackbar.value = true
            },
            modifier = Modifier.padding(top = 16.dp)
        ) {
            Text("提交")
        }
        SpacerWeight()
        Button(
            onClick = {
                // Handle the submit action here
                // For now, we just show a snackbar
                display.value = false
            },
            modifier = Modifier.padding(top = 16.dp)
        ) {
            Text("关闭")
        }
        if (showSnackbar.value) {
            Snackbar(
                modifier = Modifier.padding(top = 16.dp),
                action = {
                    closeButton(modifier = Modifier
                        .padding(16.dp)
                        .clickable {
                            showSnackbar.value = false
                            display.value = false
                        })
                }
            ) {
                Text("谢谢您的反馈")
            }
        }
    }
}
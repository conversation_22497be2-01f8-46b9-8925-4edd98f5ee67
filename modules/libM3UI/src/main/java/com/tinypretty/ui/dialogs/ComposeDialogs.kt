@file:OptIn(ExperimentalMaterial3Api::class)

package com.tinypretty.ui.dialogs

//import com.tinypretty.ui.shop.ShopUtil
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.ZeroCornerSize
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.TextFieldDefaults.indicatorLine
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.google.accompanist.flowlayout.FlowRow
import com.google.accompanist.flowlayout.MainAxisAlignment
import com.google.accompanist.flowlayout.SizeMode
import com.tiny.compose.ui.R
import com.tiny.domain.util.ChannelUtil
import com.tiny.domain.util.daySecond
import com.tiny.domain.util.enabledAfterShortTime
import com.tiny.domain.util.runThenFreeze
import com.tiny.domain.util.screenShortSplitDp
import com.tinypretty.component.IntentTools
import com.tinypretty.component.KoinTools
import com.tinypretty.component.ResTools
import com.tinypretty.component.toArray
import com.tinypretty.component.toArrayString
import com.tinypretty.ui.componets.AnimateWrap
import com.tinypretty.ui.componets.AppButton
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.Spacer
import com.tinypretty.ui.componets.ad.banner
import com.tinypretty.ui.componets.scaleAnimate
import com.tinypretty.ui.dialogs.rattingbar.RatingBar
import com.tinypretty.ui.dialogs.rattingbar.RatingBarStyle
import com.tinypretty.ui.dialogs.rattingbar.StepSize
import com.tinypretty.ui.navigation.NavHostControllerHolder
import com.tinypretty.ui.player.mAnalysis
import com.tinypretty.ui.theme.*

val mSP by KoinTools.injectPreferencesMgr()
val mL by KoinTools.injectLog("fiveStar")

@Composable
fun TitleBlock(
    title: String, color: Color = MT.C().background, modifier: Modifier = Modifier, content: @Composable () -> Unit
) {
    Column(
        modifier.fillMaxWidth()
    ) {
        Row(
            Modifier
                .alpha(MT.alpha.deep())
                .padding(top = MT.pd.gap())
        ) {
            Spacer(Modifier.size(MT.pd.small()))

            Box(
                modifier = Modifier
                    .size(6.dp)
                    .clip(CircleShape)
                    .align(Alignment.CenterVertically)
                    .background(
                        color = color,
                    )
            )
            Spacer(Modifier.size(MT.pd.mid()))
            Text(
                text = title, color = color, style = MT.T().labelSmall
            )
        }
        Spacer(Modifier.size(MT.pd.small()))
        content.invoke()
    }

}

@Composable
fun titleBar(
    title: String, backable: Boolean = true, onBackClick: () -> Unit = { NavHostControllerHolder.back() }
) {
    Box(
        contentAlignment = Alignment.CenterStart, modifier = Modifier
            .fillMaxWidth()
            .background(MT.C().primary)
            .padding(start = 2.dp, top = 6.dp, bottom = 6.dp, end = 2.dp)
    ) {
        if (backable) {
            ImageApp(data = "res/ic_back.png", color = MT.C().onPrimary, modifier = Modifier
                .size(36.dp)
                .padding(6.dp)
                .clickable {
                    onBackClick.invoke()
                })
        }

        Text(
            text = title, color = MT.C().onPrimary, style = MT.T().labelSmall, modifier = Modifier
                .align(Alignment.Center)
                .padding(start = 36.dp, end = 36.dp)
        )
    }
}

@Composable
fun titleBarWhite(
    title: String, backable: Boolean = true, onBackClick: () -> Unit = { NavHostControllerHolder.back() }
) {
    Box(
        contentAlignment = Alignment.CenterStart, modifier = Modifier
            .fillMaxWidth()
            .background(MT.C().background)
            .padding(start = 2.dp, top = 6.dp, bottom = 6.dp, end = 2.dp)
    ) {
        if (backable) {
            ImageApp(data = "res/ic_back.png", color = MT.C().onBackground, modifier = Modifier
                .size(36.dp)
                .padding(6.dp)
                .clickable {
                    onBackClick.invoke()
                })
        }

        Text(
            text = title, color = MT.C().onBackground, style = MT.T().titleSmall, modifier = Modifier.align(Alignment.Center)
        )
    }
}

@Composable
fun RowScope.backButton(
    onClick: () -> Unit = {
        NavHostControllerHolder.back()
    }
) {
    ImageApp(data = "res/ic_back.png", color = MT.C().onPrimary, modifier = Modifier
        .align(alignment = Alignment.CenterVertically)
        .size(36.dp)
        .padding(6.dp)
        .clickable {
            onClick.invoke()
        })
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlertInputSimple(
    mutableState: MutableState<Boolean>, default: String, onDone: (value: String) -> Unit
) {
    alertContent(state = mutableState) {
        mL.d { "AlertInputSimple$default" }
        var inputValue = rememberSaveable { mutableStateOf(default) }
        closeableDlg(contentState = mutableState) {
            Column(
                Modifier
                    .wrapContentHeight()
                    .verticalGradientBackground()
                    .padding(6.dp), verticalArrangement = Arrangement.Center
            ) {
                TextField(value = inputValue.value, onValueChange = { newText ->
                    inputValue.value = newText.trimStart()
                }, isError = false, colors = TextFieldDefaults.textFieldColors(focusedLabelColor = MT.C().onBackground), modifier = Modifier
                    .clipBorder()
                    .fillMaxWidth()
                    .background(MT.C().background.copy(0.9f))
                    .wrapContentHeight(), trailingIcon = {
                    ImageApp(
                        data = "res/ic_done.png",
                        Modifier
                            .size(48.dp)
                            .padding(16.dp)
                            .clickable {
                                onDone.invoke(inputValue.value)
                                mutableState.value = false
                            }, color = MT.C().onBackground
                    )
                })
                banner("alert_input")
            }
        }
    }
}

@Composable
fun searchButton(
    historyKey: String = "search_histroy_list", color: Color = MT.C().onPrimary, defaultKeywords: ArrayList<String> = arrayListOf(), onDone: (value: String) -> Unit
) {
    val inputState = remember { mutableStateOf(false) }
    if (inputState.value) {
        val lastInputList: ArrayList<String> = mSP.getString(historyKey, "").toArray()


        val keywords = arrayListOf<String>().apply {
            addAll(lastInputList)
            addAll(defaultKeywords)
        }
        val lastInput = lastInputList.getOrElse(0) {
            if (keywords.isEmpty()) "" else keywords.random()
        }

        AlertInput(mutableState = inputState, default = lastInput, keywords = keywords, clearHistory = {
            mSP.putString(historyKey, "")
        }) {
            var searchValue = it.trim()
            if (searchValue.isNotBlank()) {
                if (!defaultKeywords.contains(searchValue)) {
                    lastInputList.removeAll {
                        it == searchValue
                    }
                    lastInputList.add(0, searchValue)
                }

                mSP.putString(historyKey, lastInputList.toArrayString())
                mAnalysis.onEvent("shop_search", it)
                onDone.invoke(it)
            }
        }
    }

    ImageApp(data = "res/ic_search.png", color = color, modifier = Modifier
        .padding(12.dp)
        .background(shape = CircleShape, color = MT.C().background)
        .size(28.dp)
        .padding(6.dp)
        .clickable {
            inputState.value = true
        })
}

@Composable
fun CloseableDlgFullScreen(
    contentState: MutableState<Boolean>, modifier: Modifier = Modifier.background(
        color = MT.C().onBackground.copy(0.5f)
    ), borderWidth: Dp = 1.dp, adPlace: String = "", onClose: () -> Unit = {}, content: @Composable () -> Unit
) {
    if (!contentState.value) return
    Column {
        Box(
            modifier = modifier.weight(1f), contentAlignment = Alignment.TopEnd
        ) {
            BackHandler() {
                contentState.value = false
                onClose.invoke()
            }
            Box(
                Modifier
                    .padding(start = 6.dp, end = 6.dp, top = 6.dp, bottom = 6.dp)
                    .border(
                        width = borderWidth, color = MT.C().background.copy(0.8f), shape = RoundedCornerShape(12.dp)
                    )
                    .wrapContentHeight()
                    .clip(RoundedCornerShape(12.dp)),
            ) {
                content.invoke()
            }

            closeButton(modifier = Modifier
                .clickable {
                    contentState.value = false
                    onClose.invoke()
                }
                .align(Alignment.TopStart))
        }
        if (adPlace.isNotBlank()) {
            banner(place = adPlace)
        }
    }
}

@Composable
fun closeableDlg(
    contentState: MutableState<Boolean>, modifier: Modifier = Modifier
        .background(
            color = MT.C().onBackground.copy(0.8f)
        )
        .padding(bottom = 12.dp), borderWidth: Dp = 1.dp, adPlace: String = "", clickSpaceToClose: Boolean = false, onClose: () -> Unit = { contentState.value = false }, content: @Composable (BoxScope) -> Unit
) {
    if (!contentState.value) return

    Column(
        modifier = modifier
            .fillMaxSize()
            .clickable(clickSpaceToClose) {
                onClose.invoke()
            }, verticalArrangement = Arrangement.Center, horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            contentAlignment = Alignment.TopEnd
        ) {
            BackHandler() {
                contentState.value = false
                onClose.invoke()
            }
            Box(
                Modifier
                    .padding(start = 6.dp, end = 6.dp, top = 6.dp, bottom = 16.dp)
                    .border(
                        width = borderWidth, color = MT.C().background.copy(0.8f), shape = RoundedCornerShape(12.dp)
                    )
                    .wrapContentHeight()
                    .clip(RoundedCornerShape(12.dp)),
            ) {
                content.invoke(this)
            }

            closeButton(modifier = Modifier
                .clickable {
                    contentState.value = false
                    onClose.invoke()
                }
                .align(Alignment.BottomCenter))
        }

        if (adPlace.isNotBlank()) {
            banner(place = adPlace)
        }
    }
}

@Composable
fun closeButton(
    size: Int = 36, iconColor: Color? = MT.C().onBackground.copy(0.8f), modifier: Modifier = Modifier
) {
    circleButton(
        data = "res/ic_close_ad.png", iconColor = iconColor, modifier = modifier, size = size
    )
}

@Composable
fun BoxHintWrap(
    key: String, value: String, jumpWhenNeverClicked: Boolean = false, alwaysShowHint: Boolean = false, modifier: Modifier = Modifier, onClick: () -> Unit, content: @Composable () -> Unit
) {
    val clicked = mSP.getString(key) == value
    val needShowHint = (!clicked || alwaysShowHint) && value.isNotEmpty()

    val jumpAnimate = if (needShowHint) scaleAnimate(initialValue = 0f, targetValue = 1f) else 0f
    Box(
        modifier
            .graphicsLayer {
                if (jumpWhenNeverClicked && !clicked) {
                    translationY = jumpAnimate * 30
                }
            }
            .clickable {
                onClick.invoke()
                mSP.putString(key, value)
            }
            .wrapContentSize(), contentAlignment = Alignment.TopEnd) {
        content.invoke()
        if (needShowHint) {
            Text(
                text = value,
                Modifier
                    .background(
                        color = if (clicked) MT.C().onBackground else MT.C().primary, shape = MT.S().large
                    )
                    .padding(start = 3.dp, end = 3.dp), color = MT.C().onPrimary, textAlign = TextAlign.Center, style = MT.T().labelSmall
            )
        }
    }
}


@Composable
fun BoxHintBottomWrap(
    key: String, value: String, alwaysShowHint: Boolean = false, onClick: () -> Unit, modifier: Modifier = Modifier, content: @Composable () -> Unit
) {
    var needShowHint = mSP.getString(key) != value || alwaysShowHint
    Box(
        modifier
            .clickable {
                onClick.invoke()
                mSP.putString(key, value)
            }
            .wrapContentSize(), contentAlignment = Alignment.BottomCenter) {
        content.invoke()
        if (value.isNotEmpty() && needShowHint) {
            Text(
                text = value,
                Modifier
                    .background(color = MT.C().primary, shape = MT.S().medium)
                    .padding(start = 6.dp, end = 6.dp, top = 0.dp, bottom = 2.dp)
                    .scale(0.9f), color = MT.C().onError, textAlign = TextAlign.Center, style = MT.T().labelSmall
            )
        }
    }
}

@Composable
fun circleButton(
    data: Any, size: Int = 36, contentPadding: Int = 2, backgroundColor: Color = MT.C().background, shadowWidth: Int = 5, iconColor: Color? = MT.C().onBackground.copy(0.8f), modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(size.dp)
//            .clip
            .shadow(shadowWidth.dp, shape = CircleShape)
            .background(
                color = backgroundColor, shape = CircleShape
            )
            .padding(contentPadding.dp), contentAlignment = Alignment.Center
    ) {
        ImageApp(
            data = data, gif = true, modifier = Modifier
                .size((size - contentPadding - 4).dp)
                .align(Alignment.Center), contentScale = ContentScale.Fit, color = iconColor
        )
    }
}


@Composable
fun closeableDlgTopRight(
    contentState: MutableState<Boolean>, modifier: Modifier = Modifier, onClose: () -> Unit = {}, content: @Composable () -> Unit
) {
    if (!contentState.value) return

    Box(
        modifier = modifier.background(
            color = MT.C().onBackground.copy(0.5f)
        ), contentAlignment = Alignment.TopEnd
    ) {
        BackHandler() {
            contentState.value = false
        }
        Surface(
            Modifier
                .padding(start = 12.dp, end = 12.dp, top = 12.dp)
                .fillMaxSize(), shape = RoundedCornerShape(topStart = 6.dp, topEnd = 6.dp)
        ) {
            content.invoke()
        }
        Box(
            Modifier
                .padding(2.dp)
                .align(Alignment.TopEnd)
        ) {
            closeButton(modifier = Modifier.clickable {
                contentState.value = false
                onClose.invoke()
            })
        }
    }

}

@OptIn(ExperimentalComposeUiApi::class, ExperimentalMaterial3Api::class)
@Composable
fun SolidInput(
    inputValueState: MutableState<String>,
    hint: String,
    focused: MutableState<Boolean>,
    textStyle: TextStyle = MT.T().titleMedium,
    onValueChange: (String) -> Unit,
) {
    var fm = LocalFocusManager.current
    if (!focused.value) {
        fm.clearFocus(true)
    }

    var focusRequester = FocusRequester()
    val keyboard = LocalSoftwareKeyboardController.current

    fun refresh() {
        inputValueState.value = ""
        focusRequester.requestFocus()
        keyboard?.show()
    }
    BackHandler() {
        refresh()
    }

    TextField(
        value = inputValueState.value,
        onValueChange = onValueChange,
        isError = false,
        singleLine = true,
        leadingIcon = {
            circleButton(contentPadding = 6, data = if (inputValueState.value.isBlank()) "res/ic_search.png" else "res/ic_refresh.png", iconColor = com.tinypretty.ui.theme.MT.C().primary.copy(1F), modifier = androidx.compose.ui.Modifier
                .size(textStyle.fontSize.value.dp * 2)
                .clickable {
                    refresh()
                })
        },
        trailingIcon = {
            Row(Modifier.padding(end = 6.dp)) {
                if (inputValueState.value.isNotBlank()) {
                    if (focused.value) {
                        circleButton(contentPadding = 6, data = "res/ic_done.png", iconColor = com.tinypretty.ui.theme.MT.C().primary.copy(1F), modifier = androidx.compose.ui.Modifier
                            .size(textStyle.fontSize.value.dp * 2)
                            .clickable {
                                fm.clearFocus(true)
                            })

                    }
                }
            }
        },
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Text, imeAction = ImeAction.Done
        ),
        keyboardActions = KeyboardActions(onDone = {
            fm.clearFocus(true)
        }),
        textStyle = textStyle,
        placeholder = {
            Text(
                text = hint, color = MT.C().onPrimary.copy(0.8f), style = textStyle
            )
        },
        colors = TextFieldDefaults.textFieldColors(
            focusedLabelColor = MT.C().onPrimary, containerColor = MT.C().primary, focusedIndicatorColor = MT.C().primary, cursorColor = MT.C().onPrimary,
        ),
        modifier = androidx.compose.ui.Modifier
            .scale(0.9f)
            .onFocusChanged {
                focused.value = it.hasFocus
            }
            .focusRequester(focusRequester)
            .fillMaxWidth()
            .wrapContentHeight()
            .shadow()
            .clip()
            .background(com.tinypretty.ui.theme.MT.C().background),
    )
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SmallTextFile(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    textStyle: TextStyle = LocalTextStyle.current,
    label: @Composable (() -> Unit)? = null,
    placeholder: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    isError: Boolean = false,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions(),
    singleLine: Boolean = false,
    maxLines: Int = 100, // 限制最大行数，避免布局约束问题
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    shape: Shape = MaterialTheme.shapes.small.copy(bottomEnd = ZeroCornerSize, bottomStart = ZeroCornerSize),
    colors: TextFieldColors = TextFieldDefaults.textFieldColors()
) {
    // If color is not provided via the text style, use content color as a default
    val textColor = textStyle.color
    val mergedTextStyle = textStyle.merge(TextStyle(color = textColor))

    (BasicTextField(value = value, modifier = modifier
        .indicatorLine(enabled, isError, interactionSource, colors)
        .defaultMinSize(
            minWidth = TextFieldDefaults.MinWidth, minHeight = TextFieldDefaults.MinHeight
        ), onValueChange = onValueChange, enabled = enabled, readOnly = readOnly, textStyle = mergedTextStyle, visualTransformation = visualTransformation, keyboardOptions = keyboardOptions, keyboardActions = keyboardActions, interactionSource = interactionSource, singleLine = singleLine, maxLines = maxLines, decorationBox = @Composable { innerTextField ->
        // places leading icon, text field with label and placeholder, trailing icon
        TextFieldDefaults.TextFieldDecorationBox(
            value = value, visualTransformation = visualTransformation, innerTextField = innerTextField, placeholder = placeholder, label = label, leadingIcon = leadingIcon, trailingIcon = trailingIcon, singleLine = singleLine, enabled = enabled, isError = isError, interactionSource = interactionSource, colors = colors
        )
    }))
}

@Composable
fun smallInput(
    default: String, keywords: List<String> = arrayListOf<String>(), label: String, onDone: (value: String) -> Unit
) {
    var inputValue = rememberSaveable { mutableStateOf(default) }
    Column(
        Modifier
            .wrapContentHeight()
            .padding(0.dp), verticalArrangement = Arrangement.Center
    ) {
        SmallTextFile(value = inputValue.value, onValueChange = { newText ->
            inputValue.value = newText.trimStart()
            onDone.invoke(inputValue.value)
        }, isError = false, colors = TextFieldDefaults.textFieldColors(
            focusedLabelColor = MT.C().onPrimary, containerColor = MT.C().primary.copy(0.8f)
        ), shape = RoundedCornerShape(12.dp), modifier = Modifier
            .padding(0.dp)
            .fillMaxWidth()
            .background(MT.C().background.copy(0.9f))
            .wrapContentHeight(), label = {
            Text(
                text = label,
                color = MT.C().onPrimary.copy(0.6f), style = MT.T().labelSmall,
            )
        })
//            Spacer(dpValue = 6)
//            FlowRow(
//                modifier = Modifier.padding(3.dp),
//                mainAxisAlignment = MainAxisAlignment.Start,
//                mainAxisSize = SizeMode.Expand,
//                crossAxisSpacing = 3.dp,
//                mainAxisSpacing = 3.dp
//            ) {
//                keywords.forEach {
//                    Text(
//                        text = it,
//                        color = MT.C().onBackground.copy(0.8f),
//                        style = MT.T().labelSmall,
//                        modifier = Modifier
//                            .clickable {
//                                onDone.invoke(it)
//                            }
//                            .clip()
//                            .background(MT.C().background.copy(0.8f))
//                            .padding(6.dp)
//                    )
//                }
//            }
//            Spacer(dpValue = 6)
//            banner("alert_input")
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlertInput(
    mutableState: MutableState<Boolean>, default: String, keywords: List<String> = arrayListOf<String>(), clearHistory: () -> Unit = {}, onDone: (value: String) -> Unit
) {
    val inputValue = rememberSaveable { mutableStateOf(default) }
    alertContent(state = mutableState) {
        Column(
            Modifier
                .wrapContentSize()
                .verticalGradientBackground()
                .padding(6.dp), verticalArrangement = Arrangement.Center
        ) {
            TextField(value = inputValue.value, onValueChange = { newText ->
                inputValue.value = newText.trimStart()
            }, colors = TextFieldDefaults.textFieldColors(), modifier = Modifier
                .clipBorder()
                .fillMaxWidth()
                .background(MT.C().background.copy(0.9f))
                .wrapContentHeight(), label = {
                Text(
                    text = "填写",
                    color = MT.C().onSurface.copy(0.8f),
                )
            })
            Spacer(dpValue = 6)
            FlowRow(
                modifier = Modifier.padding(3.dp), mainAxisAlignment = MainAxisAlignment.Start, mainAxisSize = SizeMode.Expand, crossAxisSpacing = 3.dp, mainAxisSpacing = 3.dp
            ) {
                keywords.forEach {
                    Text(text = it, color = MT.C().onBackground.copy(0.8f), style = MT.T().labelSmall, modifier = Modifier
                        .clickable {
                            onDone.invoke(it)
                            mutableState.value = false
                        }
                        .clip()
                        .background(MT.C().background.copy(0.8f))
                        .padding(6.dp))
                }
            }
            Spacer(dpValue = 6)
            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(modifier = Modifier.weight(1f)) {
                    AppButton(msg = "完成") {
                        onDone.invoke(inputValue.value)
                        mutableState.value = false
                    }
                }

                Icon(
                    Icons.Filled.Delete,
                    contentDescription = "delete",
                    modifier = Modifier
                        .padding(6.dp)
                        .size(32.dp)
                        .clickable {
                            clearHistory.invoke()
                            mutableState.value = false
                        },
                    tint = MT.color.primary
                )

            }

            Spacer(dpValue = 6)
            banner("alert_input")
        }
    }
}

@Composable
fun AlertCloseAbleContent(
    state: MutableState<Boolean>, modifier: Modifier = Modifier
        .fillMaxHeight()
        .width(screenShortSplitDp(1f).dp), onDismiss: () -> Unit = {}, content: @Composable BoxScope.() -> Unit
) {

    alertContent(state = state, modifier = modifier, onDismiss = onDismiss) {
        closeableDlg(contentState = state) {
            content.invoke(it)
        }
    }


}

@Composable
fun alertContent(
    state: MutableState<Boolean>, modifier: Modifier = Modifier
        .fillMaxHeight()
        .width(screenShortSplitDp(1f).dp), onDismiss: () -> Unit = {}, content: @Composable BoxScope.() -> Unit
) {
    if (!state.value) {
        return
    }
    Dialog(properties = DialogProperties(
        usePlatformDefaultWidth = false
    ), onDismissRequest = {
        state.value = false
        onDismiss.invoke()
    }) {
        Box(modifier = modifier, contentAlignment = Alignment.Center) {
            content.invoke(this)
        }
    }
}

@Composable
fun content(
    state: MutableState<Boolean>, modifier: Modifier = Modifier.fillMaxSize(), onDismiss: () -> Unit = {}, content: @Composable BoxScope.() -> Unit
) {
    if (!state.value) {
        return
    }
    BackHandler {
        state.value = false
        onDismiss.invoke()
    }
    Box(modifier) {
        content.invoke(this)
    }
}

@Composable
fun alertMessage(
    state: MutableState<Boolean>, msg: String, icon: String?, btnString: String, onOk: () -> Unit
) {
    if (!state.value) return

    BackHandler() {
        state.value = false
    }
    Dialog(onDismissRequest = {
        state.value = false
    }) {

        Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            closeableDlg(contentState = state, Modifier.wrapContentHeight(), borderWidth = 0.dp) {
                Column(
                    Modifier
                        .wrapContentHeight()
                        .shadow()
                        .verticalGradientBackground()
                        .align(Alignment.Center), horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    Text(
                        textAlign = TextAlign.Center, style = MT.T().labelSmall, text = msg, color = MT.C().onBackground.copy(1F), modifier = Modifier
                            .fillMaxWidth()
                            .background(MT.C().background.copy(0.6f))
                            .padding(12.dp)
                    )
                    Spacer(dpValue = 24)
                    if (icon != null) {
                        ImageApp(
                            data = icon,
                            Modifier
                                .fillMaxWidth(0.8f)
                                .aspectRatio(1f), contentScale = ContentScale.Fit
                        )
                        Spacer(dpValue = 24)
                    }
                    AnimateWrap {
                        AppButton(
                            modifier = Modifier
                                .fillMaxWidth()
                                .scale(scaleAnimate()),
                            msg = btnString,
                        ) {
                            onOk.invoke()
                            state.value = false
                        }
                    }
                    Spacer(dpValue = 24)
                }
            }
        }
    }
}

@Composable
fun btnFiveStar(
    iconSize: Int = 56,
    alwaysShow: Boolean = false,
    iconColor: Color? = MT.C().onBackground.copy(0.8f),
    btnText: String = "评五星",
) {
    if (isFiveStarted() && !alwaysShow) {
        return
    }

    if (!enabledAfterShortTime() && !alwaysShow) return

    var fiveStartState = remember { mutableStateOf(false) }
    fiveStar(dialogsState = fiveStartState, force = true) { d, g -> }

    Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
        .wrapContentHeight()
        .clickable {
            fiveStartState.value = true
        }) {
        BoxHintBottomWrap(key = "ic_five_star", alwaysShowHint = true, value = btnText, onClick = { fiveStartState.value = true }) {
            circleButton(
                data = "res/ic_five_star.gif", modifier = Modifier
                    .padding(bottom = 6.dp)
                    .size(iconSize.dp), size = iconSize, iconColor = iconColor, contentPadding = 6
            )
        }
//        AppAnimateText(
//            title = "五星好评", modifier = Modifier
//                .alpha(0.95f)
//                .padding(6.dp)
//        )
    }
}

const val FIVE_STARED = "is_five_stared"
fun isFiveStarted() = mSP.getBoolean(FIVE_STARED, false)

@Composable
fun autoFiveStar() {
    var autoShow = remember { mutableStateOf(false) }
    var favStared = isFiveStarted()

    if (favStared) {
        return
    }

    runThenFreeze("autoFiveStar", 1.daySecond()) { it }?.run {
        mL.d { "runThenFreeze count = $this" }
        if (this >= 1) {
            autoShow.value = true
        }
        fiveStar(dialogsState = autoShow, force = false) { _, _ -> }
    }
}

@Composable
fun fiveStar(
    dialogsState: MutableState<Boolean>, force: Boolean, onRate: (score: Int, msg: String?) -> Unit
) {
    var onDismiss = {
        dialogsState.value = false
    }
    if (!force) {
        BackHandler() {
            onDismiss.invoke()
        }
    }

    if (dialogsState.value) {
        var initialRating = 5f
        var rating: Float by remember { mutableStateOf(initialRating) }

        AlertDialog(modifier = Modifier
            .clipBorder()
            .padding(0.dp), containerColor = MT.color.background, title = {
            Row(
                horizontalArrangement = Arrangement.Center, modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = ResTools.str(R.string.home_rate), style = MT.T().titleSmall
                )
            }
        }, text = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = ResTools.str(R.string.home_star).replace("APPNAME", ChannelUtil.mAppName), textAlign = TextAlign.Center, style = MT.T().labelSmall, modifier = Modifier
                        .fillMaxWidth()
                        .align(
                            Alignment.CenterHorizontally
                        )
                )
                Spacer(modifier = Modifier.size(MT.pd.big()))

                ImageApp(
                    data = "res/ic_five_star.gif", gif = true, modifier = Modifier.size(48.dp), contentScale = ContentScale.Crop
                )
                RatingBar(value = rating, ratingBarStyle = RatingBarStyle.Normal, size = MT.img.small(), stepSize = StepSize.ONE, activeColor = MT.C().primary, inactiveColor = MT.C().primary.copy(0.2f), onValueChange = {
                    rating = it
                }) {
                    rating = it
                }
                Spacer(modifier = Modifier.size(MT.pd.big()))

                val context = LocalContext.current

                AnimateWrap {
                    AppButton(
                        msg = ResTools.str(R.string.home_star_continue), modifier = Modifier
                            .scale(scaleAnimate())
                            .fillMaxWidth(), maxLines = 1
                    ) {
                        mSP.putBoolean(FIVE_STARED, true)
                        if (rating >= 4f) {
                            IntentTools.openGP(context)
                        }
                        onDismiss.invoke()
                    }
                }

                Row(
                    modifier = Modifier.fillMaxWidth()
                ) {

                    Text(
                        modifier = Modifier
                            .padding(12.dp)
                            .fillMaxWidth()
                            .clickable {
                                onDismiss.invoke()
                            }, color = MT.C().onSurface.copy(0.5f), style = MT.T().bodySmall, text = ResTools.str(R.string.dlg_later), textAlign = TextAlign.Right
                    )
                }
            }
        }, onDismissRequest = onDismiss, confirmButton = {})
    }
}


@Composable
fun message(
    dialogsState: MutableState<Boolean>, content: @Composable () -> Unit, onDismiss: () -> Unit
) {
    if (!dialogsState.value) return

    Box(contentAlignment = Alignment.Center, modifier = Modifier
        .fillMaxSize()
        .background(MT.C().background.copy(0.8f))
        .clickable {
            onDismiss.invoke()
        }) {
        content.invoke()
    }

    BackHandler() {
        onDismiss.invoke()
    }
}
//
//
//@Composable
//fun exitBottomSheet(sheetContent: @Composable () -> Unit, content: @Composable () -> Unit) {
//    var context = LocalContext.current
//    bottomSheet(sheetContent = {
//        Column() {
//            Row(
//                horizontalArrangement = Arrangement.Center,
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(MT.pd.big())
//            ) {
//                Text(
//                    text = ResTools.str(R.string.dlg_exit),
//                    style = MT.T().titleMedium
//                )
//            }
//            Box(
//                Modifier
//                    .fillMaxWidth()
//                    .weight(1f)
//            ) {
//                mL.d { "sheetContent INIT" }
//                sheetContent.invoke()
//            }
//            AppButton(msg = "退出软件") {
//                context.findActivity()?.finish()
//            }
//        }
//    }) {
//        content.invoke()
//    }
//}
//
//
//@Composable
//fun bottomSheet(sheetContent: @Composable () -> Unit, content: @Composable () -> Unit) {
//    val bottomSheetScaffoldState = rememberModalBottomSheetState(ModalBottomSheetValue.Hidden)
//    val coroutineScope = rememberCoroutineScope()
//    var sheetContentVisible = rememberSaveable {
//        mutableStateOf(false)
//    }
//
//    @Composable
//    fun sheepContent() {
//        if (sheetContentVisible.value) {
//            sheetContent.invoke()
//        } else {
//            Text(text = "")
//        }
//    }
//
//    fun switch() {
//        coroutineScope.launch {
//            if (bottomSheetScaffoldState.isVisible) {
//                bottomSheetScaffoldState.hide()
//                sheetContentVisible.value = false
//            } else {
//                bottomSheetScaffoldState.show()
//                sheetContentVisible.value = true
//            }
//        }
//    }
//
//    ModalBottomSheetLayout(
//        content = {
//            content.invoke()
//        },
//        sheetShape = RoundedCornerShape(topStart = 6.dp, topEnd = 6.dp),
//        sheetState = bottomSheetScaffoldState,
//        sheetContent = {
//            sheepContent()
//        }
//    )
//
//    BackHandler() {
//        switch()
//    }
//}
//

@Composable
fun shareApp(title: String?, content: String?) {
//        IntentTools.openGP()
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun showSelect(
    arrayList: List<String>, onDismissRequest: () -> Unit, onClick: (index: Int) -> Unit
) {
//        var onDismiss = {}
    BackHandler() {
        onDismissRequest.invoke()
    }
    AlertDialog(modifier = Modifier.fillMaxWidth(), text = {
        LazyColumn {
            itemsIndexed(arrayList) { index, item ->
                TextButton(

                    modifier = Modifier.fillMaxWidth(), onClick = {
                        onClick.invoke(index)
                    }) {
                    Text(text = item, style = MT.T().titleMedium)
                }
            }
        }
    }, onDismissRequest = onDismissRequest, confirmButton = { })
}
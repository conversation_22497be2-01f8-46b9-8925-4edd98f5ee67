package com.tinypretty.ui.componets

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tiny.domain.util.AssertUtil
import com.tiny.domain.util.mL
import com.tiny.domain.util.screenShortSplitDp
import com.tinypretty.component.arrayList
import com.tinypretty.component.forIndex
import com.tinypretty.component.json
import com.tinypretty.component.str
import com.tinypretty.ui.componets.lottie.AnimatedView
import com.tinypretty.ui.theme.C
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.T
import com.tinypretty.ui.theme.verticalGradientBackground
import kotlinx.coroutines.delay
import org.json.JSONObject

object SimpleLoadingHintHolder {
    var loadView: @Composable (Modifier) -> Unit = {}
}


@Composable
fun SimpleLoadingHint() {
    SimpleLoadingHint(Modifier.fillMaxSize())
}
@Composable
fun SimpleLoadingHint(modifier: Modifier, mutableState: MutableState<Boolean>? = null, size: Dp = 100.dp) {
    if (mutableState?.value == false) {
        return
    }

    val infiniteTransition = rememberInfiniteTransition(label = "")
    val angle = infiniteTransition.animateFloat(
        initialValue = 0F,
        targetValue = 360F,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ), label = ""
    )

    Icon(
        Icons.Filled.Refresh,
        contentDescription = "loading",
        modifier = modifier
            .size(size)
            .rotate(angle.value)
    )
}

@Composable
fun LoadingScreen(
    modifier: Modifier = Modifier
        .fillMaxSize()
        .verticalGradientBackground(),
    padding: Dp = 80.dp,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .fillMaxSize()
            .padding(vertical = 60.dp)
    ) {
        SimpleLoadingHint(
            modifier = Modifier.size(100.dp),
            size = 100.dp
        )
    }
}


@Composable
fun LoadingScreen(
    isLoading: MutableState<Boolean>,
    iconID: Int,
    loadingID: Int,
    modifier: Modifier = Modifier
        .fillMaxSize()
        .verticalGradientBackground(),
    wikiJson: String
) {
    if (isLoading.value) {
        var dataAll =
            AssertUtil().absolute(wikiJson).readAssets().json().arrayList<JSONObject>("items")
        var index = remember { mutableStateOf(0) }
        mL.d { "dataAll = ${dataAll.first().toString()}" }
        Column(
            modifier,
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            ImageApp(
                data = iconID,
                modifier = Modifier
                    .height(screenShortSplitDp(0.5f).dp)
                    .aspectRatio(1f),
                contentScale = ContentScale.Crop
            )

            Spacer(dpValue = 24)

            dataAll.getOrNull(index.value)?.let {
                Text(
                    text = it.str("name") + "加载中\n请稍后 (${index.value}/${dataAll.size})",
                    color = MT.C().onPrimary.copy(0.9f),
                    textAlign = TextAlign.Center,
                    style = MT.T().bodySmall
                )
            }
            AnimatedView(resource = loadingID, modifier = Modifier.size(120.dp), padding = 0.dp)
        }
        LaunchedEffect(key1 = wikiJson) {
            while (true) {
                index.value += 1
                delay(100)
            }
        }
    }
}

@Composable
fun loadingHint(modifier: Modifier, loading: Int) {
    AnimatedView(
        resource = loading,
        modifier = modifier,
        padding = 0.dp
    )
}

@Composable
fun LoadingText(modifier: Modifier, color: Color = Color.Black.copy(0.3f)) {
    var count = scaleAnimate(true, 0f, 4f, durationMillis = 1000)
    var str = ""
    count.toInt().forIndex {
        str += " ."
    }
    Box(modifier = modifier) {
        Text(
            text = "Loading$str",
            modifier = Modifier.align(Alignment.Center),
            style = MT.T().titleMedium,
            color = color
        )
    }

}

@Composable
fun LoadingScreen(
    isLoading: MutableState<Boolean>,
    modifier: Modifier = Modifier,
    padding: Dp = 80.dp
) {
    if (isLoading.value) {
        LoadingScreen(modifier, padding = padding)
    }
}

@Composable
fun LoadingScreen(
    loading: Int,
    isLoading: MutableState<Boolean>,
    modifier: Modifier = Modifier
        .fillMaxSize()
        .verticalGradientBackground(),
    wikiJson: String
) {
    if (isLoading.value) {
        var dataAll =
            AssertUtil().absolute(wikiJson).readAssets().json().arrayList<JSONObject>("items")
        var index = remember { mutableStateOf(0) }
        mL.d { "dataAll = ${dataAll.first().toString()}" }
        Column(
            modifier,
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
//            ImageApp(
//                data = ResTools.appIconId,
//                modifier = Modifier
//                    .height(screenShortSplitDp(0.5f).dp)
//                    .aspectRatio(1f),
//                contentScale = ContentScale.Crop
//            )

            Spacer(dpValue = 24)

            dataAll.getOrNull(index.value)?.let {
                Text(
                    text = it.str("name") + "加载中\n请稍后 (${index.value}/${dataAll.size})",
                    color = MT.C().onPrimary.copy(0.9f),
                    textAlign = TextAlign.Center,
                    style = MT.T().bodySmall
                )
            }
            AnimatedView(resource = loading, modifier = Modifier.size(120.dp), padding = 0.dp)
        }
        LaunchedEffect(key1 = wikiJson) {
            while (true) {
                index.value += 1
                delay(100)
            }
        }
    }
}
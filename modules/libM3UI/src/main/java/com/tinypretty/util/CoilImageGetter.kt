package com.tinypretty.util


import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.text.Html
import android.widget.TextView
import coil.Coil
import coil.ImageLoader
import coil.request.ImageRequest
import com.tiny.compose.ui.R
import com.tiny.domain.util.screenShort
import com.tinypretty.component.ResTools
import com.tinypretty.ui.componets.app

/**
 * An [Html.ImageGetter] implementation that uses Coil to load images.
 * @param textView the [TextView] which will receive the formatted HTML
 * @param imageLoader Allows you to specify your own imageLoader
 * @param sourceModifier Allows you to modify the source (typically a URL) of the image before it
 * is loaded as a drawable. This can be used to take an image that has path references such as
 * "images/cat.png" and fully resolve the path to a URL that can be loaded successfully via Coil.
 */
open class CoilImageGetter(
    private val textView: TextView,
    private val imageLoader: ImageLoader = Coil.imageLoader(textView.context),
    private val sourceModifier: ((source: String) -> String)? = null
) : Html.ImageGetter {
    var iconHeight = 48

    fun getAssertDrawable(url: String, iconHeight: Int): Drawable? {
        if (url.startsWith("http")) {
            return null
        }
        var result: Drawable? = null
        try {
            result = Drawable.createFromStream(app.applicationContext.assets.open(url), null)
                ?.apply {
                    var w = iconHeight
                    setBounds(0, 0, w, w * intrinsicWidth / intrinsicHeight)
                }
        } catch (e: Exception) {
        }
        return result

    }

    override fun getDrawable(source: String?): Drawable {
        if (source == null)
            return ResTools.drawable(R.drawable.question)

        var d = getAssertDrawable(source, iconHeight)
        if (d != null)
            return d

        val finalSource = sourceModifier?.invoke(source) ?: source

        val drawablePlaceholder = DrawablePlaceHolder()
        imageLoader.enqueue(ImageRequest.Builder(textView.context).data(finalSource).apply {
            target { drawable ->
                drawablePlaceholder.updateDrawable(drawable)
                // invalidating the drawable doesn't seem to be enough...
                textView.text = textView.text
            }
        }.build())
        // Since this loads async, we return a "blank" drawable, which we update
        // later
        return drawablePlaceholder
    }

    @Suppress("DEPRECATION")
    private class DrawablePlaceHolder : BitmapDrawable() {

        private var drawable: Drawable? = null

        override fun draw(canvas: Canvas) {
            drawable?.draw(canvas)
        }

        fun updateDrawable(drawable: Drawable) {
            this.drawable = drawable

            // 获取原始尺寸
            var width = drawable.intrinsicWidth
            var height = drawable.intrinsicHeight

            // 设置最大宽度为屏幕宽度的一半，防止图片过大
            val maxWidth = screenShort / 2

            // 设置最大高度，防止图片过高导致布局问题
            val maxHeight = 2000

            // 如果宽度超过最大宽度，按比例缩小
            if (width > maxWidth) {
                val ratio = maxWidth.toFloat() / width.toFloat()
                width = maxWidth
                height = (height * ratio).toInt()
            }

            // 如果高度仍然超过最大高度，再次按比例缩小
            if (height > maxHeight) {
                val ratio = maxHeight.toFloat() / height.toFloat()
                height = maxHeight
                width = (width * ratio).toInt()
            }

            // 确保最小尺寸
            if (width < 48) {
                width = 48
                height = width * drawable.intrinsicHeight / drawable.intrinsicWidth
            }

            // 确保尺寸不超过Compose布局系统的限制（约为2^15-1）
            val maxConstraintSize = 32000
            if (width > maxConstraintSize || height > maxConstraintSize) {
                val ratio = Math.min(
                    maxConstraintSize.toFloat() / width.toFloat(),
                    maxConstraintSize.toFloat() / height.toFloat()
                )
                width = (width * ratio).toInt()
                height = (height * ratio).toInt()
            }

            drawable.setBounds(0, 0, width, height)
            setBounds(0, 0, width, height)
        }
    }
}
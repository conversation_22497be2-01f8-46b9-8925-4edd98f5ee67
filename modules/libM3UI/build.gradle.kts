plugins {
    alias(libs.plugins.nowinandroid.android.library)
    id("org.jetbrains.kotlin.plugin.compose") version "2.1.21"
}
android {
    namespace = "com.tiny.compose.ui"

    defaultConfig {
        // Add manifest placeholders
        manifestPlaceholders["MANDI_CHANNEL_VALUE"] = "google"
        manifestPlaceholders["MANDI_PUBLISH_TIME_VALUE"] = "20240601"
    }

    lint {
        // Disable lint checks for missing classes
        disable += "MissingClass"
        // Create a baseline to ignore existing issues
        baseline = file("lint-baseline.xml")
        // Abort on errors
        abortOnError = false
    }
}
dependencies {
    //Collapsing Toolbar
    api("me.onebone:toolbar-compose:2.3.5")
    // change status bar
    api("androidx.compose.animation:animation:1.6.3")
    api("com.airbnb.android:lottie-compose:4.2.0")
    api("com.google.accompanist:accompanist-systemuicontroller:0.32.0")
    api("androidx.constraintlayout:constraintlayout-compose:1.0.1")
    api("com.google.accompanist:accompanist-pager:0.28.0")
    api("com.google.accompanist:accompanist-pager-indicators:0.28.0")

    api(project(":libDomain"))

    api(libs.androidx.compose.runtime)
    api(libs.androidx.compose.runtime.livedata)


    api(libs.androidx.compose.ui.tooling.preview)
    api(libs.androidx.compose.ui.tooling)

    api(libs.androidx.lifecycle.viewModelCompose)
    api(libs.androidx.lifecycle.runtimeCompose)

    implementation(libs.androidx.compose.runtime)
    api(libs.androidx.navigation.compose)
    api(libs.androidx.compose.material3)
    api(libs.androidx.compose.foundation.layout)
    api(libs.androidx.navigation.compose)
    api(libs.androidx.lifecycle.viewModelCompose)
    api(libs.androidx.lifecycle.runtimeCompose)
    api(libs.androidx.activity.compose)
    api(libs.androidx.work.ktx)

    //picture
    api(libs.coil.kt.compose)
    api(libs.coil.kt.gif)

    api(libs.accompanist.permissions)
    api(libs.accompanist.flowlayout)
    api(libs.accompanist.insets)
}

package com.lib.analysis.firebase.manager

import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.ktx.Firebase
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.IEvent

object FirebaseEventLogger : IEvent {
    private val firebaseAnalytics by lazy { FirebaseAnalytics.getInstance(GlobalModule.mApp) }

    override fun logEvent(key: String, vararg params: Pair<String, Any?>) {
        val bundle = Bundle().apply {
            params.forEach { (k, v) ->
                val paramsKey = if (k.length > 40) k.substring(0, 40) else k
                val value = v.toString()
                val formattedValue = if (value.length > 100) value.substring(0, 100) else value
                putString(paramsKey, formattedValue)
            }
        }
        firebaseAnalytics.logEvent(key.take(40), bundle)
    }
}
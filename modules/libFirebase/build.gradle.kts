plugins {
    alias(libs.plugins.nowinandroid.android.library)
}

android {
    namespace = "com.lib.analysis.firebase.manager"
}


dependencies {
    // Import the Firebase BoM

    implementation(platform("com.google.firebase:firebase-bom:33.14.0"))
    // TODO: Add the dependencies for Firebase products you want to use
    // When using the BoM, don't specify versions in Firebase dependencies
    implementation("com.google.firebase:firebase-analytics")
    implementation("com.google.firebase:firebase-analytics-ktx")
    implementation("com.google.firebase:firebase-crashlytics")
    implementation("com.google.firebase:firebase-config")
    implementation("com.google.firebase:firebase-common-ktx")

    // Google User Messaging Platform (UMP) for GDPR
    implementation("com.google.android.ump:user-messaging-platform:2.1.0")

    api(project(":libDomain"))
}
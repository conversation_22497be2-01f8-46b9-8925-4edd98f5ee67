package com.tiny.lib.room.json

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.room.Room
import com.tinypretty.component.GlobalModule.mApp
import com.tiny.lib.room.json.DatabaseManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch

open class JsonViewModel(val databaseName: String = "database_event") : ViewModel() {
    private val TAG = "JsonViewModel"

    //    val readAllData: LiveData<List<JsonItem>> = jsonDao.getAll()
    val allData by lazy {
        jsonDao.getAll()
            .catch { e ->
                Log.e(TAG, "Error getting all data: ${e.message}")
                emit(emptyList())
            }
            .flowOn(Dispatchers.IO)
    }

    fun query(list: List<JsonItem>?, json: String): JsonItem? {
        if (list == null) return null
        return try {
            list.find { it.json == json }
        } catch (e: Exception) {
            Log.e(TAG, "Error querying json: ${e.message}")
            null
        }
    }

    fun addTodo(todoItem: JsonItem) = viewModelScope.launch(Dispatchers.IO) {
        try {
            // 检查数据库记录数量
            DatabaseManager.checkBeforeInsert(jsonDao)
            // 插入记录
            jsonDao.insert(todoItem)
            Log.d(TAG, "addTodo " + todoItem.json)
        } catch (e: Exception) {
            Log.e(TAG, "Error adding todo: ${e.message}")
        }
    }

    fun updateTodo(todoItem: JsonItem) = viewModelScope.launch(Dispatchers.IO) {
        try {
            jsonDao.update(todoItem)
            Log.d(TAG, "updateTodo " + todoItem.json)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating todo: ${e.message}")
        }
    }

    fun deleteTodo(todoItem: JsonItem) = viewModelScope.launch(Dispatchers.IO) {
        try {
            jsonDao.delete(todoItem)
            Log.d(TAG, "deleteTodo " + todoItem.json)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting todo: ${e.message}")
        }
    }

    fun deleteAllJsonItem() = viewModelScope.launch(Dispatchers.IO) {
        try {
            jsonDao.deleteAllJsonItem()
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting all items: ${e.message}")
        }
    }

    val jsonDao by lazy {
        try {
            Room.databaseBuilder(
                mApp.applicationContext, JsonDatabase::class.java,
                databaseName
            )
                .fallbackToDestructiveMigration()
                .build()
                .jsonDao()
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing jsonDao: ${e.message}")
            // 如果初始化失败，尝试重建数据库
            Room.databaseBuilder(
                mApp.applicationContext, JsonDatabase::class.java,
                "${databaseName}_new"
            )
                .fallbackToDestructiveMigration()
                .build()
                .jsonDao()
        }
    }
}
package com.tiny.lib.room.json

import androidx.room.*
import androidx.sqlite.db.SupportSQLiteQuery
import kotlinx.coroutines.flow.Flow


@Dao
abstract class JsonDatabaseDao {
    @Query("SELECT * from json_list")
    abstract fun getAll(): Flow<List<JsonItem>>

    @Query("SELECT * from json_list where itemId = :id")
    abstract fun getById(id: Int): JsonItem?

    @Insert
    abstract fun insert(item: JsonItem)

    @Update
    abstract fun update(item: JsonItem)

    @Delete
    abstract fun delete(item: JsonItem)

    @Query("DELETE FROM json_list")
    abstract fun deleteAllJsonItem()

//    @Update
//    fun updateAll(vararg items: JsonItem)

    @Delete
    abstract fun deleteAll(items: Array<JsonItem>)

    @Insert
    abstract fun insertAll(items: Array<JsonItem>)

    /**
     * 获取数据库中记录的总数
     */
    @Query("SELECT COUNT(*) FROM json_list")
    abstract fun getCountSync(): Int

    /**
     * 获取数据库中记录的总数（协程版本）
     */
    suspend fun getCount(): Int {
        return getCountSync()
    }

    /**
     * 删除最旧的记录（按ID排序，假设ID是自增的）
     * @param count 要删除的记录数量
     */
    @Query("DELETE FROM json_list WHERE itemId IN (SELECT itemId FROM json_list ORDER BY itemId ASC LIMIT :count)")
    abstract fun deleteOldestSync(count: Int): Int

    /**
     * 删除最旧的记录（协程版本）
     * @param count 要删除的记录数量
     */
    suspend fun deleteOldest(count: Int): Int {
        return deleteOldestSync(count)
    }

    /**
     * 获取最旧的记录（用于日志记录）
     * @param count 要获取的记录数量
     */
    @Query("SELECT * FROM json_list ORDER BY itemId ASC LIMIT :count")
    abstract fun getOldestSync(count: Int): List<JsonItem>

    /**
     * 获取最旧的记录（协程版本）
     * @param count 要获取的记录数量
     */
    suspend fun getOldest(count: Int): List<JsonItem> {
        return getOldestSync(count)
    }
}
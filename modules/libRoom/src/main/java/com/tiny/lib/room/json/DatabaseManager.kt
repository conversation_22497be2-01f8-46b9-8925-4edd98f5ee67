package com.tiny.lib.room.json

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 简化版的数据库管理器，用于管理 JSON 数据库记录数量
 */
object DatabaseManager {
    private val TAG = "DatabaseManager"

    // 数据库最大记录数
    private const val MAX_JSON_RECORDS = 1000

    // 一次删除的记录数（当超过最大记录数时）
    private const val DELETE_BATCH_SIZE = 50

    // 简单的日志工具
    private object Logger {
        fun d(tag: String, message: String) {
            Log.d(tag, message)
        }

        fun e(tag: String, message: String) {
            Log.e(tag, message)
        }
    }

    /**
     * 检查并清理JSON数据库
     * @param dao JSON数据库DAO
     */
    suspend fun checkAndCleanJsonDatabase(dao: JsonDatabaseDao) = withContext(Dispatchers.IO) {
        try {
            val count = dao.getCount()
            Logger.d(TAG, "JSON database record count: $count")

            if (count > MAX_JSON_RECORDS) {
                val deleteCount = count - MAX_JSON_RECORDS + DELETE_BATCH_SIZE
                Logger.d(TAG, "JSON database exceeds limit, deleting $deleteCount oldest records")

                // 获取将被删除的记录（用于日志）
                val oldestRecords = dao.getOldest(5)
                oldestRecords.forEach { record ->
                    Logger.d(TAG, "Will delete old JSON record: $record")
                }

                // 删除最旧的记录
                val deletedCount = dao.deleteOldest(deleteCount)
                Logger.d(TAG, "Deleted $deletedCount old records from JSON database")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "Error cleaning JSON database: ${e.message}")
        }
    }

    /**
     * 在插入新记录前检查数据库记录数
     * @param dao JSON数据库DAO
     */
    suspend fun checkBeforeInsert(dao: JsonDatabaseDao) {
        try {
            checkAndCleanJsonDatabase(dao)
        } catch (e: Exception) {
            Logger.e(TAG, "Error checking database before insert: ${e.message}")
        }
    }
}

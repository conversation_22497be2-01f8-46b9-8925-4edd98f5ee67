package com.tiny.lib.room.json

import android.util.Log
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.tinypretty.component.GlobalModule.mApp

@Database(entities = [JsonItem::class], version = 1, exportSchema = false)
@TypeConverters(DatabaseConverters::class)
abstract class JsonDatabase : RoomDatabase() {
    abstract fun jsonDao(): JsonDatabaseDao

    companion object {
        private val TAG = "JsonDatabase"
        private var INSTANCE: JsonDatabase? = null
        fun getInstance(): JsonDatabase? {
            synchronized(this) {
                var instance = INSTANCE
                if (instance == null) {
                    try {
                        instance = Room.databaseBuilder(
                            mApp.applicationContext,
                            JsonDatabase::class.java,
                            "json_database"
                        ).fallbackToDestructiveMigration()
                            .build()
                        INSTANCE = instance
                    } catch (e: Exception) {
                        Log.e(TAG, "Error initializing database: ${e.message}")
                        // 如果初始化失败，尝试重建数据库
                        instance = Room.databaseBuilder(
                            mApp.applicationContext,
                            JsonDatabase::class.java,
                            "json_database_new"
                        ).fallbackToDestructiveMigration()
                            .build()
                        INSTANCE = instance
                    }
                }
                return instance
            }
        }
    }
}
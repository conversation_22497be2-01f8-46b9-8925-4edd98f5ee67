package com.tiny.lib.web.view.sniffer

import CoroutineTask
import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Bitmap
import android.os.Build
import android.view.ViewGroup
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.TextView
import androidx.lifecycle.ViewModel
import com.tiny.domain.util.BitmapCacheUtil
import com.tiny.lib.web.view.sniffer.core.JSConfigure
import com.tiny.lib.web.view.sniffer.core.JSRepo
import com.tiny.lib.web.view.sniffer.core.JsInterface
import com.tiny.lib.web.view.sniffer.core.RedirectUtil
import com.tiny.lib.web.view.sniffer.core.SnifferSingleResult
import com.tiny.lib.web.view.sniffer.core.UrlIntentHandler
import com.tiny.lib.web.view.sniffer.core.VideoSpider
import com.tiny.lib.web.view.sniffer.core.WebViewSafeInitializer
import com.tinypretty.component.GlobalModule.mApp
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.component.IntentTools
import com.tinypretty.component.trueThen
import com.tinypretty.component.urlHostIconCacheKey
import com.tinypretty.component.validHttp
import com.tinypretty.component.validUrl
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.withContext

class SnifferDelegate : ViewModel() {
    private val log = newLog("SnifferDelegate")
    val jsInterface by lazy { JsInterface() }
    val loadingProgress = MutableStateFlow(0)
    var mParsedAlready = false
    private val mVideoSpider = VideoSpider(JSConfigure.userAgent).apply {
        mOnParsed = { pageUrl, urlVideo, pageTitle, contentLen, ext, mergeOnly ->
            if (!mergeOnly) {
                CoroutineTask("mOnParsed").io().launch {
                    val result = mutableListOf<SnifferSingleResult>().apply {
                        addAll(jsInterface.snifferSingleResult.value)
                    }
                    val new = SnifferSingleResult(pageUrl, urlVideo, pageTitle, contentLen, ext)
                    if (!result.any { it.urlVideo == new.urlVideo }) {
                        result.add(new)
                        jsInterface.snifferSingleResult.value = result
                    }
                }
            }
        }
    }

    private val jsRepo by lazy { JSRepo(mApp) }

    @SuppressLint("StaticFieldLeak")
    private var mWebView: WebView? = null
    fun webView(): WebView? {
        return mWebView
    }

    fun removeFromParent() {
        mWebView?.let {
            (it.parent is ViewGroup).trueThen {
                (it.parent as ViewGroup).removeView(it)
            }
        }
    }

    fun title(): String {
        return mWebView?.title ?: ""
    }

    fun url(): String {
        return mWebView?.url ?: ""
    }

    fun icon(): String? {
        return BitmapCacheUtil.getCachedPath(url().urlHostIconCacheKey())
    }

    companion object {
        // 移除重复的WebView初始化调用
        // WebView环境初始化应该只在Application中进行一次
        // 这里不再需要重复调用 WebViewSafeInitializer.initWebViewEnvironment()
    }

    fun canBack(): Boolean {
        return mWebView?.canGoBack() ?: false
    }

    fun goBack() {
        mWebView?.goBack()
    }

    fun canForward(): Boolean {
        return mWebView?.canGoForward() ?: false
    }

    fun goForward() {
        mWebView?.goForward()
    }

    fun canRefresh(): Boolean {
        return mWebView?.url?.validUrl() ?: false
    }

    fun refresh() {
        mParsedAlready = false
        mWebView?.reload()
    }

    fun stopLoading() {
        mWebView?.stopLoading()
    }

    fun loadUrl(url: String) {
        mWebView?.loadUrl(url)
    }

    @SuppressLint("SetJavaScriptEnabled")
    fun createWebView(activity: Activity): WebView {
        // 如果已经有WebView实例，直接返回
        if (mWebView != null) {
            log.i { "createWebView ignore -> return cached webView" }
            return mWebView!!.apply {
                (this.parent is ViewGroup).trueThen {
                    (this.parent as ViewGroup).removeView(this)
                }
            }
        }

        // 检查WebView环境是否已初始化
        if (!WebViewSafeInitializer.isEnvironmentInitialized()) {
            log.e { "WebView environment not initialized, attempting to initialize..." }
            WebViewSafeInitializer.initWebViewEnvironment(activity.applicationContext)
        }

        // 使用安全初始化工具创建WebView
        val webView = WebViewSafeInitializer.createWebViewSafely(activity)

        if (webView == null) {
            // WebView创建失败，记录错误日志
            log.e { "Failed to create WebView, WebView may not be available on this device" }
            // 返回一个基础的WebView，避免空指针异常
            return try {
                WebView(activity).apply {
                    mWebView = this
                    // 设置最基本的属性，避免使用高级功能
                    this.layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                }
            } catch (e: Exception) {
                log.e { "Even basic WebView creation failed: ${e.message}" }
                // 如果连基础WebView都无法创建，抛出异常
                throw RuntimeException("WebView is not available on this device", e)
            }
        }

        return webView.apply {
            mWebView = this
            // 不加这一段 有些网站会无法滑动
            this.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )

            try {
                initSetting(this)
                webViewClient = SnifferViewClient()
                webChromeClient = SnifferChromeClient()
                addJavascriptInterface(jsInterface, "AndroidWebView")
                log.i { "WebView created and configured successfully" }
            } catch (e: Exception) {
                log.e { "Error configuring WebView: ${e.message}" }
            }
        }
    }

    private fun evaluateJavascript(js: String, onCallback: () -> Unit = {}) {
        CoroutineTask("evaluateJavascript").main().launch {
            mWebView?.evaluateJavascript(js) { onCallback() }
        }
    }

    fun destroyWebView() {
        mWebView?.apply {
            loadUrl("about:blank")
            (parent is ViewGroup).trueThen {
                (parent as ViewGroup).removeView(this)
            }

            try {
                stopLoading()
                clearHistory()
                clearCache(true)
                loadUrl("about:blank")
                onPause()
                removeAllViews()
                // Destroy the WebView and free its memory
                destroyDrawingCache()
                destroy()
            } catch (e: Exception) {
                log.e { "destroyWebView error=${e.message}" }
            }

        }
        mWebView = null
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initSetting(webView: WebView) {
        CoroutineTask("initSetting").main().launch {
            webView.settings.run {
                userAgentString = JSConfigure.userAgent
                javaScriptEnabled = true
                javaScriptCanOpenWindowsAutomatically = true
                mediaPlaybackRequiresUserGesture = true
                domStorageEnabled = true
                databaseEnabled = true
                setSupportZoom(true)
                builtInZoomControls = false
                displayZoomControls = false
                allowContentAccess = true
                allowFileAccess = true
                allowFileAccessFromFileURLs = true
                allowUniversalAccessFromFileURLs = true
                useWideViewPort = true
                loadWithOverviewMode = true
            }
        }
    }

    private inner class SnifferChromeClient : android.webkit.WebChromeClient() {
        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            super.onProgressChanged(view, newProgress)
            loadingProgress.value = newProgress
        }

        override fun onReceivedIcon(view: WebView?, icon: Bitmap?) {
            super.onReceivedIcon(view, icon)
            view?.url?.urlHostIconCacheKey()?.let {
                BitmapCacheUtil.saveToCache(icon, it)
            }
        }
    }


    private inner class SnifferViewClient : WebViewClient() {
        fun onPageUrlChange(webUrl: String) {
            if (webUrl.validHttp()) {
                val oldWebUrl = jsInterface.pageStartUrl.value.second
                val oldWebTitle = jsInterface.pageStartUrl.value.first
                val newTitle = title()
                if (oldWebUrl != webUrl) {
                    mParsedAlready = false
                    log.i { "onPageUrlChange oldWebUrl=$oldWebUrl,webUrl=$webUrl" }
                    jsInterface.pageStartUrl.value = Pair(newTitle, webUrl)
                    jsInterface.onPageUrlChange()
                    mVideoSpider.clear()
                }

                if (newTitle.isNotBlank() && oldWebTitle != newTitle) {
                    jsInterface.pageStartUrl.value = Pair(title(), webUrl)
                }
            }
        }

        var mEvaluateJavascriptJob: Job? = null
        override fun onLoadResource(view: WebView?, url: String?) {
            onPageUrlChange(url())
            if (url?.contains(".youtube.") == true) {
                return super.onLoadResource(view, url)
            }

            val redirectUrl = RedirectUtil.redirectUrl(url ?: "")
            if (redirectUrl.isNotEmpty()) {
                log.i { "onLoadResource redirectUrl=$redirectUrl" }
                stopLoading()
                if (mEvaluateJavascriptJob == null || mEvaluateJavascriptJob?.isActive == false) {
                    mEvaluateJavascriptJob = CoroutineTask("evaluateJavascript").main().launch {
                        mWebView?.evaluateJavascript("javascript:window.location.replace('$url')") {}
                        refresh()
                        delay(1000)
                        refresh()
                    }
                }
                return
            }

            CoroutineTask("verifyVideo").main().launch {
                val pageUrl = url()
                val resUrl = url ?: ""
                val title = title()
                withContext(Dispatchers.IO) {
                    mVideoSpider.verifyVideo(pageUrl, resUrl, title)
                }
            }


            val progress = view?.progress ?: 0
            log.i { "onLoadResource -> progress=$progress,parsed=$mParsedAlready" }
            if (progress > 10) {
                if (!mParsedAlready) {
                    mParsedAlready = true
                    evaluateJavascript(jsRepo.vd)
                }
                evaluateJavascript(jsRepo.longPressJs)
            } else {
                mParsedAlready = false
            }
            super.onLoadResource(view, url)
        }

        @Deprecated("Deprecated in Java")
        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
            return handleIntentUrl(url)
        }

        override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
            return handleIntentUrl(request?.url?.toString())
        }

        private fun handleIntentUrl(url: String?): Boolean {
            // 对于非HTTP开头的URL，总是返回true并尝试处理
            if (url != null && !url.startsWith("http://") && !url.startsWith("https://")) {
                log.i { "Handling non-HTTP URL: $url" }

                // 使用新的URL处理器来处理非HTTP URL
                UrlIntentHandler.handleNonHttpUrl(url)
                return true
            }

            // 对于HTTP URL，使用默认处理
            return false
        }
    }
}


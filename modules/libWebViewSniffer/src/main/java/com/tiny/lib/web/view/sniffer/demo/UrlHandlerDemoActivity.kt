package com.tiny.lib.web.view.sniffer.demo

import android.app.Activity
import android.os.Bundle
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.LinearLayout
import android.widget.ScrollView
import com.tiny.lib.web.view.sniffer.core.UrlIntentHandler
import com.tinypretty.component.GlobalModule.newLog

/**
 * URL处理器演示Activity
 * 用于测试非HTTP URL的处理功能
 */
class UrlHandlerDemoActivity : Activity() {
    private val log = newLog("UrlHandlerDemo")
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setupUI()
    }
    
    private fun setupUI() {
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(32, 32, 32, 32)
        }
        
        // 标题
        val title = android.widget.TextView(this).apply {
            text = "URL Handler Demo"
            textSize = 20f
            setPadding(0, 0, 0, 32)
        }
        layout.addView(title)
        
        // 测试按钮
        val testUrls = listOf(
            "mailto:<EMAIL>" to "Test Gmail",
            "tel:+1234567890" to "Test Phone Call",
            "sms:+1234567890?body=Hello" to "Test SMS",
            "whatsapp://send?text=Hello%20World" to "Test WhatsApp",
            "intent://scan/#Intent;scheme=zxing;package=com.google.zxing.client.android;end" to "Test QR Scanner"
        )
        
        testUrls.forEach { (url, label) ->
            val button = Button(this).apply {
                text = label
                setOnClickListener {
                    log.i { "Testing URL: $url" }
                    UrlIntentHandler.handleNonHttpUrl(url)
                }
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    bottomMargin = 16
                }
            }
            layout.addView(button)
        }
        
        // WebView测试
        val webViewButton = Button(this).apply {
            text = "Test in WebView"
            setOnClickListener {
                showWebViewDemo()
            }
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                topMargin = 32
            }
        }
        layout.addView(webViewButton)
        
        val scrollView = ScrollView(this).apply {
            addView(layout)
        }
        
        setContentView(scrollView)
    }
    
    private fun showWebViewDemo() {
        val webView = WebView(this).apply {
            webViewClient = object : WebViewClient() {
                override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                    log.i { "WebView shouldOverrideUrlLoading: $url" }
                    
                    // 对于非HTTP URL，总是返回true并尝试处理
                    if (url != null && !url.startsWith("http://") && !url.startsWith("https://")) {
                        log.i { "Handling non-HTTP URL: $url" }
                        UrlIntentHandler.handleNonHttpUrl(url)
                        return true
                    }
                    
                    return false
                }
            }
            
            settings.javaScriptEnabled = true
        }
        
        // 加载包含各种链接的HTML页面
        val html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>URL Handler Test</title>
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    a { display: block; margin: 10px 0; padding: 10px; background: #f0f0f0; text-decoration: none; color: #333; border-radius: 5px; }
                    a:hover { background: #e0e0e0; }
                </style>
            </head>
            <body>
                <h1>URL Handler Test Page</h1>
                <p>Click the links below to test URL handling:</p>
                
                <a href="mailto:<EMAIL>">📧 Send Email (Gmail)</a>
                <a href="tel:+1234567890">📞 Make Phone Call</a>
                <a href="sms:+1234567890?body=Hello">💬 Send SMS</a>
                <a href="whatsapp://send?text=Hello%20World">💚 WhatsApp Message</a>
                <a href="https://www.google.com">🌐 Regular HTTP Link</a>
                
                <h2>JavaScript Test</h2>
                <button onclick="window.location.href='mailto:<EMAIL>'">JS Email Link</button>
                <button onclick="window.location.href='tel:+9876543210'">JS Phone Link</button>
            </body>
            </html>
        """.trimIndent()
        
        webView.loadData(html, "text/html", "UTF-8")
        setContentView(webView)
    }
}

package com.tiny.lib.web.view.sniffer.core

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import android.net.Uri
import com.tiny.lib.web.view.sniffer.ui.AppLaunchDialog
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.GlobalModule.newLog

/**
 * URL Intent处理器
 * 专门处理非HTTP URL的跳转逻辑
 */
object UrlIntentHandler {
    private val log = newLog("UrlIntentHandler")

    /**
     * 处理非HTTP URL
     * @param url 非HTTP URL
     */
    fun handleNonHttpUrl(url: String) {
        try {
            val intent = createIntentFromUrl(url)
            if (intent != null) {
                val appInfo = getAppInfoForIntent(intent)
                if (appInfo != null) {
                    // 如果找到可以处理的应用，显示选择对话框
                    showAppSelectionDialog(intent, appInfo)
                } else {
                    log.e { "No app found to handle URL: $url" }
                }
            }
        } catch (e: Exception) {
            log.e { "Error handling non-HTTP URL: $url, error: ${e.message}" }
        }
    }

    /**
     * 从URL创建Intent
     */
    private fun createIntentFromUrl(url: String): Intent? {
        return try {
            when {
                url.startsWith("intent://") -> {
                    Intent.parseUri(url, Intent.URI_INTENT_SCHEME)
                }
                url.contains("://") -> {
                    Intent(Intent.ACTION_VIEW, Uri.parse(url))
                }
                else -> null
            }
        } catch (e: Exception) {
            log.e { "Failed to create intent from URL: $url, error: ${e.message}" }
            null
        }
    }

    /**
     * 获取可以处理Intent的应用信息
     */
    private fun getAppInfoForIntent(intent: Intent): AppInfo? {
        return try {
            val packageManager = GlobalModule.mApp.packageManager
            val resolveInfos = packageManager.queryIntentActivities(
                intent,
                PackageManager.MATCH_DEFAULT_ONLY
            )

            if (resolveInfos.isNotEmpty()) {
                val resolveInfo = resolveInfos.first()
                val appName = resolveInfo.loadLabel(packageManager).toString()
                val appIcon = resolveInfo.loadIcon(packageManager)
                val packageName = resolveInfo.activityInfo.packageName

                AppInfo(appName, appIcon, packageName, intent)
            } else {
                null
            }
        } catch (e: Exception) {
            log.e { "Failed to get app info for intent: ${e.message}" }
            null
        }
    }

    /**
     * 显示应用选择对话框
     */
    private fun showAppSelectionDialog(intent: Intent, appInfo: AppInfo) {
        val activity = GlobalModule.activity.invoke()
        if (activity != null) {
            // 优先使用自定义对话框，显示应用图标和名称
            try {
                AppLaunchDialog.show(
                    activity = activity,
                    appName = appInfo.name,
                    appIcon = appInfo.icon,
                    intent = appInfo.intent
                )
                log.i { "Showed custom app launch dialog for: ${appInfo.name}" }
            } catch (e: Exception) {
                log.e { "Custom dialog failed, falling back to system chooser: ${e.message}" }
                // 如果自定义对话框失败，使用系统选择器作为备用方案
                showSystemChooser(activity, intent, appInfo)
            }
        } else {
            log.e { "No activity available to show dialog" }
        }
    }

    /**
     * 显示系统选择器（备用方案）
     */
    private fun showSystemChooser(activity: Activity, intent: Intent, appInfo: AppInfo) {
        try {
            val chooserIntent = Intent.createChooser(intent, null).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            activity.startActivity(chooserIntent)
            log.i { "Launched system chooser for: ${appInfo.name}" }
        } catch (e: ActivityNotFoundException) {
            log.e { "No activity found to handle intent: ${e.message}" }
        } catch (e: Exception) {
            log.e { "Error launching system chooser: ${e.message}" }
        }
    }

    /**
     * 应用信息数据类
     */
    data class AppInfo(
        val name: String,
        val icon: Drawable,
        val packageName: String,
        val intent: Intent
    )
}

package com.tiny.lib.web.view.sniffer.ui

import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.tiny.lib.web.view.sniffer.R
import com.tinypretty.component.GlobalModule.newLog

/**
 * 应用启动对话框
 * 显示应用图标、名称，让用户选择是否打开应用
 */
object AppLaunchDialog {
    private val log = newLog("AppLaunchDialog")
    
    /**
     * 显示应用启动确认对话框
     * @param activity 当前Activity
     * @param appName 应用名称
     * @param appIcon 应用图标
     * @param intent 要启动的Intent
     */
    fun show(
        activity: Activity,
        appName: String,
        appIcon: Drawable?,
        intent: Intent
    ) {
        try {
            // 使用系统默认的对话框样式，避免依赖应用内翻译
            val builder = AlertDialog.Builder(activity, android.R.style.Theme_Material_Dialog_Alert)
            
            // 创建自定义布局
            val dialogView = createDialogView(activity, appName, appIcon)
            builder.setView(dialogView)
            
            // 设置按钮 - 使用系统默认文本
            builder.setPositiveButton(android.R.string.ok) { _, _ ->
                try {
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    activity.startActivity(intent)
                    log.i { "Successfully launched app: $appName" }
                } catch (e: Exception) {
                    log.e { "Failed to launch app: $appName, error: ${e.message}" }
                }
            }
            
            builder.setNegativeButton(android.R.string.cancel) { dialog, _ ->
                dialog.dismiss()
                log.i { "User cancelled launching app: $appName" }
            }
            
            // 显示对话框
            val dialog = builder.create()
            if (!activity.isFinishing && !activity.isDestroyed) {
                dialog.show()
            }
            
        } catch (e: Exception) {
            log.e { "Error showing app launch dialog: ${e.message}" }
            // 如果对话框显示失败，直接启动应用
            fallbackLaunchApp(activity, intent, appName)
        }
    }
    
    /**
     * 创建对话框视图
     */
    private fun createDialogView(
        activity: Activity,
        appName: String,
        appIcon: Drawable?
    ): View {
        // 创建简单的线性布局
        val layout = android.widget.LinearLayout(activity).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(48, 32, 48, 32)
            gravity = android.view.Gravity.CENTER
        }
        
        // 应用图标
        if (appIcon != null) {
            val iconView = ImageView(activity).apply {
                setImageDrawable(appIcon)
                layoutParams = android.widget.LinearLayout.LayoutParams(
                    120, 120
                ).apply {
                    gravity = android.view.Gravity.CENTER
                    bottomMargin = 24
                }
                scaleType = ImageView.ScaleType.FIT_CENTER
            }
            layout.addView(iconView)
        }
        
        // 提示文本
        val messageView = TextView(activity).apply {
            text = "Open with $appName?"
            textSize = 16f
            gravity = android.view.Gravity.CENTER
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                gravity = android.view.Gravity.CENTER
            }
            // 使用系统默认的文本颜色
            setTextColor(activity.resources.getColor(android.R.color.primary_text_light, activity.theme))
        }
        layout.addView(messageView)
        
        return layout
    }
    
    /**
     * 备用启动方法 - 如果对话框显示失败，直接启动应用
     */
    private fun fallbackLaunchApp(activity: Activity, intent: Intent, appName: String) {
        try {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            activity.startActivity(intent)
            log.i { "Fallback launch successful for app: $appName" }
        } catch (e: Exception) {
            log.e { "Fallback launch failed for app: $appName, error: ${e.message}" }
        }
    }
}

package com.tiny.lib.web.view.sniffer.core

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.util.Log
import android.webkit.WebView
import android.widget.TextView
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.GlobalModule.newLog

/**
 * WebView安全初始化工具类
 * 用于处理WebView初始化过程中可能出现的异常
 * 特别是处理 ClassNotFoundException: com.android.webview.chromium.WebViewChromiumFactoryProviderForQ 异常
 */
object WebViewSafeInitializer {
    private val TAG = "WebViewSafeInitializer"
    private val log = newLog(TAG)
    private var isWebViewAvailable = true
    private var isEnvironmentInitialized = false

    /**
     * 初始化WebView环境
     * 应在Application中调用
     * 该方法只会执行一次，重复调用会被忽略
     */
    fun initWebViewEnvironment(context: Context) {
        // 防止重复初始化
        if (isEnvironmentInitialized) {
            log.i { "WebView environment already initialized, skipping..." }
            return
        }

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val processName = context.applicationInfo.processName
                val dirName = "sniffer_" + processName.hashCode()
                WebView.setDataDirectorySuffix(dirName)
                log.i { "WebView data directory suffix set to: $dirName" }
            }

            // 尝试创建一个WebView实例来验证WebView是否可用
            val testWebView = createWebViewSafely(context)
            if (testWebView == null) {
                isWebViewAvailable = false
                log.e { "WebView initialization failed during environment setup" }
            } else {
                isWebViewAvailable = true
                // 销毁测试用的WebView
                testWebView.destroy()
                log.i { "WebView environment initialized successfully" }
            }

            // 标记环境已初始化
            isEnvironmentInitialized = true

        } catch (e: Exception) {
            isWebViewAvailable = false
            isEnvironmentInitialized = true // 即使失败也标记为已初始化，避免重复尝试
            log.e { "WebView environment initialization failed: ${e.message}" }
        }
    }

    /**
     * 安全创建WebView
     * 如果创建失败，返回null
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun createWebViewSafely(context: Context): WebView? {
        if (!isWebViewAvailable) {
            log.e { "WebView is not available on this device" }
            return null
        }

        return try {
            WebView(context).apply {
                // 基本设置，确保WebView可以正常工作
                settings.javaScriptEnabled = true
                settings.domStorageEnabled = true
                settings.databaseEnabled = true
            }
        } catch (e: Exception) {
            isWebViewAvailable = false
            log.e { "Failed to create WebView: ${e.message}" }
            null
        }
    }

    /**
     * 检查WebView是否可用
     */
    fun isWebViewAvailable(): Boolean {
        return isWebViewAvailable
    }

    /**
     * 检查WebView环境是否已初始化
     */
    fun isEnvironmentInitialized(): Boolean {
        return isEnvironmentInitialized
    }

    /**
     * 创建一个TextView作为WebView不可用时的替代视图
     */
    fun createFallbackTextView(context: Context, message: String = "WebView is not available on this device"): TextView {
        return TextView(context).apply {
            text = message
        }
    }
}

package com.tiny.lib.web.view.sniffer.core

import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.net.Uri
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.verify
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * UrlIntentHandler 单元测试
 */
class UrlIntentHandlerTest {

    @Before
    fun setup() {
        // Mock GlobalModule
        mockkObject(com.tinypretty.component.GlobalModule)
    }

    @Test
    fun `test createIntentFromUrl with intent scheme`() {
        val url = "intent://scan/#Intent;scheme=zxing;package=com.google.zxing.client.android;end"
        
        // 这个测试验证Intent URL的解析
        // 在实际环境中，Intent.parseUri会正确解析这种URL
        assertTrue("Intent URL should be recognized", url.startsWith("intent://"))
    }

    @Test
    fun `test createIntentFromUrl with custom scheme`() {
        val url = "mailto:<EMAIL>"
        
        // 验证自定义scheme的识别
        assertTrue("Custom scheme should be recognized", url.contains("://"))
    }

    @Test
    fun `test non-http url detection`() {
        val httpUrl = "https://www.example.com"
        val httpsUrl = "https://www.example.com"
        val mailtoUrl = "mailto:<EMAIL>"
        val intentUrl = "intent://test"
        
        // 验证HTTP URL检测
        assertTrue("HTTP URL should be detected", httpUrl.startsWith("http://") || httpUrl.startsWith("https://"))
        assertTrue("HTTPS URL should be detected", httpsUrl.startsWith("http://") || httpsUrl.startsWith("https://"))
        
        // 验证非HTTP URL检测
        assertFalse("Mailto URL should not be HTTP", mailtoUrl.startsWith("http://") || mailtoUrl.startsWith("https://"))
        assertFalse("Intent URL should not be HTTP", intentUrl.startsWith("http://") || intentUrl.startsWith("https://"))
    }

    @Test
    fun `test common app schemes`() {
        val testUrls = listOf(
            "mailto:<EMAIL>",
            "tel:+1234567890",
            "sms:+1234567890",
            "whatsapp://send?text=hello",
            "telegram://msg?text=hello",
            "intent://scan/#Intent;scheme=zxing;package=com.google.zxing.client.android;end"
        )
        
        testUrls.forEach { url ->
            assertFalse("$url should be non-HTTP", 
                url.startsWith("http://") || url.startsWith("https://"))
        }
    }
}

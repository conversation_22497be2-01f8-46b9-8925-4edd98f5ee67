package com.tiny.lib.web.view.sniffer.core

import android.content.Context
import android.content.pm.ApplicationInfo
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * WebViewSafeInitializer 单元测试
 * 测试WebView初始化的各种场景
 */
class WebViewSafeInitializerTest {

    private lateinit var mockContext: Context
    private lateinit var mockApplicationInfo: ApplicationInfo

    @Before
    fun setup() {
        mockContext = mockk(relaxed = true)
        mockApplicationInfo = mockk(relaxed = true)
        
        every { mockContext.applicationInfo } returns mockApplicationInfo
        every { mockApplicationInfo.processName } returns "com.test.app"
        
        // 重置初始化状态（通过反射）
        resetInitializationState()
    }

    @Test
    fun `test initWebViewEnvironment called once`() {
        // 第一次调用应该成功
        WebViewSafeInitializer.initWebViewEnvironment(mockContext)
        assertTrue("Environment should be initialized", WebViewSafeInitializer.isEnvironmentInitialized())
        
        // 第二次调用应该被忽略
        WebViewSafeInitializer.initWebViewEnvironment(mockContext)
        assertTrue("Environment should still be initialized", WebViewSafeInitializer.isEnvironmentInitialized())
    }

    @Test
    fun `test isWebViewAvailable returns correct state`() {
        // 初始状态应该是可用的
        assertTrue("WebView should be available initially", WebViewSafeInitializer.isWebViewAvailable())
        
        // 初始化后状态应该保持
        WebViewSafeInitializer.initWebViewEnvironment(mockContext)
        // 注意：在测试环境中，WebView可能不可用，所以这里不强制断言
    }

    @Test
    fun `test isEnvironmentInitialized returns correct state`() {
        // 初始状态应该是未初始化
        assertFalse("Environment should not be initialized initially", WebViewSafeInitializer.isEnvironmentInitialized())
        
        // 初始化后应该返回true
        WebViewSafeInitializer.initWebViewEnvironment(mockContext)
        assertTrue("Environment should be initialized after init", WebViewSafeInitializer.isEnvironmentInitialized())
    }

    /**
     * 通过反射重置初始化状态，用于测试
     */
    private fun resetInitializationState() {
        try {
            val clazz = WebViewSafeInitializer::class.java
            val isEnvironmentInitializedField = clazz.getDeclaredField("isEnvironmentInitialized")
            isEnvironmentInitializedField.isAccessible = true
            isEnvironmentInitializedField.setBoolean(WebViewSafeInitializer, false)
            
            val isWebViewAvailableField = clazz.getDeclaredField("isWebViewAvailable")
            isWebViewAvailableField.isAccessible = true
            isWebViewAvailableField.setBoolean(WebViewSafeInitializer, true)
        } catch (e: Exception) {
            // 如果反射失败，忽略错误
            e.printStackTrace()
        }
    }
}

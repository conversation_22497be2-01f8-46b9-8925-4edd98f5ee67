/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.nowinandroid


import com.android.build.gradle.internal.dsl.BaseAppModuleExtension
import org.gradle.api.Project
import org.gradle.kotlin.dsl.extra
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.io.File

/**
 * Configure Compose-specific options
 * "../../keystores/debug111111debug111111.jks;111111;debug"
 */

class SignConfigure {
    var appName = HashMap<String, String>()
}


var AD_GDT = "libAdGDT"
var AD_GO_MORE = "libAdGoMore"
var AD_NONE = ""

//"kuan","s360",
fun Project.configureAndroidSign(
    appNameJson: String,
    signConfigs: String,
    appExtension: BaseAppModuleExtension,
    debugConfigs: String = signConfigs,
    applicationIDMap: kotlin.collections.Map<String, String> = mapOf(),
    appIconMap: kotlin.collections.Map<String, String> = mapOf(),//mapOf(Pair("vivo","@mipmap/ic_launcher_box")))
    channelList: List<String> = arrayListOf("google")
) {
    appExtension.apply {
        var signParam = signConfigs.split(";")
        var debugParam = debugConfigs.split(";")
        signingConfigs {
            getByName("debug") {
                storeFile = file(debugParam[0])
                storePassword = debugParam[1]
                keyAlias = debugParam[2]
                keyPassword = debugParam[1]
            }
            create("release") {
                storeFile = file(signParam[0])
                storePassword = signParam[1]
                keyAlias = signParam[2]
                keyPassword = signParam[1]
            }
        }

        buildTypes {
            getByName("debug") {
                extra["enableCrashlytics"] = false
                extra["alwaysUpdateBuildId"] = false
                isDebuggable = true
                signingConfig = signingConfigs.getByName("debug")
                multiDexEnabled = true
                isMinifyEnabled = false
                isShrinkResources = false
                proguardFiles(getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro")
            }
            getByName("release") {
                isDebuggable = false
                signingConfig = signingConfigs.getByName("release")
                multiDexEnabled = true
                isMinifyEnabled = true
                isShrinkResources = true
                proguardFiles(getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro")
            }
        }

        flavorDimensions.add("default")

        productFlavors {
            channelList.apply {
                var vipStr = "vip:"
                forEach {
                    vipStr += "${defaultConfig.versionName}.$it "
                }
                println("vipStr = $vipStr")
            }.forEach { channel ->
                create(channel) {
                    dimension = "default"
                    // change applicationID by channel
                    if (channel in applicationIDMap) {
                        this.applicationId = applicationIDMap[channel]
                    }

                    // change icon
                    if (channel in appIconMap) {
                        manifestPlaceholders["appIcon"] = "${appIconMap[channel]}"
                    } else {
                        manifestPlaceholders["appIcon"] = "@mipmap/ic_launcher"
                    }
                    println("manifestPlaceholders[\"appIcon\"] = $channel" + manifestPlaceholders["appIcon"])

                    // change appname by channel
                    if (appNameJson.isNotBlank()) {
                        var appName = ""
                        appNameJson.split(";").forEach { channelAppName ->
                            if (appName.isEmpty()) {
                                appName = channelAppName
                            }
                            if (channelAppName.contains("[$channel]")) {
                                appName = channelAppName.replace("[$channel]", "")
                            }
                        }
                        if (appName.isNotBlank()) {
                            resValue("string", "app_name", appName)
                        }

                        println("configureAndroidSign channel=$channel appName=$appName")
                    }
                }
            }
        }
        productFlavors.all {
            manifestPlaceholders["MANDI_CHANNEL_VALUE"] = name
            manifestPlaceholders["MANDI_PUBLISH_TIME_VALUE"] = "[long]${System.currentTimeMillis()}"
        }

        // Add afterEvaluate block for renaming APK and AAB files
        afterEvaluate {
            // Create pub directory and clean it
            val pubDir = file("$buildDir/../../../../../PubFiles").apply { 
                mkdirs()
                // Clean up existing files in pub directory
                listFiles()?.forEach { file ->
                    if (file.extension in listOf("apk", "aab", "txt")) {
                        println("Cleaning up old file: ${file.absolutePath}")
                        file.delete()
                    }
                }
            }
            val date = SimpleDateFormat("yyyyMMddHHmm", Locale.getDefault(Locale.Category.FORMAT)).format(Date())
            
            // Configure APK renaming and mapping copying
            tasks.matching { it.name.startsWith("assemble") }.configureEach {
                doLast {
                    println("Searching for APK files in: $buildDir/outputs/apk/")
                    val apkDir = file("$buildDir/outputs/apk/")
                    apkDir.walkTopDown()
                        .filter { it.isFile && it.extension == "apk" }
                        .forEach { apk ->
                            println("Found APK: ${apk.absolutePath}")
                            val flavorName = apk.parentFile.name
                            val buildType = if (flavorName.contains("release")) "r" else "d"
                            val packageName = defaultConfig.applicationId ?: ""
                            val versionName = defaultConfig.versionName ?: ""
                            val versionCode = defaultConfig.versionCode ?: 0
                            val newName = "$packageName-$versionName-$versionCode-$date.apk"
                            val newFile = File(pubDir, newName)
                            apk.copyTo(newFile, overwrite = true)
                            println("Copied APK to: ${newFile.absolutePath}")
                        }
                }
            }

            // Configure AAB renaming and mapping copying
            tasks.matching { it.name.startsWith("bundle") }.configureEach {
                doLast {
                    println("Searching for AAB files in: $buildDir/outputs/bundle/")
                    val bundleDir = file("$buildDir/outputs/bundle/")
                    bundleDir.walkTopDown()
                        .filter { it.isFile && it.extension == "aab" }
                        .forEach { aab ->
                            val flavorName = aab.parentFile.name
                            val buildType = if (flavorName.contains("release")) "r" else "d"
                            val packageName = defaultConfig.applicationId ?: ""
                            val versionName = defaultConfig.versionName ?: ""
                            val versionCode = defaultConfig.versionCode ?: 0
                            val newName = "$packageName-$versionName-$versionCode-$date.aab"
                            val newFile = File(pubDir, newName)
                            aab.copyTo(newFile, overwrite = true)
                            println("Copied AAB to: ${newFile.absolutePath}")
                        }
                }
            }
        }
    }
}
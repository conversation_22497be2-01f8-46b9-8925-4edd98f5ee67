package free.download.video.downloader.utils

import free.download.video.downloader.model.download.DownloadDao
import free.download.video.downloader.model.download.FileEntity
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.mockito.Mockito.*

/**
 * 数据库管理器的单元测试
 */
class DatabaseManagerTest {

    @Test
    fun testCheckAndCleanDownloadDatabase() = runBlocking {
        // 创建模拟的 DAO 对象
        val dao = mock(DownloadDao::class.java)
        
        // 设置记录数量为 1100（超过限制）
        `when`(dao.getCount()).thenReturn(1100)
        
        // 设置最旧的记录
        val oldestRecords = listOf(
            FileEntity("title1", "url1", "cover1", "website1", "dir1", "file1").apply { time = 1000L },
            FileEntity("title2", "url2", "cover2", "website2", "dir2", "file2").apply { time = 2000L }
        )
        `when`(dao.getOldest(anyInt())).thenReturn(oldestRecords)
        
        // 设置删除结果
        `when`(dao.deleteOldest(anyInt())).thenReturn(150)
        
        // 调用被测试的方法
        DatabaseManager.checkAndCleanDownloadDatabase(dao)
        
        // 验证方法调用
        verify(dao).getCount()
        verify(dao).getOldest(5)
        verify(dao).deleteOldest(150) // 1100 - 1000 + 50 = 150
    }
    
    @Test
    fun testCheckAndCleanDownloadDatabaseUnderLimit() = runBlocking {
        // 创建模拟的 DAO 对象
        val dao = mock(DownloadDao::class.java)
        
        // 设置记录数量为 900（未超过限制）
        `when`(dao.getCount()).thenReturn(900)
        
        // 调用被测试的方法
        DatabaseManager.checkAndCleanDownloadDatabase(dao)
        
        // 验证方法调用
        verify(dao).getCount()
        verify(dao, never()).getOldest(anyInt())
        verify(dao, never()).deleteOldest(anyInt())
    }
    
    @Test
    fun testCheckBeforeInsert() = runBlocking {
        // 创建模拟的 DAO 对象
        val dao = mock(DownloadDao::class.java)
        
        // 设置记录数量为 1100（超过限制）
        `when`(dao.getCount()).thenReturn(1100)
        
        // 设置最旧的记录
        val oldestRecords = listOf(
            FileEntity("title1", "url1", "cover1", "website1", "dir1", "file1").apply { time = 1000L }
        )
        `when`(dao.getOldest(anyInt())).thenReturn(oldestRecords)
        
        // 设置删除结果
        `when`(dao.deleteOldest(anyInt())).thenReturn(150)
        
        // 调用被测试的方法
        DatabaseManager.checkBeforeInsert(dao)
        
        // 验证方法调用
        verify(dao).getCount()
        verify(dao).getOldest(5)
        verify(dao).deleteOldest(150)
    }
}

package free.download.video.downloader.utils

import android.database.Cursor
import org.junit.Assert.assertEquals
import org.junit.Test
import org.mockito.Mockito.`when`
import org.mockito.Mockito.mock
import java.lang.Exception

/**
 * 数据库辅助工具类的单元测试
 */
class DatabaseHelperTest {

    @Test
    fun testSafeGetString() {
        // 创建模拟的 Cursor 对象
        val cursor = mock(Cursor::class.java)
        
        // 设置正常情况下的返回值
        `when`(cursor.getString(0)).thenReturn("test")
        assertEquals("test", DatabaseHelper.safeGetString(cursor, 0))
        
        // 设置异常情况下的返回值
        `when`(cursor.getString(1)).thenThrow(Exception("Test exception"))
        assertEquals("", DatabaseHelper.safeGetString(cursor, 1))
    }
    
    @Test
    fun testSafeGetLong() {
        // 创建模拟的 Cursor 对象
        val cursor = mock(Cursor::class.java)
        
        // 设置正常情况下的返回值
        `when`(cursor.getLong(0)).thenReturn(123L)
        assertEquals(123L, DatabaseHelper.safeGetLong(cursor, 0))
        
        // 设置异常情况下的返回值
        `when`(cursor.getLong(1)).thenThrow(Exception("Test exception"))
        assertEquals(0L, DatabaseHelper.safeGetLong(cursor, 1))
    }
    
    @Test
    fun testSafeGetInt() {
        // 创建模拟的 Cursor 对象
        val cursor = mock(Cursor::class.java)
        
        // 设置正常情况下的返回值
        `when`(cursor.getInt(0)).thenReturn(123)
        assertEquals(123, DatabaseHelper.safeGetInt(cursor, 0))
        
        // 设置异常情况下的返回值
        `when`(cursor.getInt(1)).thenThrow(Exception("Test exception"))
        assertEquals(0, DatabaseHelper.safeGetInt(cursor, 1))
    }
    
    @Test
    fun testSafeGetBoolean() {
        // 创建模拟的 Cursor 对象
        val cursor = mock(Cursor::class.java)
        
        // 设置正常情况下的返回值
        `when`(cursor.getInt(0)).thenReturn(1)
        assertEquals(true, DatabaseHelper.safeGetBoolean(cursor, 0))
        
        // 设置异常情况下的返回值
        `when`(cursor.getInt(1)).thenThrow(Exception("Test exception"))
        assertEquals(false, DatabaseHelper.safeGetBoolean(cursor, 1))
    }
}

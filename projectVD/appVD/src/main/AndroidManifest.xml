<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="ScopedStorage"
        tools:node="replace" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:node="replace" />

    <uses-permission
        android:name="android.permission.READ_MEDIA_VIDEO"
        tools:node="replace" />
    <uses-permission
        android:name="android.permission.READ_MEDIA_IMAGES"
        tools:node="replace" />

    <application
        android:name=".DVDApp"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true">

        <!--dvd 新 admob -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            tools:replace="android:value" 
            android:value="ca-app-pub-3940256099942544~**********"  />   <!-- //<todo replace>android:value="ca-app-pub-3940256099942544~**********" /></todo replace>--> 
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:launchMode="singleInstance"
            android:screenOrientation="unspecified"
            android:supportsPictureInPicture="true"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="MANDI_CHANNEL"
            android:value="${MANDI_CHANNEL_VALUE}" />
        <meta-data
            android:name="MANDI_PUBLISH_TIME"
            android:value="${MANDI_PUBLISH_TIME_VALUE}" />

        <service
            android:name=".service.DownloadService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />
    </application>
</manifest>
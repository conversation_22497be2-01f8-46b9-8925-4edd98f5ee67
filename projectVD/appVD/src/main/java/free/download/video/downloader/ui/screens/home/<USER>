package free.download.video.downloader.ui.screens.home

import CoroutineTask
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tiny.ad.network.AdRepo
import com.tiny.domain.util.BitmapCacheUtil
import com.tiny.domain.util.copyToClipboard
import com.tiny.domain.util.forEachGroup
import com.tinypretty.component.GlobalModule.activity
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.component.ResTools
import com.tinypretty.component.urlHostIconCacheKey
import com.tinypretty.ui.componets.AppButton
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.RowSplit
import com.tinypretty.ui.componets.SpacerFix
import com.tinypretty.ui.componets.ad.SelfAdBannerView
import com.tinypretty.ui.dialogs.AlertCloseAbleContent
import com.tinypretty.ui.theme.C
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clip
import com.tinypretty.ui.theme.mid
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.LocalBookmarkViewModel
import free.download.video.downloader.LocalHistoryViewModel
import free.download.video.downloader.R
import free.download.video.downloader.model.bookmark.BookMarkEntity
import free.download.video.downloader.model.history.HistoryEntity
import free.download.video.downloader.ui.components.RewardNoADModeScreen
import free.download.video.downloader.ui.screens.downloader.downloadGuideItem

/**
 * <AUTHOR>
 * @Since 2024/01/12
 */
@Composable
fun HomePageScreen() = Box(
    Modifier
        .fillMaxSize()
        .background(MT.color.background)
) {
    val log = newLog("HomePageScreen").apply { i { "HomePageScreen" } }
    val bookMarks = LocalBookmarkViewModel.current.bookMarkList.collectAsState(initial = emptyList())
    val historyViewModel = LocalHistoryViewModel.current
    val history = historyViewModel.history.collectAsState(initial = emptyList())

    val listState = rememberLazyListState()
    LazyColumn(
        Modifier
            .background(MT.color.background)
            .padding(12.dp), state = listState
    ) {

        if (bookMarks.value.isNotEmpty()) {
            item {
                Column(
                    Modifier
                        .background(MT.color.surface, MT.shapes.small)
                        .padding(horizontal = 0.dp, vertical = 12.dp)
                ) {
                    bookMarks.value.sortedBy { it.url }.forEachGroup(5) { items ->
                        RowSplit(columnCount = 5) { index ->
                            items.getOrNull(index)?.let { item ->
                                log.i { "HomePageScreen bookmark $item" }
                                BookMarkItem(item)
                            }
                        }
                    }
                }
            }
        }
        item {
            Box(contentAlignment = Alignment.CenterEnd, modifier = Modifier.fillMaxWidth()) {
                RewardNoADModeScreen(
                    Modifier
                        .padding(top = 12.dp)
                        .align(Alignment.CenterStart)
                )

                if (history.value.isNotEmpty()) {
                    ImageApp(
                        data = "res/ic_clear_all.webp",
                        Modifier
                            .size(32.dp)
                            .padding(6.dp)
                            .clickable {
                                CoroutineTask("clearHistory")
                                    .io()
                                    .launch {
                                        historyViewModel.clearHistory()
                                    }
                            },
                        color = MT.color.onBackground
                    )
                }
            }
        }

        // 过滤掉无效的历史记录并反转顺序
        val validHistory = history.value.filter { it != null && it.isValid() }.asReversed()
        validHistory.forEachIndexed { index, item ->
            item {
                HistoryItem(item)
            }
        }
        item {
            12.SpacerFix()
        }
        downloadGuideItem()
    }
}

@Composable
private fun HistoryItem(it: HistoryEntity?) {
    if (it == null || !it.isValid()) {
        return
    }

    val appVM = LocalAppViewModel.current
    Row(
        Modifier
            .fillMaxWidth()
            .padding(bottom = 24.dp)
            .clickable { appVM.toBrowserPage(it.url) }, verticalAlignment = Alignment.CenterVertically
    ) {
        ImageApp(
            data = "res/ic_bottom_history.webp", modifier = Modifier
                .size(42.dp)
                .padding(6.dp), color = MT.color.onSurface
        )
        Column(Modifier.padding(start = 12.dp)) {
            Text(
                text = it.title.ifBlank { "无标题" },
                style = MT.typography.titleMedium,
                color = MT.color.onSurface,
                maxLines = 1
            )
            Text(
                text = it.url.ifBlank { "无链接" },
                style = MT.typography.bodyMedium,
                color = MT.color.onSurface,
                maxLines = 1
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BookMarkItem(bookMarkEntity: BookMarkEntity) {
    val activity = activity() ?: return
    val bookMarksVM = LocalBookmarkViewModel.current
    val cachedIconUrl = BitmapCacheUtil.getCachedPath(bookMarkEntity.url.urlHostIconCacheKey())
    val iconUrl = remember { mutableStateOf(cachedIconUrl ?: bookMarkEntity.icon) }

    val appVM = LocalAppViewModel.current
    val showSelected = remember { mutableStateOf(false) }
    val showEdit = remember { mutableStateOf(false) }


    if (showEdit.value) {
        val text = remember { mutableStateOf(bookMarkEntity.title) }
        AlertDialog(
            onDismissRequest = { showEdit.value = false },
            title = { Text(ResTools.str(R.string.book_mark_edit)) },
            text = {
                TextField(
                    value = text.value,
                    onValueChange = { text.value = it },
                    label = { Text(ResTools.str(R.string.book_mark_edit_title)) }
                )
            },
            confirmButton = {
                AppButton(msg = ResTools.str(R.string.book_mark_save)) {
                    CoroutineTask("updateBookmark").io().launch {
                        bookMarkEntity.title = text.value
                        bookMarksVM.update(bookMarkEntity)
                        showEdit.value = false
                    }
                }
            }
        )
    }

//    AlertCloseAbleContent(state = showEdit) {
//        Column {
//            val resultValue = remember { mutableStateOf(bookMarkEntity.title) }
//            TextField(value = resultValue.value, onValueChange = {
//                resultValue.value = it
//            })
//
//            AppButton(msg = ResTools.str(R.string.book_mark_save)) {
//                CoroutineTask("updateBookmark").io().launch {
//                    bookMarkEntity.title = resultValue.value
//                    bookMarksVM.update(bookMarkEntity)
//                    showEdit.value = false
//                }
//            }
//        }
//    }

    AlertCloseAbleContent(state = showSelected) {
        Column(Modifier.background(MT.C().background)) {
            AppButton(ResTools.str(R.string.book_mark_open_in_new_tab)) {
                appVM.toBrowserPage(bookMarkEntity.url)
                showSelected.value = false
            }

            AppButton(msg = ResTools.str(R.string.book_mark_copy_link)) {
                copyToClipboard(activity, bookMarkEntity.url)
                showSelected.value = false
            }

            AppButton(msg = ResTools.str(R.string.book_mark_remove)) {
                CoroutineTask("deleteBookmark").io().launch {
                    bookMarksVM.deleteBookmark(bookMarkEntity.url)
                }
                showSelected.value = false
            }

            AppButton(msg = ResTools.str(R.string.book_mark_edit)) {
                showSelected.value = false
                showEdit.value = true
            }
        }
    }
    Column(
        Modifier
            .fillMaxWidth()
            .padding(top = 6.dp), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        ImageApp(data = iconUrl.value, contentScale = ContentScale.Fit,
            modifier = Modifier
                .size(36.dp)
                .padding()
                .clip(CircleShape)
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = { appVM.toBrowserPage(bookMarkEntity.url) },
                        onLongPress = { showSelected.value = true }
                    )
                }
        )
        Text(
            text = bookMarkEntity.title,
            Modifier
                .fillMaxWidth()
                .padding(top = 6.dp), maxLines = 2, color = MT.color.onSurface, style = MT.typography.labelSmall, textAlign = TextAlign.Center
        )
    }
}
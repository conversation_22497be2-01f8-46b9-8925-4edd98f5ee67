package free.download.video.downloader.ui.screens

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.lib.bill.manager.BillIntent
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.LocalBillingViewModel

/**
 * <AUTHOR>
 * www.eporner.com
 * @Since 2024/01/12
 * * After updating, I can no longer download in the background. The history is also listed endlessly and cannot be deleted. The app crashes when an ad appears. It is in very unusable condition. Uninstall it after you have finished backing up your videos.
 */

sealed class Screen(val route: String) {
    object Home : Screen("home")
    object Video : Screen("Video")
    object VideoOnLine : Screen("VideoOnLine")
}

@Composable
fun App() {
    LocalBillingViewModel.current.process(BillIntent.CheckUserInSubscriptionPeriod("app"))

    val navController = rememberNavController()
    LocalAppViewModel.current.mNavControl = navController
    NavHost(navController = navController, startDestination = Screen.Home.route) {
        composable(Screen.Home.route) {
            MainTabScreen()
        }
        composable(Screen.Video.route) {
            VideoScreen()
        }
        composable(Screen.VideoOnLine.route) {
            VideoScreen()
        }
    }
}

package free.download.video.downloader.ui.screens.web

import CoroutineTask
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewModelScope
import com.tiny.domain.util.CoroutineUtil
import com.tiny.lib.web.view.sniffer.SnifferDelegate
import com.tiny.lib.web.view.sniffer.TabManager
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.ResTools
import com.tinypretty.component.validHttp
import com.tinypretty.ui.componets.ComposableWrap
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.activity
import com.tinypretty.ui.theme.MT
import free.download.video.downloader.Constants
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.LocalBookmarkViewModel
import free.download.video.downloader.LocalHistoryViewModel
import free.download.video.downloader.R
import free.download.video.downloader.model.history.HistoryEntity
import free.download.video.downloader.viewmodel.app.AppPage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @Since 2024/01/20
 */

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun WebNavigateBar(
    modifier: Modifier,
    editUrl: MutableState<TextFieldValue>,
    appPageState: State<AppPage>,
    bannerVisible: MutableState<Boolean>
) {
    val appPage = appPageState.value
    if (appPage is AppPage.Download || appPage is AppPage.MUSIC) {
        bannerVisible.value = true
        Box(modifier)
    } else {
        AnimatedVisibility(
            modifier = modifier,
            visible = bannerVisible.value,
            enter = slideInVertically(initialOffsetY = { -it }),
            exit = slideOutVertically(targetOffsetY = { -it })
        ) {
            Box(Modifier.fillMaxWidth(), contentAlignment = Alignment.BottomCenter) {
                WebNavigateBar(modifier = Modifier, editUrl = editUrl, appPageState = appPageState)
                ComposableWrap {
                    if (appPage is AppPage.Browser) {
                        val progress = appPage.tabInfo.snifferDelegate.loadingProgress.collectAsState()
                        if (progress.value <= 90) {
                            Box(
                                it
                                    .alpha(0.8f)
                                    .height(2.dp)
                                    .fillMaxWidth()
                                    .background(MT.color.primary), contentAlignment = Alignment.CenterEnd
                            ) {
                                Box(
                                    Modifier
                                        .fillMaxWidth(1 - (progress.value / 100f))
                                        .height(2.dp)
                                        .background(MT.color.onPrimary.copy(alpha = 0.8f))
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun WebNavigateBar(
    modifier: Modifier,
    editUrl: MutableState<TextFieldValue>,
    appPageState: State<AppPage>
) {
    val activity = activity() ?: return
    val appPage = appPageState.value
    val requestFocusTime = appPage.let { if (it is AppPage.Home) it.requestFocus else null } ?: 0
    val keyboardController = LocalSoftwareKeyboardController.current
    val appVM = LocalAppViewModel.current
    val hisVM = LocalHistoryViewModel.current
    val log = GlobalModule.newLog("NavigateBar")
    log.i { "NavigateBar redraw $requestFocusTime" }
    val isFocused = remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val tabInfo = TabManager.currentTab(activity)
    CoroutineTask("requestFocusTime").main().launch {
        if (requestFocusTime > 0) {
            delay(10)
            focusRequester.requestFocus()
            keyboardController?.show()
        }
    }

    if (appPage is AppPage.Browser) {
        LaunchedEffect(tabInfo.snifferDelegate.url()) {
            appPage.tabInfo.snifferDelegate.jsInterface.pageStartUrl.collectLatest {
                val title = it.first ?: ""
                val url = it.second ?: ""
                editUrl.value = TextFieldValue(url)

                // 确保标题和URL都不为空才添加到历史记录
                if (url.validHttp() && title.isNotBlank()) {
                    log.i { "NavigateBar addHistory: title=$title, url=$url" }
                    hisVM.addHistory(title, url)
                } else {
                    log.e { "NavigateBar skip invalid history: title=$title, url=$url" }
                }
            }
        }
    }

    fun go() {
        focusManager.clearFocus()
        TabManager.currentTab()?.run {
            appVM.toBrowserPage(this, editUrl.value.text)
        }
    }

    Row(
        modifier
            .fillMaxWidth()
            .height(Constants.NAVI_BAR_HEIGHT)
            .background(MT.color.surface)
            .padding(horizontal = 6.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        FavButton(snifferDelegate = tabInfo.snifferDelegate)
        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxSize()
                .padding(6.dp)
                .background(MT.color.background, MT.shapes.large)
                .padding(2.dp),
            contentAlignment = Alignment.CenterStart

        ) {
            BasicTextField(
                value = editUrl.value,
                cursorBrush = SolidColor(MT.color.primary), // 设置光标颜色为红色
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                keyboardActions = KeyboardActions(onDone = { go() }),
                onValueChange = {
                    editUrl.value = it
                },
                textStyle = TextStyle(color = MT.color.onBackground),
                singleLine = true,
                modifier = Modifier
                    .focusRequester(focusRequester)
                    .onFocusChanged {
                        isFocused.value = it.isFocused
                        if (it.isFocused) {
                            CoroutineUtil.launch("") {
                                delay(200)
                                withContext(Dispatchers.Main) {
                                    editUrl.value = editUrl.value.copy(selection = TextRange(0, editUrl.value.text.length))
                                }
                            }
                        }
                    }
                    .padding(start = 36.dp, end = 32.dp)
                    .fillMaxWidth(),
            )

            if (editUrl.value.text.isBlank()) {
                Text(
                    text = ResTools.str(R.string.browser_search_hint),
                    color = MT.color.onBackground.copy(0.6f),
                    style = MT.typography.bodySmall,
                    textAlign = TextAlign.Start,
                    modifier = Modifier.padding(start = 36.dp)
                )
            } else if (isFocused.value) {
                ImageApp(
                    data = "res/ic_close.webp",
                    color = MT.color.primary,
                    modifier = Modifier
                        .padding(8.dp)
                        .align(Alignment.CenterEnd)
                        .clickable() { editUrl.value = TextFieldValue("") })
            }

            ImageApp(
                data = "res/nb_icon_search.png",
                color = MT.color.primary.copy(if (editUrl.value.text.isNotBlank()) 1f else 0.1f),
                modifier = Modifier
                    .padding(6.dp)
                    .align(Alignment.CenterStart)
                    .clickable(enabled = editUrl.value.text.isNotBlank()) { go() })
        }
//        VipButton(
//            EventValue.SUBS_FROM_NAVIGATION_BAR,
//            Modifier
//                .size(42.dp)
//                .align(Alignment.CenterVertically)
//        )
//        DebugContent {
//            AppButton(text = "GVIP", onClick = {
//                BillingRepo.onSubscribedByDebug()
//                BillEvent.SUBSCRIPTION_PURCHASE_OFFER_END_SUCCEED.logEvent(Pair(BillKey.FROM, "debug_content_buy"), Pair(BillKey.VALUE, BillValue.SUBSCRIPTION_RESULT_SUBSCRIBED), BillKey.SUBSCRIBED to BillingRepo.isSubscribed())
//            })
//        }
    }
}

@Composable
private fun FavButton(snifferDelegate: SnifferDelegate) {
    val appVM = LocalAppViewModel.current
    val bookMarkVm = LocalBookmarkViewModel.current

    val appPage = appVM.appPage.collectAsState()
    val urlState = snifferDelegate.jsInterface.pageStartUrl.collectAsState()
    val url = if (appPage.value is AppPage.Browser) urlState.value.second ?: "" else ""

    val log = GlobalModule.newLog("FavButton").also { it.i { "redraw url=$url" } }

    val isFavorited = remember { mutableStateOf(false) }
    LaunchedEffect(urlState.value, appPage.value) {
        isFavorited.value = (appPage.value is AppPage.Browser) && bookMarkVm.isUrlBookmarked(url)
    }
    val enabled = appPage.value is AppPage.Browser && url.validHttp()
    ImageApp(
        data = "res/ic_fav.webp",
        modifier = Modifier
            .clickable(enabled) {
                GlobalModule.toast(ResTools.str(if (isFavorited.value) R.string.book_mark_removed else R.string.book_mark_added))

                val title = snifferDelegate.webView()?.title ?: ""
                bookMarkVm.viewModelScope.launch {
                    withContext(Dispatchers.IO) {
                        if (isFavorited.value) {
                            log.i { "FavButton deleteBookmark=${isFavorited.value},url=${url}" }
                            bookMarkVm.deleteBookmark(url)
                        } else {
                            log.i { "FavButton addBookmark=${isFavorited.value},url=${url}" }
                            bookMarkVm.addBookmark(title, url)
                        }
                        isFavorited.value = isFavorited.value.not()
                    }
                }
            }
            .size(Constants.NAVI_BAR_HEIGHT)
            .background(MT.color.surface, shape = RoundedCornerShape(topEndPercent = 50, bottomEndPercent = 50))
            .padding(6.dp),
        color = (if (isFavorited.value) MT.color.primary else MT.color.onSurface).copy(alpha = if (enabled) 1f else 0.1f)
    )
}
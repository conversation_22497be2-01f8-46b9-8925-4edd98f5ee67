package free.download.video.downloader.utils

import com.tinypretty.component.GlobalModule.newLog
import okhttp3.CertificatePinner
import okhttp3.FormBody
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import java.util.concurrent.TimeUnit


object HttpUtil {
    var mL = newLog("HttpUtil")
    val CONTENT_LENGTH = "Content-Length"
    val CONTENT_TYPE = "Content-Type"
    val CONTENT_Disposition = "Content-Disposition"
    val CONTENT_ERROR = "Content-ERROR"
//    fun fuelPostForm(
//        url: String,
//        paramters: ArrayList<Pair<String, String>> = arrayListOf(),
//        charSet: String = "",
//        inHeaders: Map<String, String> = hashMapOf()
//    ): String {
//
//    }

    private fun createBuilder(url: String, headers: HashMap<String, String>?): Request.Builder {
        val mBuilder = Request.Builder()
        mBuilder.url(url)

        headers?.run {
            for (h in this) {
                mBuilder.addHeader(h.key, h.value)
                mL.d { "createBuilder headers $h" }
            }
        }
        return mBuilder
    }

    private fun createHttpClient(): OkHttpClient = mOkHttpClient
    fun Response.responseContent(): String {
        return (body?.string() ?: "").apply {
            body?.close()
        }
    }

    val JSON = "application/json; charset=utf-8".toMediaTypeOrNull()
    fun httpPostJson(
        url: String,
        json: String,
        headers: HashMap<String, String>?
    ): String {
        mL.d { "post start $url $json" }
        var result = ""
        try {
            val requestBody = RequestBody.create(JSON, json)

            val okHttpClient = createHttpClient()
            val mBuilder = createBuilder(url, headers)

            val okHttpRequest = mBuilder.post(requestBody).build()

            var response = okHttpClient.newCall(okHttpRequest).execute()
            result = response.responseContent()
            response.releaseAll()
        } catch (e: Exception) {
        }
        return result
    }

    class HttpResult {
        var mCode = 0
        var mResponse = ""
        override fun toString(): String {
            return "HttpResult code=$mCode response=$mResponse"
        }
    }


    fun addC() {
        try {
            val hostname = "api2.mhpan.com"
            val certificatePinner = CertificatePinner.Builder()
                .add(hostname, "sha256/Vjs8r4z+80wjNcr1YKepWQboSIRi63WsWXhIMN+eWys=")
                .add(hostname, "sha256/CPy/CT/9m62iZ9ljOLEpKgFMJ4z27aqr6xjpF3ODn6A=")
                .add(hostname, "sha256/jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=").build()

            val client = OkHttpClient.Builder()
//                    .certificatePinner(certificatePinner)
                .build()

            val request: Request = Request.Builder()
                .url("https://$hostname")
                .build()

            client.newCall(request).execute().let {
                mL.d { "mhpanSSL ${it.isSuccessful} ${it.responseContent()}" }
            }
//            Certificate pinning failure!
//            Peer certificate chain:
//            sha256/CPy/CT/9m62iZ9ljOLEpKgFMJ4z27aqr6xjpF3ODn6A=: CN=config.mhpan.com
//            sha256/jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=: CN=R3,O=Let's Encrypt,C=US
//            sha256/Vjs8r4z+80wjNcr1YKepWQboSIRi63WsWXhIMN+eWys=: CN=DST Root CA X3,O=Digital Signature Trust Co.
//            Pinned certificates for api2.mhpan.com:
//            sha256/98bd9b54b280c05c3b42852ec7be4cf40dbfccada91169ad30f54270fee958e7
        } catch (e: Exception) {
            mL.d { "mhpanSSL2 $e ${e.message} " }
            mL.d { "mhpanSSL2 $e ${e.localizedMessage} " }
            mL.d { "mhpanSSL2 $e ${e.stackTrace} " }
            mL.d { "mhpanSSL2 $e ${e.toString()} " }

        }
    }

    fun httpPostPHan(
        url: String,
        byteArray: ByteArray
    ): HttpResult? {
        mL.d { "mhpan post start no pin $url ${byteArray?.size}" }
        var result: HttpResult? = null
        try {
            val okHttpClient = mhpanHttpClient

            val mBuilder = createBuilder(url, null)

            val okHttpRequest = mBuilder.post(RequestBody.create(null, byteArray)).build()

            var response = okHttpClient.newCall(okHttpRequest).execute()
            result = HttpResult().apply {
                mCode = response.code
                mResponse = response.responseContent()
            }
            mL.d { "mhpan post result = ${result?.mResponse} code=${result?.mCode}" }
            response.releaseAll()

        } catch (e: Exception) {
            mL.d { "mhpan post error $e" }
            result = null
        }
        return result
    }

    fun httpPost(
        url: String,
        paramters: HashMap<String, String>?,
        headers: HashMap<String, String>?
    ): String {
        mL.d { "post start $url $paramters" }
        var result = ""
        try {
            val requestBody = FormBody.Builder().apply {
                paramters?.run {
                    for (p in this) {
                        add(p.key, p.value)
                        mL.d { "post form add $p" }
                    }
                }
            }
                .build()

            val okHttpClient = createHttpClient()
            val mBuilder = createBuilder(url, headers)

            val okHttpRequest = mBuilder.post(requestBody).build()

            var response = okHttpClient.newCall(okHttpRequest).execute()
            result = response.responseContent()
            response.releaseAll()
        } catch (e: Exception) {
        }
        return result
    }

    fun httpGet(
        inUrl: String,
        headers: HashMap<String, String>?,
        paramters: HashMap<String, String>? = null
    ): String {
        var url = inUrl

        paramters?.run {
            if (paramters.isNotEmpty()) {
                var flag = "?"
                for (p in paramters) {
                    url += flag + p.key + "=" + p.value
                    flag = "&"
                }
            }
        }

        mL.d { "get start $url" }
        var result = ""
        try {
            val okHttpClient = createHttpClient()
            val mBuilder = createBuilder(url, headers)

            val okHttpRequest = mBuilder.get().build()
            var response = okHttpClient.newCall(okHttpRequest).execute()
            result = response.responseContent()
            response.releaseAll()
        } catch (e: Exception) {
        }
        return result
    }

    val mOkHttpClient = OkHttpClient.Builder()
        .readTimeout(10, TimeUnit.SECONDS)
        .writeTimeout(10, TimeUnit.SECONDS)
        .retryOnConnectionFailure(true)
        .build()

    val mhpanHttpClient = OkHttpClient.Builder()
        .readTimeout(10, TimeUnit.SECONDS)
        .writeTimeout(10, TimeUnit.SECONDS)
        .apply {
            val hostname = "api2.mhpan.com"
            val certificatePinner = CertificatePinner.Builder()
                .add(hostname, "sha256/Vjs8r4z+80wjNcr1YKepWQboSIRi63WsWXhIMN+eWys=")
                .add(hostname, "sha256/CPy/CT/9m62iZ9ljOLEpKgFMJ4z27aqr6xjpF3ODn6A=")
                .add(hostname, "sha256/jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=").build()
            certificatePinner(certificatePinner)
        }
        .retryOnConnectionFailure(true)
        .build()

    val mSniffOkhttpClient = OkHttpClient.Builder()
        .readTimeout(10, TimeUnit.SECONDS)
        .writeTimeout(10, TimeUnit.SECONDS)
        .retryOnConnectionFailure(false)
        .build()

    fun httpContentLength(url: String, headers: HashMap<String, String>): Pair<String, Long> {
        mL.d { "httpContentLength start $url" }
        headers.forEach {
            mL.d { "httpContentLength header ${it.key} ${it.value}\n" }
        }

        var type = ""
        var lenLong = 0L

        try {
            val mBuilder = Request.Builder()
            mBuilder.url(url)
            for (h in headers) {
                mBuilder.addHeader(h.key, h.value)
            }

            val okHttpRequest = mBuilder.head().build()
            var response = mSniffOkhttpClient.newCall(okHttpRequest).execute()
            var len = response.header(CONTENT_LENGTH) ?: "0"
            type = response.header(CONTENT_TYPE) ?: ""
            lenLong = len.toLong()
            response.releaseAll()
            mL.i { "httpContentLength response  -> Len=$lenLong" }
        } catch (e: Exception) {
            mL.e { "httpContentLength exception -> ${e.message}" }
        }


        return Pair(type, lenLong)
    }

    fun httpContent(url: String, headers: HashMap<String, String>): HashMap<String, String> {
        mL.d { "httpContent start $url" }

        var result = hashMapOf<String, String>()
        try {
            val mBuilder = Request.Builder()
            mBuilder.url(url)
            for (h in headers) {
                mBuilder.addHeader(h.key, h.value)
            }

            val okHttpRequest = mBuilder.head().build()
            var response = mSniffOkhttpClient.newCall(okHttpRequest).execute()

            result[CONTENT_TYPE] = response.header(CONTENT_TYPE) ?: ""
            result[CONTENT_Disposition] = (response.header(CONTENT_Disposition) ?: "").let {
                var index = it.indexOf("filename=")
                var result = it
                if (index > 0) {
                    result = it.substring(index + "filename=".length)
                }
                result
            }
            result[CONTENT_LENGTH] = response.header(CONTENT_LENGTH) ?: "0"
            result[CONTENT_ERROR] = "succeed=${response.isSuccessful} message=${response.message}"
            response.releaseAll()

        } catch (e: Exception) {
        }
        mL.d { "httpContent end result=$result url=$url " }
        return result
    }

    fun Response.releaseAll() {
        close()
        body?.let {
            mL.d { "releaseAll" }
            it.close()
            it.source().close()
            it.charStream().close()
            it.byteStream().close()
        }
    }
}
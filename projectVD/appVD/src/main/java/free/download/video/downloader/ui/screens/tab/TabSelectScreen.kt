package free.download.video.downloader.ui.screens.tab

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.tiny.domain.ext.str
import com.tiny.lib.web.view.sniffer.TabManager
import com.tiny.lib.web.view.sniffer.TabManager.index
import com.tiny.lib.web.view.sniffer.TabManager.selected
import com.tiny.lib.web.view.sniffer.bean.TabInfo
import com.tinypretty.component.GlobalModule.activity
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.SpacerFix
import com.tinypretty.ui.container.CloseableBox
import com.tinypretty.ui.container.CloseableTopRightBox
import com.tinypretty.ui.container.TouchToCloseBox
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clipBorder
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.R
import free.download.video.downloader.ui.components.AppButton

@Composable
fun TabSelectScreen(mutableState: MutableState<Boolean>) {
    val activity = activity() ?: return
    val appVM = LocalAppViewModel.current
    val currentTabIndex = TabManager.currentTabIndex()
    if (mutableState.value) {
        val onDismiss = { mutableState.value = false }
        TouchToCloseBox(onDismiss = onDismiss) {
            CloseableBox(
                modifier = Modifier
                    .align(Alignment.Center)
                    .offset(y = (-46).dp)
                    .wrapContentHeight()
                    .fillMaxWidth(),
                onDismiss = onDismiss
            ) {
                Column(
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 36.dp), verticalArrangement = Arrangement.spacedBy(0.dp)
                ) {
                    TabManager.tabList().toList().reversed().forEachIndexed { _, tabInfo ->
                        TabItem(tabInfo, tabInfo.index() == currentTabIndex) { _, close ->
                            if (close) {
                                TabManager.deletePage(tabInfo)
                                TabManager.lastSelectedTab()?.run {
                                    appVM.toBrowserPage(this, null, requestFocus = false, changeTab = true)
                                }
                                mutableState.value = false
                                mutableState.value = true

                            } else {
                                mutableState.value = false
                                appVM.toBrowserPage(tabInfo, null, changeTab = true)
                            }
                        }
                    }
                    24.SpacerFix()
                    AppButton(text = R.string.tab_new.str(), modifier = Modifier.fillMaxWidth(), onClick = {
                        TabManager.newTab(activity).selected()
                        appVM.toHomePage()
                        mutableState.value = false
                    })
                }
            }
        }
    }
}

@Composable
private fun TabItem(tabInfo: TabInfo, selected: Boolean, onClick: (TabInfo, close: Boolean) -> Unit) {
    val sn = tabInfo.snifferDelegate
    val background = if (selected) MT.color.surface else MT.color.background
    val textColor = if (selected) MT.color.primary else MT.color.onBackground
    CloseableTopRightBox(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        backHandler = false,
        borderColor = textColor,
        closeable = tabInfo.index() != 0,
        onDismiss = { onClick.invoke(tabInfo, true) }) {
        Column(
            Modifier
                .fillMaxWidth()
                .background(background)
                .padding(12.dp)
                .clickable {
                    onClick(tabInfo, false)
                }, verticalArrangement = Arrangement.Center
        ) {
            if (sn.url().isEmpty()) {
                ImageApp(
                    data = "res/ic_home.webp", modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .size(36.dp)
                        .padding(6.dp), color = MT.color.primary, contentScale = ContentScale.FillBounds
                )
            } else {
                Row(
                    Modifier
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        Modifier
                            .size(16.dp)
                            .clipBorder(1.dp, background, CircleShape)
                    ) {
                        ImageApp(
                            data = R.mipmap.ic_launcher_foreground,
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.FillBounds
                        )
                    }

                    6.SpacerFix()
                    Text(text = sn.title(), color = textColor, style = MT.typography.titleSmall, maxLines = 1)
                }
                Text(text = sn.url(), color = textColor.copy(0.6f), style = MT.typography.bodySmall, maxLines = 1)
            }
        }
    }
}


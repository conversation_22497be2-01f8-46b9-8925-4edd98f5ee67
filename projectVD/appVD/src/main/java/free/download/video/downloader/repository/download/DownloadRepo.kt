package free.download.video.downloader.repository.download

import androidx.room.Room
import com.tinypretty.component.GlobalModule.mApp
import com.tinypretty.component.GlobalModule.mL
import free.download.video.downloader.model.download.DownloadDatabase
import free.download.video.downloader.model.download.FileEntity
import free.download.video.downloader.utils.DatabaseManager
import free.download.video.downloader.utils.DatabaseMigrationHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object DownloadRepo {
    private val TAG = "DownloadRepo"

    val downloadTaskList by lazy {
        try {
            Room.databaseBuilder(
                mApp, DownloadDatabase::class.java,
                "database_download"
            )
                .addMigrations(DatabaseMigrationHelper.DOWNLOAD_MIGRATION_1_2) // 添加迁移策略
                .fallbackToDestructiveMigration() // 如果迁移失败，允许破坏性迁移
                .build()
                .downloadDao()
        } catch (e: Exception) {
            mL.e { "$TAG -> Error initializing downloadTaskList database: ${e.message}" }

            // 尝试恢复数据库
            val restored = DatabaseMigrationHelper.restoreDatabase("database_download")
            if (restored) {
                mL.d { "$TAG -> Database restored from backup, trying to open again" }
                try {
                    return@lazy Room.databaseBuilder(
                        mApp, DownloadDatabase::class.java,
                        "database_download"
                    )
                        .fallbackToDestructiveMigration()
                        .build()
                        .downloadDao()
                } catch (e2: Exception) {
                    mL.e { "$TAG -> Error opening restored database: ${e2.message}" }
                }
            }

            // 如果恢复失败，尝试重建数据库
            Room.databaseBuilder(
                mApp, DownloadDatabase::class.java,
                "database_download_new"
            )
                .fallbackToDestructiveMigration()
                .build()
                .downloadDao()
        }
    }

    val recent by lazy {
        try {
            Room.databaseBuilder(
                mApp, DownloadDatabase::class.java,
                "database_recent"
            )
                .addMigrations(DatabaseMigrationHelper.DOWNLOAD_MIGRATION_1_2) // 添加迁移策略
                .fallbackToDestructiveMigration() // 如果迁移失败，允许破坏性迁移
                .build()
                .downloadDao()
        } catch (e: Exception) {
            mL.e { "$TAG -> Error initializing recent database: ${e.message}" }

            // 尝试恢复数据库
            val restored = DatabaseMigrationHelper.restoreDatabase("database_recent")
            if (restored) {
                mL.d { "$TAG -> Recent database restored from backup, trying to open again" }
                try {
                    return@lazy Room.databaseBuilder(
                        mApp, DownloadDatabase::class.java,
                        "database_recent"
                    )
                        .fallbackToDestructiveMigration()
                        .build()
                        .downloadDao()
                } catch (e2: Exception) {
                    mL.e { "$TAG -> Error opening restored recent database: ${e2.message}" }
                }
            }

            // 如果恢复失败，尝试重建数据库
            Room.databaseBuilder(
                mApp, DownloadDatabase::class.java,
                "database_recent_new"
            )
                .fallbackToDestructiveMigration()
                .build()
                .downloadDao()
        }
    }

    /**
     * 安全地插入下载记录，会先检查数据库记录数量
     * @param file 要插入的文件实体
     * @param isRecent 是否是最近的记录
     * @return 插入的记录ID
     */
    fun safeInsert(file: FileEntity, isRecent: Boolean = false) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = if (isRecent) recent else downloadTaskList
                // 检查数据库记录数量
                DatabaseManager.checkBeforeInsert(dao)
                // 插入记录
                val id = dao.insert(file)
                mL.d { "$TAG -> Inserted record with ID: $id, isRecent: $isRecent" }
            } catch (e: Exception) {
                mL.e { "$TAG -> Error inserting record: ${e.message}" }
            }
        }
    }

    /**
     * 安全地插入多个下载记录，会先检查数据库记录数量
     * @param files 要插入的文件实体列表
     * @param isRecent 是否是最近的记录
     */
    fun safeInsertAll(files: ArrayList<FileEntity>, isRecent: Boolean = false) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val dao = if (isRecent) recent else downloadTaskList
                // 检查数据库记录数量
                DatabaseManager.checkBeforeInsert(dao)
                // 插入记录
                dao.insert(files)
                mL.d { "$TAG -> Inserted ${files.size} records, isRecent: $isRecent" }
            } catch (e: Exception) {
                mL.e { "$TAG -> Error inserting records: ${e.message}" }
            }
        }
    }
}
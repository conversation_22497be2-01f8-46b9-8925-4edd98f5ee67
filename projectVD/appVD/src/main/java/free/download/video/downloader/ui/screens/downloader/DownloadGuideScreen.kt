package free.download.video.downloader.ui.screens.downloader

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.component.ResTools
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.Spacer
import com.tinypretty.ui.componets.SpacerFix
import com.tinypretty.ui.dialogs.AlertCloseAbleContent
import com.tinypretty.ui.theme.MT
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.R
import free.download.video.downloader.repository.cache.CachedValue
import free.download.video.downloader.ui.components.AppButton

fun LazyListScope.downloadGuideItem() {
    if (!CachedValue.showGuide) {
        return
    }

    item {
        val showGuide = remember { mutableStateOf(false) }
        val app = LocalAppViewModel.current
        AlertCloseAbleContent(state = showGuide) {
            DownloadGuideScreen()
        }

        DownloadTitleBox(title = ResTools.str(R.string.download_task_no_video_hint)) {
            Column {
                6.SpacerFix()
                AppButton(text = ResTools.str(R.string.home_video_tutorial), modifier = Modifier.fillMaxWidth(), onClick = {
                    showGuide.value = true
                })
            }
        }
    }
}

@Composable
fun DownloadGuideScreen() {
    val log = newLog("downloadGuideScreen")
    log.i { "CachedValue.showGuide = ${CachedValue.showGuide}" }
    if (!CachedValue.showGuide) {
        return
    }
    DownloadTitleBox(title = ResTools.str(R.string.home_video_tutorial_title)) {
        LazyColumn {
            val title = listOf(R.string.home_video_tutorial_1, R.string.home_video_tutorial_2, R.string.home_video_tutorial_3)
            List(3) {
                "res/guide_${it + 1}.webp"
            }.forEachIndexed { index, s ->
                item {
                    Spacer(dpValue = 12)
                }

                item {
                    Text(text = ResTools.str(title[index]), style = MT.typography.titleMedium, color = MT.color.onBackground, textAlign = TextAlign.Start, modifier = Modifier.fillMaxWidth())
                    ImageApp(data = s, modifier = Modifier.fillMaxWidth(), contentScale = ContentScale.FillWidth)
                }
            }
            item {
                Spacer(dpValue = 12)
            }
        }
    }

}
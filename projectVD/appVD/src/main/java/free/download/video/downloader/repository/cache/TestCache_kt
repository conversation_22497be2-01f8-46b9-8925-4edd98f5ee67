import com.tiny.domain.ext.Cache
import com.tiny.domain.ext.getValue
import com.tiny.domain.ext.saveValue

class User(val name: String, val age: Int, val task: ArrayList<String>)
var mUser: User?
    set(value) = value.saveValue("user_key")
    get() = getValue("user_key")

var mUserList: ArrayList<User>?
    set(value) = value.saveValue("user_key_list")
    get() = getValue("user_key_list")
fun testCache() {
    mUserList = null
    mUser = null
    var start = System.currentTimeMillis()
    Cache.log.d { "test $mUser" }
    Cache.log.d { "test cost ${System.currentTimeMillis() - start}" }
    start = System.currentTimeMillis()
    Cache.log.d { "test ${mUserList?.size} $mUserList" }
    Cache.log.d { "test cost ${System.currentTimeMillis() - start}" }
    mUser?.let {
        println("user: $it")
    }

    start = System.currentTimeMillis()

    arrayListOf<User>().apply {
        addAll(mUserList ?: arrayListOf())
        for (i in 0..1000) {
            add(User("name$i", i, arrayListOf("task1", "task2")))
        }
        mUserList = this
    }
    Cache.log.d { "test set cost ${System.currentTimeMillis() - start}" }
    mUser = User("name", 1, arrayListOf("task1", "task2"))
}
package free.download.video.downloader.model.history

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import free.download.video.downloader.model.download.DatabaseConverters

@Database(entities = [HistoryEntity::class], version = 1, exportSchema = false)
@TypeConverters(DatabaseConverters::class)
abstract class HistoryDatabase : RoomDatabase() {
    abstract fun historyDao(): HistoryDao
}

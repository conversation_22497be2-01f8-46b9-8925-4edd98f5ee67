package free.download.video.downloader.utils;

import android.content.Context
import android.database.Cursor
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import androidx.activity.ComponentActivity
import androidx.loader.app.LoaderManager
import androidx.loader.content.CursorLoader
import androidx.loader.content.Loader
import com.tinypretty.component.GlobalModule.newLog
import kotlinx.coroutines.suspendCancellableCoroutine

class VideoLoaderCallbacks(private val context: Context, private val callback: (List<Uri>) -> Unit) : androidx.loader.app.LoaderManager.LoaderCallbacks<Cursor> {
    override fun onCreateLoader(id: Int, args: Bundle?): Loader<Cursor> {
        val uri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
        val projection = arrayOf(MediaStore.Video.Media._ID)
        return CursorLoader(context, uri, projection, null, null, null)
    }

    override fun onLoadFinished(loader: Loader<Cursor>, data: Cursor?) {
        val videos = mutableListOf<Uri>()
        data?.let {
            val idColumn = it.getColumnIndexOrThrow(MediaStore.Video.Media._ID)
            while (it.moveToNext()) {
                val id = it.getLong(idColumn)
                val contentUri = Uri.withAppendedPath(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, id.toString())
                videos.add(contentUri)
            }
        }
        callback(videos)
    }

    override fun onLoaderReset(loader: Loader<Cursor>) {
        // Handle the case where the loader is reset (e.g. due to configuration change)
    }

    companion object {
        private val log = newLog("VideoLoaderCallbacks")


        fun getVideoDetails(context: Context, videoUri: Uri): VideoDetails? {
            val projection = arrayOf(
                MediaStore.Video.Media.DATA,
                MediaStore.Video.Media.DURATION,
                MediaStore.Video.Media.DATE_ADDED,
                MediaStore.Video.Thumbnails.DATA
            )

//            context.contentResolver.query(videoUri, projection, null, null, null)?.use { cursor ->
//                if (cursor.moveToFirst()) {
//                    val path = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATA))
//                    val duration = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DURATION))
//                    val creationTime = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_ADDED)) * 1000 // convert to milliseconds
//                    val thumbnailPath = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Video.Thumbnails.DATA))
//                    val thumbnail = BitmapFactory.decodeFile(thumbnailPath)
//                    val size = File(path).length() // get the file size in bytes
//                    return VideoDetails(videoUri, path, duration, creationTime,
//                        thumbnail, size)
//                }
//            }

            return null
        }

        suspend fun loadVideos(activity: ComponentActivity, dirToRefresh: List<String>) = suspendCancellableCoroutine {
            val result = mutableListOf<VideoDetails>()
            log.i { "loadVideos start" }
            MediaScannerConnection.scanFile(activity, dirToRefresh.toTypedArray(), null) { path, uri ->

            }
            LoaderManager.getInstance(activity).restartLoader(0, null, VideoLoaderCallbacks(activity) { videos ->
                // 在这里处理视频列表
                videos.forEach { video ->
                    getVideoDetails(activity, video)?.let {
                        result.add(it)
                        log.i { "$it" }
                    }
                }

                if (!it.isCompleted) {
                    it.resume(result) {
                        log.i { "loadVideos cancelled" }
                    }
                }
            })
        }
    }

    data class VideoDetails(
        val uri: Uri,
        val path: String,
        val duration: Long,
        val creationTime: Long,
        val cover: String?,
        val size: Long
    ) {
        override fun toString(): String {
            return "VideoDetails(uri=$uri, path='$path', duration=$duration, creationTime=$creationTime, thumbnail=$cover)"
        }
    }
}
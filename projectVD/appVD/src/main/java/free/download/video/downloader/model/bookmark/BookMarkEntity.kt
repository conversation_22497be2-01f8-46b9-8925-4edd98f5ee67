package free.download.video.downloader.model.bookmark

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * <AUTHOR>
 * @Since 2024/01/06
 */
@Entity(tableName = "bookmark")
class BookMarkEntity {
    @PrimaryKey(autoGenerate = true)
    var id: Long = 0

    @ColumnInfo(name = "url")
    var url: String = ""

    @ColumnInfo(name = "title")
    var title: String = ""

    @ColumnInfo(name = "icon")
    var icon: String = ""

    @ColumnInfo(name = "exifJson")
    var exifJson: String = ""
}
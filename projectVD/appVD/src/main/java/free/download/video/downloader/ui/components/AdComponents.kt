package free.download.video.downloader.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.window.Dialog
import com.tiny.ad.old.AdShowResult
import com.tinypretty.ui.componets.SimpleLoadingHint
import free.download.video.downloader.utils.AdManager
import kotlinx.coroutines.launch

/**
 * 显示插屏广告的组件
 * @param maxWaitTime 最大等待时间，单位毫秒
 * @param showLoadingDialog 是否显示加载对话框
 * @param onAdResult 广告显示结果回调
 */
@Composable
fun ShowInterstitialAd(
    maxWaitTime: Long = 5000,
    showLoadingDialog: Boolean = true,
    onAdResult: (AdShowResult) -> Unit
) {
    val scope = rememberCoroutineScope()
    val isLoading = remember { mutableStateOf(false) }
    
    // 如果需要显示加载对话框且等待时间大于5秒
    if (showLoadingDialog && maxWaitTime > 5000) {
        if (isLoading.value) {
            Dialog(onDismissRequest = { isLoading.value = false }) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.7f)),
                    contentAlignment = Alignment.Center
                ) {
                    SimpleLoadingHint()
                }
            }
        }
    }
    
    LaunchedEffect(Unit) {
        isLoading.value = true
        scope.launch {
            val result = AdManager.showInterstitialAd(maxWaitTime, false)
            isLoading.value = false
            onAdResult(result)
        }
    }
}

/**
 * 显示激励广告的组件
 * @param maxWaitTime 最大等待时间，单位毫秒
 * @param showLoadingDialog 是否显示加载对话框
 * @param onAdResult 广告显示结果回调
 */
@Composable
fun ShowRewardedAd(
    maxWaitTime: Long = 5000,
    showLoadingDialog: Boolean = true,
    onAdResult: (AdShowResult) -> Unit
) {
    val scope = rememberCoroutineScope()
    val isLoading = remember { mutableStateOf(false) }
    
    // 如果需要显示加载对话框且等待时间大于5秒
    if (showLoadingDialog && maxWaitTime > 5000) {
        if (isLoading.value) {
            Dialog(onDismissRequest = { isLoading.value = false }) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.7f)),
                    contentAlignment = Alignment.Center
                ) {
                    SimpleLoadingHint()
                }
            }
        }
    }
    
    LaunchedEffect(Unit) {
        isLoading.value = true
        scope.launch {
            val result = AdManager.showRewardedAd(maxWaitTime, false)
            isLoading.value = false
            onAdResult(result)
        }
    }
}

package free.download.video.downloader.utils

import CoroutineTask
import android.content.Context
import androidx.work.Constraints
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.tiny.domain.ext.safeResume
import com.tinypretty.component.GlobalModule
import free.download.video.downloader.viewmodel.download.FileDownloaderViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.suspendCancellableCoroutine

/**
 * <AUTHOR>
 * @Since 2024/03/10
 */
object DownloadWorker {
    val log = GlobalModule.newLog("DownloadWorker")

    class DownloadWorker(appContext: Context, workerParams: WorkerParameters) : Worker(appContext, workerParams) {
        override fun doWork(): Result {
            return runBlocking {
                log.i { "doWork start" }
                startTask()
                log.i { "doWork end" }
                Result.success()
            }
        }
    }

    var startTaskJob: Job? = null
    suspend fun startTask() =
        suspendCancellableCoroutine { continuation ->
            startTaskJob = CoroutineTask("doWork Check").io().launch {
                FileDownloaderViewModel.downloading.collect { list ->

                    val count = list.filter { !it.done && !it.pause }.size
                    log.i { "collect running=${count},list=${list.size}" }
                    if (count == 0) {
                        continuation.safeResume(Unit)
                    }
                }
            }
        }

    fun startDownload(context: Context) {
        log.i { "startDownload downloadRequest" }
        enqueueDownload(context)
    }

    fun enqueueDownload(context: Context) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val downloadRequest = OneTimeWorkRequestBuilder<DownloadWorker>()
            .setConstraints(constraints)
            .build()

        WorkManager.getInstance(context).enqueue(downloadRequest)
    }
}
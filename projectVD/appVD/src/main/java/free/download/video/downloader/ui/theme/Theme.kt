package free.download.video.downloader.ui.theme

import android.app.Activity
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

private val DarkColorScheme = darkColorScheme(
    primary = YELLOW,
    secondary = YELLOW.copy(0.8f),
    tertiary = YELLOW.copy(0.6f),
    background = BLACK_DARK,
    surface = BLACK_LIGHT,
    onPrimary = Color.Black.copy(0.9f),
    onSecondary = Color.White.copy(0.8f),
    onTertiary = Color.White.copy(0.6f),
    onBackground = Color.White.copy(0.8f),
    onSurface = Color.White.copy(0.8f),
)

private val LightColorScheme = DarkColorScheme

@Composable
fun VDTheme(
    darkTheme: Boolean = true,
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = DarkColorScheme
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.surface.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
package free.download.video.downloader.ui.screens.player

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.container.ScrollableRow
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clipBorder
import free.download.video.downloader.analysis.EventValue
import free.download.video.downloader.bean.ParseResultItemInfo
import free.download.video.downloader.model.download.FileEntity
import free.download.video.downloader.repository.cache.CachedValue
import free.download.video.downloader.ui.components.VipButton
import free.download.video.downloader.utils.MemoryUtil
import kotlinx.coroutines.flow.MutableStateFlow

/**
 * <AUTHOR>
 * @Since 2024/01/21
 */

@Composable
fun PlayerOnLineScreen(fileEntity: List<ParseResultItemInfo>) {
    PlayerOnLineScreenContent(fileEntity)
}

sealed class PlayState {
    data class Play(val file: ParseResultItemInfo) : PlayState()
    data class Switch(val file: ParseResultItemInfo) : PlayState()
}

@Composable
fun PlayerOnLineScreenContent(fileEntityList: List<ParseResultItemInfo>) {
    CachedValue.showGuide = false
    val fileEntity = fileEntityList.firstOrNull() ?: return
    val currentSelectedFile = remember { mutableStateOf<PlayState>(PlayState.Play(fileEntity)) }
    val playState = currentSelectedFile.value
    val vipClicked = rememberSaveable { mutableStateOf(false) }

    if (playState is PlayState.Switch) {
        currentSelectedFile.value = PlayState.Play(playState.file)
    } else if (playState is PlayState.Play) {
        Column {
            Box(modifier = Modifier.weight(1f)) {
                PlayerScreen(parseResultItemInfo = playState.file)
            }

            ScrollableRow(Modifier.padding(end = 12.dp)) {

                fileEntityList.forEach {
                    it.fileEntity?.let { fe ->
                        val clickAble = fe.url != playState.file.fileEntity?.url
                        PlayerItem(
                            Modifier
                                .alpha(if (clickAble) 1f else 0.3f)
                                .clickable(clickAble) {
                                    currentSelectedFile.value = PlayState.Play(it)
                                    vipClicked.value = true
                                }, fileEntity = fe, fileSizeFlow = it.fileSize, head = it.head, foot = it.foot, it.byJs
                        )
                    }
                }

                VipButton(EventValue.SUBS_FROM_ONLINE_PLAY, Modifier.size(56.dp))
            }
        }
    }
}

@Composable
private fun PlayerItem(modifier: Modifier, fileEntity: FileEntity, fileSizeFlow: MutableStateFlow<Long>, head: String, foot: String, byJs: Boolean) {
    val fontColor = if (byJs) MT.color.primary else MT.color.onBackground
    val fileSize = fileSizeFlow.collectAsState()
    Box(
        modifier
            .wrapContentWidth()
            .padding(start = 2.dp, bottom = 2.dp)
            .clipBorder(1.dp, fontColor.copy(0.7f), MT.shapes.small)
            .background(MT.color.surface.copy(0.95f)), contentAlignment = Alignment.CenterEnd
    ) {
        Column(
            Modifier
                .wrapContentSize()
                .padding(start = 12.dp, end = 24.dp, top = 6.dp, bottom = 6.dp), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = head,
                style = MT.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                maxLines = 1,
                color = fontColor
            )
            Text(
                text = MemoryUtil().formatSize(fileSize.value),
                style = MT.typography.bodySmall,
                maxLines = 1,
                color = fontColor
            )
            Text(
                text = foot,
                maxLines = 1,
                fontWeight = FontWeight.Bold,
                style = MT.typography.labelSmall,
                color = fontColor
            )
        }
        ImageApp(
            data = "res/ic_play.webp",
            Modifier
                .align(Alignment.CenterEnd)
                .size(24.dp, 24.dp)
                .padding(6.dp), color = MT.color.primary
        )
    }
}
package free.download.video.downloader

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import com.lib.bill.manager.BillingRepo
import com.lib.bill.manager.BillingViewModel
import com.mandi.common.ad.AdmobFactory
import com.tiny.ad.network.AdRepo
import com.tiny.domain.util.FileUtil
import com.tinypretty.component.GlobalModule
import free.download.video.downloader.ui.components.AdUtil
import free.download.video.downloader.ui.screens.App
import free.download.video.downloader.ui.theme.VDTheme
import free.download.video.downloader.utils.DownloadWorker
import free.download.video.downloader.viewmodel.app.AppViewModel
import free.download.video.downloader.viewmodel.bookmark.BookMarkViewModel
import free.download.video.downloader.viewmodel.download.FileDownloaderViewModel
import free.download.video.downloader.viewmodel.history.HistoryViewModel
import CoroutineTask
import kotlinx.coroutines.delay

/**
 * <AUTHOR>
 * @Since 2023/09/27
 */
val LocalBookmarkViewModel = staticCompositionLocalOf<BookMarkViewModel> { error("No BookmarkViewModel provided") }
val LocalAppViewModel = staticCompositionLocalOf<AppViewModel> { error("No SnifferDelegate provided") }
val LocalHistoryViewModel = staticCompositionLocalOf<HistoryViewModel> { error("No HistoryViewModel provided") }
val LocalFileDownloaderViewModel = staticCompositionLocalOf<FileDownloaderViewModel> { error("No FileDownloaderViewModel provided") }
val LocalBillingViewModel = staticCompositionLocalOf<BillingViewModel> { error("No HistoryViewModel provided") }

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        GlobalModule.activity = { this }
        FileUtil.setupOnActivityCreate(this)

        AdRepo.adEnable = {
            if (AdUtil.inNoAdMode()) {
                false
            } else {
                !BillingRepo.subscribedStateFlow.value
            }
        }

        // 应用启动时预加载插屏广告
        CoroutineTask("preloadAdsOnStart").io().launch {
            // AdmobFactory会自动等待SDK初始化完成
            free.download.video.downloader.utils.AdManager.preloadInterstitialAds()
        }

        setContent {
            VDTheme {
                BillingRepo.init(this)

                CompositionLocalProvider(
                    LocalBookmarkViewModel provides BookMarkViewModel(),
                    LocalAppViewModel provides AppViewModel(),
                    LocalHistoryViewModel provides HistoryViewModel(),
                    LocalFileDownloaderViewModel provides FileDownloaderViewModel,
                    LocalBillingViewModel provides BillingViewModel()
                ) {
                    App()
                }
            }
        }
//        val intent = Intent(this, DownloadService::class.java)
//        startService(intent)
    }

    override fun onPause() {
        super.onPause()
        DownloadWorker.startDownload(this)
    }
}
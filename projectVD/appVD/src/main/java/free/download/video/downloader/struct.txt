 - myapp
              - shared
                - repository
                  - SharedRepo.kt
              - home
                - model
                  - HomeState.kt
                  - HomeResult.kt
                - view
                  - HomeScreen.kt
                - intent
                  - HomeIntent.kt
                - viewmodel
                  - HomeViewModel.kt
              - detail
                - model
                  - DetailState.kt
                  - DetailResult.kt
                - view
                  - DetailScreen.kt
                - intent
                  - DetailIntent.kt
                - viewmodel
                  - DetailViewModel.kt
              - setting
                - model
                  - SettingState.kt
                  - SettingResult.kt
                - view
                  - SettingScreen.kt
                - intent
                  - SettingIntent.kt
                - viewmodel
                  - SettingViewModel.kt
package free.download.video.downloader.viewmodel.app

import android.app.Activity
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.ViewModel
import com.tiny.lib.web.view.sniffer.TabManager
import com.tiny.lib.web.view.sniffer.TabManager.selected
import com.tiny.lib.web.view.sniffer.bean.TabInfo
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.component.validUrl
import free.download.video.downloader.bean.ParseResultItemInfo
import free.download.video.downloader.ui.screens.Screen
import kotlinx.coroutines.flow.MutableStateFlow
import java.io.File
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

/**
 * <AUTHOR>
 * @Since 2024/01/12
 */

sealed class AppPage {
    data class Browser(var tabInfo: TabInfo, val requestFocus: Boolean = false, var changeTab: Boolean = false) : AppPage()
    data object Download : AppPage()
    data object MUSIC : AppPage()
    data class Home(val requestFocus: Long? = 0) : AppPage()
}

sealed class VideoPage {
    data class PlayLocal(val file: File) : VideoPage()
    data class PlayUrl(val file: List<ParseResultItemInfo>) : VideoPage()
}


class AppViewModel : ViewModel() {
    var mNavControl: androidx.navigation.NavController? = null
    private val log = newLog("AppViewModel")
    val appPage = MutableStateFlow<AppPage>(AppPage.Home(null))
    val videoPage = MutableStateFlow<VideoPage?>(null)
    private fun generateGoogleSearchUrl(keyword: String): String {
        val baseUrl = "https://www.google.com/search?q="
        val encodedKeyword = URLEncoder.encode(keyword, StandardCharsets.UTF_8.toString())
        return baseUrl + encodedKeyword
    }

    fun toBrowserPage(url: String?, requestFocus: Boolean = false) {
        val tabInfo = TabManager.currentTab() ?: return
        toBrowserPage(tabInfo, url, requestFocus)
    }

    fun toBrowserPage(tabInfo: TabInfo, url: String?, requestFocus: Boolean = false, changeTab: Boolean = false) {
        hideKeyboard()

        tabInfo.selected()
        if (url != null) {
            var formattedUrl = url
            if (!formattedUrl.contains(".")) {
                formattedUrl = generateGoogleSearchUrl(url)
            }
            if (!formattedUrl.validUrl()) {
                formattedUrl = "https://$formattedUrl"
            }
            log.i { "browser updateEditingUrl url=$url,formattedUrl=$formattedUrl" }
            tabInfo.snifferDelegate.loadUrl(formattedUrl)
        } else if (tabInfo.snifferDelegate.url().isEmpty()) {
            toHomePage(true)
            return
        }

        appPage.value = AppPage.Browser(tabInfo, requestFocus = requestFocus, changeTab = changeTab)
    }

    fun toHomePage(requestFocus: Boolean = false) {
        log.i { "toHomePage $requestFocus" }
        appPage.value = AppPage.Home(if (requestFocus) System.currentTimeMillis() else null)
    }

    fun toDownloadedPage() {
        log.i { "toDownloadedTaskPage" }
        appPage.value = AppPage.Download
    }

    fun toMusic() {
        log.i { "toDownloadedTaskPage" }
        appPage.value = AppPage.MUSIC
    }

    fun playVideo(file: File) {
        log.i { "playVideo" }
        videoPage.value = VideoPage.PlayLocal(file)
        mNavControl?.navigate(Screen.Video.route)
    }

    fun playVideo(file: List<ParseResultItemInfo>) {
        log.i { "playVideo" }
        videoPage.value = VideoPage.PlayUrl(file)
        mNavControl?.navigate(Screen.Video.route)
    }

    fun intoBrowser(run: () -> Unit) {
        if (appPage.value !is AppPage.Browser) {
            TabManager.currentTab()?.run {
                toBrowserPage(this, null)
            }
        } else {
            run()
        }
    }

    fun hideKeyboard() {
        val activity = GlobalModule.activity() ?: return
        try {
            val inputMethodManager = activity.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
            // Check if no view has focus:
            val view = activity.currentFocus
            if (view != null) {
                inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
            }
        } catch (_: Exception) {

        }

    }
}

package free.download.video.downloader.ui.screens.player

import android.content.Context
import android.content.pm.ActivityInfo
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import com.shuyu.gsyvideoplayer.player.PlayerFactory
import com.shuyu.gsyvideoplayer.utils.OrientationUtils
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
import com.tiny.domain.util.dpToPix
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.findActivity
import tv.danmaku.ijk.media.exo2.Exo2PlayerManager

class CustomGSYVideoPlayer(context: Context) : StandardGSYVideoPlayer(context) {
    init {
        setPlayManager
    }

    companion object {
        val setPlayManager by lazy {
            PlayerFactory.setPlayManager(Exo2PlayerManager::class.java)
        }
    }

    var onBackClickListener: (() -> Unit)? = null
    var onSpeedChangeHint: (msg: String) -> Unit = {
        GlobalModule.toast(it)
    }

    override fun startWindowFullscreen(context: Context?, actionBar: Boolean, statusBar: Boolean): CustomGSYVideoPlayer {
        // 获取视频的长宽比
        val aspectRatio = currentVideoWidth.toFloat() / currentVideoHeight.toFloat()

        // 根据长宽比来决定全屏模式
        val orientation = if (aspectRatio > 1) {
            // 如果长宽比大于1，那么使用横屏模式
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        } else {
            // 如果长宽比小于或等于1，那么使用竖屏模式
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }

        // 设置全屏模式
        val orientationUtils = OrientationUtils(context?.findActivity(), this)
        orientationUtils.isEnable = false
        orientationUtils.resolveByClick()
        orientationUtils.isEnable = true
        orientationUtils.isLand = if (orientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) 1 else 0

        return super.startWindowFullscreen(context, actionBar, statusBar) as CustomGSYVideoPlayer
    }

    init {
        backButton.setOnClickListener {
            onBackClickListener?.invoke()
        }

        fullscreenButton.apply {

            layoutParams.width = 48.dpToPix()
            layoutParams.height = 32.dpToPix()
            scaleType = ImageView.ScaleType.FIT_CENTER
            layoutParams = layoutParams
            val p = 6.dpToPix()
            setPadding(p, p, p, p)

        }
        fullscreenButton.setOnClickListener {
            if (!isIfCurrentIsFullscreen) {
                startWindowFullscreen(context, false, true)
            } else {
                backFromFull(context)
            }
        }
    }

    override fun touchLongPress(e: MotionEvent?) {
        super.touchLongPress(e)
        speed = 3f
        onSpeedChangeHint("3X")
    }

    override fun onTouch(v: View?, event: MotionEvent): Boolean {
        // 将触摸事件传递给手势检测器
        // 判断是否发生了 ACTION_UP 事件
        if (event.action == MotionEvent.ACTION_UP) {
            if (speed > 1f) {
                speed = 1f
                onSpeedChangeHint("1X")
            }
        }
        return super.onTouch(v, event)
    }
}

package free.download.video.downloader.utils

import android.webkit.CookieManager
import com.tinypretty.component.GlobalModule.newLog

object CookieUtil {
    private val mL = newLog("CookieUtil")
    fun createRandomUA() = arrayListOf(
        "Mozilla/5.0 (Linux; Android 8.0.0; SM-G930F Build/R16NW; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 8.0.0; ANE-LX1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 8.0.0; SM-G935F Build/R16NW; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 8.0.0; moto g(6) play Build/OPP27.91-143) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 8.0.0; SAMSUNG SM-G950F Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/7.2 Chrome/59.0.3071.125 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 8.0.0; WAS-LX3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.80 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 8.0.0; FLA-LX3 Build/HUAWEIFLA-LX3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile Safari/537.36"
    ).random()

    private fun encodeHeadInfo(headInfo: String): String {
        val stringBuffer = StringBuffer()
        var i = 0
        val length = headInfo.length
        while (i < length) {
            val c = headInfo[i]
            if (c <= '\u001f' || c >= '\u007f') {
                stringBuffer.append(String.format("\\u%04x", c.toInt()))
            } else {
                stringBuffer.append(c)
            }
            i++
        }
        return stringBuffer.toString()
    }

    // val ua = CachedValue.userAgent

    fun getRequestHeads(ua: String, website: String): HashMap<String, String> {
        val heads = hashMapOf<String, String>()

        fun format(str: String): String {
            return encodeHeadInfo(str)//URLEncoder.encode(str)
        }

        if (ua.isNotBlank()) heads["User-Agent"] = ua
        if (website.isNotBlank()) heads["Referer"] = format(website)

        try {
            val cookie = CookieManager.getInstance().getCookie(website) ?: ""
            if (cookie.isNotBlank()) heads["Cookie"] = format(cookie)
        } catch (e: Exception) {
            mL.e { "getRequestHeads $heads" }
        }

        mL.d { "getRequestHeads $heads" }
        return heads
    }
}

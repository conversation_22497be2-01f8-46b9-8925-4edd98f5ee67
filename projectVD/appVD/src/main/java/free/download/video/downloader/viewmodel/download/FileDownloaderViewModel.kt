package free.download.video.downloader.viewmodel.download

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tiny.domain.ext.fromJson
import com.tiny.domain.util.CoroutineUtil
import com.tiny.ok.downloader.DownloadState
import com.tiny.ok.downloader.OkDownloadManager
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.component.validUrl
import free.download.video.downloader.model.download.FileEntity
import free.download.video.downloader.repository.download.DownloadRepo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @Since 2024/01/21
 */
object FileDownloaderViewModel : ViewModel() {
    private val log = newLog("FileDownloaderViewModel")
    private var allDownloadTaskList: Flow<List<FileEntity>> = DownloadRepo.downloadTaskList.getAll()
    var downloading: MutableStateFlow<MutableList<FileEntity>> = MutableStateFlow(mutableListOf())
    var downloaded: MutableStateFlow<MutableList<FileEntity>> = MutableStateFlow(mutableListOf())
    private fun allDownloadTask() = downloading.value + downloaded.value
    private val resumeAllTask by lazy {
        downloading.value.forEach { fileEntity ->
//            log.d { "resumeAllTask start -> fileEntity=$fileEntity" }
            if (!fileEntity.done && !fileEntity.pause) {
                startDownload(fileEntity)
            }
        }
    }

    private suspend fun forceSync(resumeTask: Boolean = false) {
        allDownloadTaskList.collect { list ->
            list.firstOrNull { !it.url.validUrl() }?.let { delete(it) }
//            log.d { "forceSync -> list=$list" }
            downloading.update { list.filter { !it.done }.reversed().toMutableList() }
            downloaded.update { list.filter { it.done }.reversed().toMutableList() }
            if (resumeTask) {
                resumeAllTask
            }
        }
    }

    init {
        CoroutineUtil.launch("watchDownloading") {
            forceSync(true)
        }

        CoroutineUtil.launch("watchDownloadingState") {
            log.i { "watchDownloadingState start" }
            OkDownloadManager.tasksEvent.collectLatest { task ->
                log.i { "watchDownloadingState -> task=${task.state}" }

                if (task.state != DownloadState.DOWNLOADING) {
                    log.i { "watchDownloadingState -> task=${task.state}" }
                }
                val fileEntity = allDownloadTask().firstOrNull { it.url == task.url }
                if (fileEntity != null) {
                    if (task.state == DownloadState.COMPLETED) {
                        updateTask(fileEntity.copy().apply {
                            uid = fileEntity.uid
                            done = true
                        })
                    }
                }
            }
        }
    }

    fun addTask(fileEntity: FileEntity, threadCount: Int) = viewModelScope.launch(Dispatchers.IO) {
        DownloadRepo.downloadTaskList.insert(fileEntity).also { log.i { "addTask $it -> fileEntity=$fileEntity" } }
        startDownload(fileEntity, threadCount)
    }

    fun existTask(fileEntity: FileEntity) = allDownloadTask().any { it.url == fileEntity.url }.also { log.i { "existTask $it -> fileEntity=$fileEntity" } }

    fun updateTask(fileEntity: FileEntity) = viewModelScope.launch(Dispatchers.IO) {
        DownloadRepo.downloadTaskList.update(fileEntity).also {
            log.i { "updateTask $it -> fileEntity=$fileEntity" }
        }
        if (!fileEntity.done) {
            if (fileEntity.pause) {
                OkDownloadManager.downloaderImpl.pause(fileEntity.url)
            } else {
                startDownload(fileEntity)
            }
        }
        forceSync()
    }

    fun delete(fileEntity: FileEntity) = viewModelScope.launch(Dispatchers.IO) {
        OkDownloadManager.downloaderImpl.delete(fileEntity.url)
        DownloadRepo.downloadTaskList.delete(fileEntity.filename).also { log.i { "delete $it -> fileEntity=$fileEntity" } }
    }

    private fun startDownload(fileEntity: FileEntity, threadCount: Int = -1) {
        try {
            val header = fileEntity.tag.fromJson<HashMap<String, String>>()
            OkDownloadManager.downloaderImpl.downloadStart(fileEntity.url, fileEntity.directory, fileEntity.filename, header, threadCount)
        } catch (e: Exception) {
            log.e { "syncDownloadState exception -> ${e.message},fileEntity=$fileEntity" }
        }
    }
}
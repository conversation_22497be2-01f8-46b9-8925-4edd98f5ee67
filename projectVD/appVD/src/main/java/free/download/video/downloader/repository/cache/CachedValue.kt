package free.download.video.downloader.repository.cache

import com.tiny.domain.ext.getValue
import com.tiny.domain.ext.saveValue
import com.tiny.lib.web.view.sniffer.core.CookieUtil
import free.download.video.downloader.Constants.publicDownloadDir

/**
 * <AUTHOR>
 * @Since 2024/01/07
 */
object CachedValue {
    var gainRewardedTime: Long
        get() = getValue("gainRewardedTime", Long.MAX_VALUE) ?: Long.MAX_VALUE
        set(value) = value.saveValue("gainRewardedTime")

    var bookMarkUpgraded: Boolean
        get() = getValue("bookMarkUpgraded", false) ?: false
        set(value) = value.saveValue("bookMarkUpgraded")

    var showGuide: Boolean
        get() = getValue("showGuide", true) ?: true
        set(value) = value.saveValue("showGuide")

    var downloadDir: String
        get() = getValue("downloadDir", publicDownloadDir) ?: publicDownloadDir
        set(value) = value.saveValue("downloadDir")

    private val randomUA by lazy { CookieUtil.createRandomUA() }
    var userAgent: String
        get() = (getValue("userAgent", randomUA) ?: randomUA).apply {
            saveValue("userAgent")
        }
        set(value) = value.saveValue("userAgent")
}
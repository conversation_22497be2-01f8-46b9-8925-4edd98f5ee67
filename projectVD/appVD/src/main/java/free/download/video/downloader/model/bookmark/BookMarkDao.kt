package free.download.video.downloader.model.bookmark

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow

/**
 * <AUTHOR>
 * @Since 2024/01/06
 */
@Dao
abstract class BookMarkDao {

    @Insert
    abstract fun insert(bookMark: BookMarkEntity): Long

    @Update
    abstract fun update(bookMark: BookMarkEntity): Int

    @Delete
    abstract fun delete(bookMark: BookMarkEntity): Int

    @Query("SELECT * FROM bookmark")
    abstract fun getAll(): Flow<List<BookMarkEntity>>

    @Query("SELECT * FROM bookmark WHERE id = :id")
    abstract fun get(id: Long): Flow<BookMarkEntity?>

    /**
     * 获取数据库中记录的总数（同步版本）
     */
    @Query("SELECT COUNT(*) FROM bookmark")
    abstract fun getCountSync(): Int

    /**
     * 获取数据库中记录的总数（协程版本）
     */
    suspend fun getCount(): Int {
        return getCountSync()
    }

    /**
     * 删除最旧的记录（按ID排序，ID是自增的）（同步版本）
     * @param count 要删除的记录数量
     */
    @Query("DELETE FROM bookmark WHERE id IN (SELECT id FROM bookmark ORDER BY id ASC LIMIT :count)")
    abstract fun deleteOldestSync(count: Int): Int

    /**
     * 删除最旧的记录（协程版本）
     * @param count 要删除的记录数量
     */
    suspend fun deleteOldest(count: Int): Int {
        return deleteOldestSync(count)
    }

    /**
     * 获取最旧的记录（用于日志记录）（同步版本）
     * @param count 要获取的记录数量
     */
    @Query("SELECT * FROM bookmark ORDER BY id ASC LIMIT :count")
    abstract fun getOldestSync(count: Int): List<BookMarkEntity>

    /**
     * 获取最旧的记录（协程版本）
     * @param count 要获取的记录数量
     */
    suspend fun getOldest(count: Int): List<BookMarkEntity> {
        return getOldestSync(count)
    }
}
package free.download.video.downloader.model.bookmark

import android.app.Application
import android.database.Cursor
import android.database.DatabaseUtils
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty

@Singleton
class BookmarkDatabaseOld @Inject constructor(
    application: Application
) : SQLiteOpenHelper(application, DATABASE_NAME, null, DATABASE_VERSION) {
    private val database: SQLiteDatabase by databaseDelegate()
    // Creating Tables
    override fun onCreate(db: SQLiteDatabase) {
        val createBookmarkTable = "CREATE TABLE ${DatabaseUtils.sqlEscapeString(TABLE_BOOKMARK)}(" +
                "${DatabaseUtils.sqlEscapeString(KEY_ID)} INTEGER PRIMARY KEY," +
                "${DatabaseUtils.sqlEscapeString(KEY_URL)} TEXT," +
                "${DatabaseUtils.sqlEscapeString(KEY_TITLE)} TEXT," +
                "${DatabaseUtils.sqlEscapeString(KEY_FOLDER)} TEXT," +
                "${DatabaseUtils.sqlEscapeString(KEY_POSITION)} INTEGER" +
                ')'
        db.execSQL(createBookmarkTable)
    }

    // Upgrading database
    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        // Drop older table if it exists
        db.execSQL("DROP TABLE IF EXISTS ${DatabaseUtils.sqlEscapeString(TABLE_BOOKMARK)}")
        // Create tables again
        onCreate(db)
    }

    fun getAllBookmarksSorted(): List<Pair<String,String>> {
        return database.query(
            TABLE_BOOKMARK,
            null,
            null,
            null,
            null,
            null,
            "$KEY_FOLDER, $KEY_POSITION ASC, $KEY_TITLE COLLATE NOCASE ASC, $KEY_URL ASC"
        ).let {
            toList(it)
        }
    }

    private fun toList(cursor: Cursor): List<Pair<String,String>> {
        fun Cursor.getString(key: String): String {
            val index = getColumnIndex(key).let {
                if (it < 0) 0 else it
            }
            return getString(index)
        }
        val result = mutableListOf<Pair<String,String>>()
        try {
            with(cursor) {
                while (moveToNext()) {
                    result.add(Pair(getString(KEY_TITLE),getString(KEY_URL)))
                }
            }
            cursor.close()
        }catch (_:Exception){}
        return result
    }

    companion object {

        // Database version
        private const val DATABASE_VERSION = 1

        // Database name
        private const val DATABASE_NAME = "bookmarkManager"

        // Bookmark table name
        private const val TABLE_BOOKMARK = "bookmark"

        // Bookmark table columns names
        private const val KEY_ID = "id"
        private const val KEY_URL = "url"
        private const val KEY_TITLE = "title"
        private const val KEY_FOLDER = "folder"
        private const val KEY_POSITION = "position"

    }

}


/**
 * A delegate that caches a [SQLiteDatabase] object for the consumer, reopening it whenever it is
 * provided if it has been closed between the last time it was accessed.
 */
private class DatabaseDelegate : ReadOnlyProperty<SQLiteOpenHelper, SQLiteDatabase> {

    private var sqLiteDatabase: SQLiteDatabase? = null

    override fun getValue(thisRef: SQLiteOpenHelper, property: KProperty<*>): SQLiteDatabase {
        return sqLiteDatabase?.takeIf(SQLiteDatabase::isOpen)
            ?: thisRef.writableDatabase.also { sqLiteDatabase = it }
    }

}

fun databaseDelegate(): ReadOnlyProperty<SQLiteOpenHelper, SQLiteDatabase> = DatabaseDelegate()


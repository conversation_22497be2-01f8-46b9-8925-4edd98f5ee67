package free.download.video.downloader.ext

import android.annotation.SuppressLint
import com.tiny.domain.util.JsonUtil
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.util.EmojiUtils
import free.download.video.downloader.model.download.FileEntity
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date


fun List<FileEntity>.toJson(): String {
    var result = "["
    var gap = ""
    for (item in this) {
        result += gap + item.toString()
        gap = ","
    }
    result += "]"
    return result
}

@SuppressLint("SimpleDateFormat")
fun Date.getNowDateTime(): String {
    val sdf = SimpleDateFormat("MMddHHmmSS")
    return sdf.format(this)
}

fun Date.getNowHumanDateTime(): String {
    val sdf = SimpleDateFormat("HH:mm:ss")
    return sdf.format(this)
}

fun FileEntity.absolutePath(): String {
    return filename.absolutePath(directory)
}

fun FileEntity.exists(): Boolean {
    return File(absolutePath()).exists()
}

fun String.absolutePath(directory: String): String {
    var gap = if (directory.endsWith("/")) "" else "/"
    return "$directory$gap$this"
}

fun enforceFileNameLengthLimit(fileName: String, maxLength: Int = 255): String {
    val byteCount = fileName.toByteArray(Charsets.UTF_8).size
    if (byteCount <= maxLength) {
        return fileName
    } else {
        var cutLength = 0
        var cutByteCount = 0
        while (cutByteCount < byteCount - maxLength && cutLength < fileName.length) {
            cutByteCount += fileName[fileName.length - 1 - cutLength].toString().toByteArray(Charsets.UTF_8).size
            cutLength++
        }
        return fileName.substring(0, fileName.length - cutLength)
    }
}

fun String.validFileName(): String {
    var result = this.replace(Regex("""["*/:<>?\\|]+""")) { "" }
    result = EmojiUtils.replaceEmoji(result)
//    "\\s*|\t|\r|\n"
    result = result.replace(Regex("[\\s*|\t|\r|\n]+"), " ")

    if (result.isNullOrBlank()) {
        result = "no_name"
    }
    return enforceFileNameLengthLimit(result, 200)
}

fun FileEntity.renameTo(newFileName: String): String {
    var msg = ""
    do {
        if (newFileName.isBlank()) {
            msg = "filename is blank"
            break
        }

        var from = File(absolutePath())
        if (!from.exists()) {
            msg = "source file is not exist ${from.absolutePath}"
            break
        }

        var to = File(directory, newFileName)

        if (to.exists()) {
            msg = "file name is exist"
        }

        var succeed = from.renameTo(to)
        if (!succeed) {
            msg = "rename fail"
        } else {
            filename = newFileName
            title = newFileName
        }
    } while (false)
    return msg
}

fun File.toFileEntity(): FileEntity {
    val childFile = this
    return FileEntity(
        childFile.name,
        "",
        "",
        "",
        childFile.parent,
        childFile.name
    ).apply {
        length = childFile.length()
        time = childFile.lastModified()
    }
}

fun FileEntity.moveTo(dir: String): Pair<String, String> {
    val log = newLog("moveTo")
    var error = ""
    var resultAbsolutPath = ""
    try {
        File(dir).mkdirs()

        var to = File(dir, filename)

        if (to.exists()) {
            to = File(directory, "${Date().getNowDateTime()}${filename}")
        }

        val from = File(absolutePath())
        from.renameTo(to)
        resultAbsolutPath = to.absolutePath
    } catch (e: Exception) {
        error = e.message ?: ""
    }
    log.d { "moveTo dir=$dir filename=$filename error=$error" }
    return Pair(error, resultAbsolutPath)
}

fun FileEntity.rename(): String {
    val log = newLog("rename")
    var result = filename
    log.d { "updateWhenCompelete rename start $result $json" }
    try {
        val directory = File(directory)
        val from = File(absolutePath())
        var newTitle = title.validFileName()

        var suffer = "vd.mp4"
        JsonUtil.obj(json).run {
            var correctSuffer =
                JsonUtil.string(this, "res") + "." +
                        JsonUtil.string(this, "ext")

            if (correctSuffer.contains("m3u8")) {
                correctSuffer = "live.mp4"
            }

            if (correctSuffer.contains("vid")) {
                correctSuffer = "vid.mp4"
            }

            if (correctSuffer.length > 3) {
                suffer = "vd.$correctSuffer"
            }
        }

        var to = File(directory, "$newTitle.$suffer")
        if (filename.contains("file_size_")) {
            to = File(directory, "$newTitle")
        }

        if (to.exists()) {
            log.d { "updateWhenCompelete exist ${to.absolutePath}" }
            to = File(directory, newTitle + "${Date().getNowDateTime()}.$suffer")

            if (filename.contains("file_size_")) {
                to = File(directory, "${Date().getNowDateTime()}_$newTitle")
            }
        }

        val succeed = from.renameTo(to)
        if (succeed) {
            result = to.name
        }
        log.d { "updateWhenCompelete rename $succeed end${to.name}" }
    } catch (e: Exception) {
        log.e { "updateWhenCompelete rename fail $e" }
    }
    return result
}

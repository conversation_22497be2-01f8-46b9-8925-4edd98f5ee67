package free.download.video.downloader.ui.screens.player

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.ImageView
import androidx.core.view.isVisible
import com.shuyu.gsyvideoplayer.utils.GSYVideoType
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
import com.tinypretty.component.GlobalModule.newLog
import free.download.video.downloader.R


class VideoPlayer : StandardGSYVideoPlayer {
    constructor(ctx: Context) : super(ctx)
    constructor(context: Context, fullFlag: Boolean) : super(context, fullFlag)
    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) : super(context, attrs)

    val log = newLog("VideoPlayer")
    override fun getLayoutId(): Int {
        return R.layout.video_layout_sp
    }

    override fun onClick(v: View?) {
        v?.run {
            if (mCurrentState != CURRENT_STATE_PAUSE && id == com.shuyu.gsyvideoplayer.R.id.start) {
                L.d { "video pause" }
//                AdHolder.showGapCp(2) // should show native
            }
        }
        super.onClick(v)
    }

    var mVrButton: ImageView? = null
    var mMuteButton: ImageView? = null
    private var mOriginVolume = 0

    val mAnimation by lazy {
        AnimationUtils.loadAnimation(AppPreferences.mAppContext, R.anim.anim_sound_mute)
    }

    fun updateMuteButton(mute:Boolean) {
        if (mute) {
            AppPreferences.mAutoSilenceMgr.mute()

            mMuteButton?.setImageDrawable(Res.drawablePrimary(R.drawable.ic_mute))
            mMuteButton?.startAnimation(mAnimation)
            mMuteButton?.setOnClickListener {
                AppPreferences.mAutoSilenceMgr.restoreVolume()
                updateMuteButton(false)
            }
        } else {
            mMuteButton?.setImageDrawable(Res.drawablePrimary(R.drawable.ic_sound))
            mMuteButton?.setOnClickListener {
                updateMuteButton(true)
                AppPreferences.mAutoSilenceMgr.mute()
            }
        }
        mMuteButton?.isVisible = true
    }

    override fun init(context: Context?) {
        super.init(context)
        mVrButton = findViewById(R.id.btn_vr)
        mMuteButton = findViewById(R.id.btn_mute)

        setVr()
        mVrButton?.setOnClickListener {
            AppPreferences.mCfgApp.mVREnable = !AppPreferences.mCfgApp.mVREnable
            setVr()

            do {
                if (mIfCurrentIsFullscreen) {
                    onVideoPause()
                    addTextureView()
                    onVideoResume()
                    break
                }

                if (AppPreferences.mCfgApp.mVREnable) {
                    fullscreenButton.performClick()
                }

            } while (false)
        }
    }

    override fun onBackFullscreen() {
        super.onBackFullscreen()
        AppPreferences.mCfgApp.mVREnable = false
        setVr()
    }


    fun setVr() {
        setVr(AppPreferences.mCfgApp.mVREnable)
    }

    fun setVr(enable: Boolean) {
        L.d { "setVr $enable" }
        if (enable) {
            GSYVideoType.setRenderType(GSYVideoType.GLSURFACE)
            // for vr
            setCustomGLRenderer(VideoVrRender()?.apply {
                mPlayer = this@VideoPlayer
            })

            mVrButton?.setImageDrawable(Res.drawableAccent(R.drawable.icon_vr))
        } else {
            GSYVideoType.setRenderType(GSYVideoType.SUFRACE)
            // for vr
//            setCustomGLRenderer(null)
            mVrButton?.setImageDrawable(Res.drawablePrimary(R.drawable.icon_vr))
        }
    }
//
//    override fun onVideoPause() {
//        super.onVideoPause()
//        mOnVideoPause.invoke()
//    }
//
//    override fun onPrepared() {
//        super.onPrepared()
//        mOnVideoPrepared.invoke()
//    }
//
//    override fun onError(what: Int, extra: Int) {
//        super.onError(what, extra)
////        TraceUserHelper.videoError("what=$what extra=$extra currentState=$mCurrentState")
//        if (mCurrentState == CURRENT_STATE_ERROR) {
//            mOnVideoError.invoke()
//        }
//    }
//
//    override fun isVerticalVideo(): Boolean {
//        var result = true
//        try {
//            result = super.isVerticalVideo()
//        }catch (e:Exception){
//
//        }
//        return result
//    }
}

package free.download.video.downloader.utils

import CoroutineTask
import android.app.Activity
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.window.Dialog
import com.mandi.common.ad.AdmobFactory
import com.tiny.ad.network.AdCachePool
import com.tiny.ad.network.AdObject
import com.tiny.ad.network.AdRepo
import com.tiny.ad.network.AdUnitInfo
import com.tiny.ad.old.AdShowResult
import com.tiny.domain.ext.logI
import com.tiny.domain.util.AdConfigure
import com.tinypretty.component.GlobalModule
import com.tinypretty.ui.componets.SimpleLoadingHint
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withTimeoutOrNull

/**
 * 广告管理类，用于集中管理广告的加载和显示
 */
object AdManager {
    private const val TAG = "AdManager"
    
    // 应用启动时自动预加载插屏广告
    fun preloadInterstitialAds() {
        CoroutineTask("preloadInterstitialAds").io().launch {
            val activity = GlobalModule.activity() ?: return@launch
            val ads = AdmobFactory.popAdGroup()
            TAG.logI("预加载插屏广告: $ads")
            AdCachePool.loadAd(activity, ads)
        }
    }
}

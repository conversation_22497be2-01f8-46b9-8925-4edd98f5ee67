package free.download.video.downloader.utils

import CoroutineTask
import android.app.Activity
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.window.Dialog
import com.mandi.common.ad.AdmobFactory
import com.tiny.ad.network.AdCachePool
import com.tiny.ad.network.AdObject
import com.tiny.ad.network.AdRepo
import com.tiny.ad.network.AdUnitInfo
import com.tiny.ad.old.AdShowResult
import com.tiny.domain.ext.logI
import com.tiny.domain.util.AdConfigure
import com.tinypretty.component.GlobalModule
import com.tinypretty.ui.componets.SimpleLoadingHint
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withTimeoutOrNull

/**
 * 广告管理类，用于集中管理广告的加载和显示
 */
object AdManager {
    private const val TAG = "AdManager"
    
    // 应用启动时自动预加载插屏广告
    fun preloadInterstitialAds() {
        CoroutineTask("preloadInterstitialAds").io().launch {
            val activity = GlobalModule.activity() ?: return@launch
            val ads = AdmobFactory.popAdGroup()
            TAG.logI("预加载插屏广告: $ads")
            AdCachePool.loadAd(activity, ads)
        }
    }
    
    // 显示插屏广告，带等待时间和加载提示
    suspend fun showInterstitialAd(
        maxWaitTime: Long = 5000, // 最大等待时间，单位毫秒
        showLoadingDialog: Boolean = true // 是否显示加载对话框
    ): AdShowResult {
        val ads = AdmobFactory.popAdGroup()
        return showFullScreenAdWithTimeout(ads, maxWaitTime, showLoadingDialog)
    }
    
    // 显示激励广告，带等待时间和加载提示
    suspend fun showRewardedAd(
        maxWaitTime: Long = 5000, // 最大等待时间，单位毫秒
        showLoadingDialog: Boolean = true // 是否显示加载对话框
    ): AdShowResult {
        val ads = AdmobFactory.rewardAdGroup()
        return showFullScreenAdWithTimeout(ads, maxWaitTime, showLoadingDialog)
    }
    
    // 带超时的全屏广告显示
    private suspend fun showFullScreenAdWithTimeout(
        ads: List<AdUnitInfo>,
        maxWaitTime: Long,
        showLoadingDialog: Boolean
    ): AdShowResult {
        val activity = GlobalModule.activity() ?: return AdShowResult(false, "Activity is null")
        
        // 先检查是否有缓存的广告
        var ad = AdCachePool.getCachedAdThenRemove(ads)
        if (ad != null) {
            TAG.logI("使用缓存广告: ${ad.adUnitInfo}")
            return ad.adUnitInfo.adBehavior.show(activity, ad, null) ?: AdShowResult(false, "Show failed")
        }
        
        // 没有缓存广告，开始加载
        TAG.logI("开始加载广告: $ads")
        AdCachePool.loadAd(activity, ads)
        
        // 等待广告加载完成或超时
        val loadedAd = withTimeoutOrNull(maxWaitTime) {
            while (true) {
                val cachedAds = AdCachePool.cachedAdFlow(ads).first()
                if (cachedAds.isNotEmpty()) {
                    return@withTimeoutOrNull cachedAds.first()
                }
                delay(100)
            }
            null
        }
        
        // 如果加载成功，显示广告
        return if (loadedAd != null) {
            TAG.logI("广告加载成功，开始显示: ${loadedAd.adUnitInfo}")
            loadedAd.adUnitInfo.adBehavior.show(activity, loadedAd, null) ?: AdShowResult(false, "Show failed")
        } else {
            TAG.logI("广告加载超时")
            // 继续尝试加载广告，以便下次使用
            AdCachePool.loadAd(activity, ads)
            AdShowResult(false, "Load timeout")
        }
    }
    
    // 加载对话框组件
    @Composable
    fun AdLoadingDialog(isShowing: MutableState<Boolean>) {
        if (isShowing.value) {
            Dialog(onDismissRequest = { isShowing.value = false }) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.7f)),
                    contentAlignment = Alignment.Center
                ) {
                    SimpleLoadingHint()
                }
            }
        }
    }
    
    // 带加载对话框的广告显示
    @Composable
    fun ShowAdWithLoading(
        maxWaitTime: Long = 5000,
        adType: String = "interstitial", // "interstitial" 或 "rewarded"
        onAdResult: (AdShowResult) -> Unit
    ) {
        val isLoading = remember { mutableStateOf(false) }
        
        // 显示加载对话框
        AdLoadingDialog(isLoading)
        
        LaunchedEffect(Unit) {
            isLoading.value = true
            
            val result = if (adType == "rewarded") {
                showRewardedAd(maxWaitTime, false)
            } else {
                showInterstitialAd(maxWaitTime, false)
            }
            
            // 如果加载时间小于5秒，不显示加载对话框
            if (maxWaitTime <= 5000) {
                isLoading.value = false
            }
            
            onAdResult(result)
        }
    }
}

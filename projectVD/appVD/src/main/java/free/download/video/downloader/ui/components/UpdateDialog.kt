package free.download.video.downloader.ui.components

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import com.tiny.domain.ext.str
import com.tiny.domain.util.DeviceUtil
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.IntentTools
import com.tinypretty.ui.componets.ComposableActivityWrap
import free.download.video.downloader.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @Since 2024/04/28
 */
@Composable
fun UpdateDialog() = ComposableActivityWrap { activity, _ ->
    val showDialog = remember { mutableStateOf(false) }
    val isForceUpgrade = remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        withContext(Dispatchers.IO) {
            val currentVersionCode = DeviceUtil.getVersionCode(activity)
            val serverVersionCode = GlobalModule.remoteConfig.value("upgrade_server_version_code", "0", 5000).toIntOrNull() ?: 0
            isForceUpgrade.value = GlobalModule.remoteConfig.value("upgrade_type", "", 5000) == "force"
            showDialog.value = currentVersionCode < serverVersionCode
        }
    }
    if (showDialog.value) {
        AlertDialog(
            onDismissRequest = {
                if (!isForceUpgrade.value) {
                    showDialog.value = false
                }
            },
            title = {
                Text(text = R.string.upgrade_title.str())
            },
            text = {
                Text(R.string.upgrade_des.str())
            },
            confirmButton = {
                Button(onClick = {
                    IntentTools.openGP(activity)
                    if (!isForceUpgrade.value) {
                        showDialog.value = false
                    }
                }) {
                    Text(R.string.upgrade_go.str())
                }
            },
            dismissButton = {
                if (!isForceUpgrade.value) {
                    Button(onClick = {
                        showDialog.value = false
                    }) {
                        Text(R.string.upgrade_later.str())
                    }
                }
            }
        )
    }
}
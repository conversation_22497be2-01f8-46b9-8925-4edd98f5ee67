package free.download.video.downloader.model.history

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * <AUTHOR>
 * @Since 2024/01/20
 */
@Entity(tableName = "history")
data class HistoryEntity(
    @ColumnInfo(name = "title")
    var title: String = "",

    @ColumnInfo(name = "url")
    var url: String = ""
) {
    @PrimaryKey(autoGenerate = true)
    var id: Long = 0

    // 添加验证方法确保实体有效
    fun isValid(): Boolean = title.isNotBlank() && url.isNotBlank()
}

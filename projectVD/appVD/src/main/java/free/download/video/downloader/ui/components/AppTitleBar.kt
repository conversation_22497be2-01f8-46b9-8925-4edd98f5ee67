package free.download.video.downloader.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.tinypretty.component.IntentTools
import com.tinypretty.component.ResTools
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.activity
import com.tinypretty.ui.dialogs.btnFiveStar
import com.tinypretty.ui.theme.MT
import free.download.video.downloader.Constants
import free.download.video.downloader.R
import free.download.video.downloader.repository.cache.CachedValue

/**
 * <AUTHOR>
 * @Since 2024/03/03
 */
@Composable
fun AppTitleBar(titleId: Int, fiveStar: Boolean = false) {
    val activity = activity() ?: return
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(MT.color.surface)
            .height(Constants.NAVI_BAR_HEIGHT), contentAlignment = Alignment.CenterEnd
    ) {

        Text(text = ResTools.str(titleId), style = MT.typography.titleMedium, color = Color.White, modifier = Modifier.align(Alignment.Center))

        if (fiveStar && !CachedValue.showGuide) {
            Row(Modifier.padding(top = 6.dp, end = 6.dp)) {
                ImageApp(
                    data = "res/ic_share.webp", modifier = Modifier
                        .size(42.dp)
                        .padding(bottom = 6.dp, top = 6.dp, end = 6.dp, start = 6.dp)
                        .clickable {
                            IntentTools.shareApp(activity, ResTools.str(R.string.home_share_app), "https://play.google.com/store/apps/details?id=${activity.packageName}")
                        }, color = MT.color.primary, contentScale = ContentScale.FillWidth
                )

                btnFiveStar(42, true, null, "")
            }
        }
    }
}
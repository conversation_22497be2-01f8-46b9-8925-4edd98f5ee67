package free.download.video.downloader.analysis

/**
 * <AUTHOR>
 * @Since 2024/03/19
 */
object EventName {
    const val FETCH_REMOTE_CONFIG_FAILED = "fetch_remote_config_failed"


    //AD
    const val AD_PLACEMENT_SHOW = "ad_placement_show"
    const val AD_SHOW = "AD_SHOW"
    const val AD_CLICK = "AD_CLICK"
    const val AD_GAIN_REWARD = "AD_GAIN_REWARD"

    //SHARE
    const val SHARE_PLACEMENT_SHOW = "share_placement_show"
    const val SHARE_PLACEMENT_CLICK = "share_placement_click"

    //FIVE STAR
    const val FIVE_STAR_SHOW = "five_star_show"
    const val FIVE_STAR_CLICK = "five_star_click"
}

object EventKey {
    const val TYPE = "type"
    const val FROM = "from"
    const val VALUE = "value"
}

object EventValue {
    const val AD_TYPE_REWARD = "reward"

    const val SUBS_FROM_ONLINE_PLAY = "online_play"
    const val SUBS_FROM_PARSED_RESULT = "parsed_result"
    const val SUBS_FROM_NAVIGATION_BAR = "navigation_bar"
    const val SUBS_FROM_NEW_TASK = "new_task"
}

object ConfigKey {
    const val AD_MODE_KEEP_TIME = "ad_mode_keep_time"

    const val SUBS_SHOW_AT_FIRST = "subs_show_at_first"
}
package free.download.video.downloader.ui.screens.downloader

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tinypretty.ui.componets.Spacer
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.S
import com.tinypretty.ui.theme.clip

@Composable
fun DownloadTitleBox(title: String, content: @Composable () -> Unit) {
    Column(
        Modifier
            .padding(8.dp)
            .clip(MT.S().small)
            .background(MT.color.surface)
            .padding(horizontal = 8.dp, vertical = 8.dp)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Box(
                Modifier
                    .size(8.dp)
                    .background(color = MT.color.background, CircleShape)
            )
            Spacer(dpValue = 6)
            Text(text = title, style = MT.typography.titleSmall, color = MT.color.onSurface)
        }

        content.invoke()
    }
}
package free.download.video.downloader.ui.screens.bill

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.android.billingclient.api.ProductDetails
import com.lib.bill.manager.BillIntent
import com.lib.bill.manager.BillState
import com.lib.bill.manager.BillingRepo
import com.lib.bill.manager.BillingTools
import com.lib.bill.manager.BillingViewModel
import com.lib.bill.manager.bean.AppProductDetails
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.ResTools
import com.tinypretty.ui.componets.ComposableActivityWrap
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.SimpleLoadingHint
import com.tinypretty.ui.componets.Spacer
import com.tinypretty.ui.dialogs.closeableDlg
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clipBorder
import com.tinypretty.ui.theme.clipBorderSmall
import free.download.video.downloader.R
import free.download.video.downloader.ui.components.AppButton

/**
 * <AUTHOR>
 * @Since 2024/02/25
 */

@Composable
fun BillingScreen(from: String, state: MutableState<Boolean>, viewModel: BillingViewModel) = ComposableActivityWrap { activity, _ ->
    val subscribe = BillingRepo.subscribedStateFlow.collectAsState()
    if (subscribe.value) {
        GlobalModule.toast(ResTools.str(R.string.subscribed_successfully))
        state.value = false
        return@ComposableActivityWrap
    }

    val purchaseState = viewModel.state.collectAsState().value

    LaunchedEffect(Unit) {
        viewModel.process(BillIntent.LoadProducts(from))
    }

    closeableDlg(contentState = state) {
        Column(
            Modifier
                .fillMaxWidth()
                .wrapContentSize()
                .clipBorderSmall()
                .background(MT.color.background)
                .padding(20.dp)
        ) {
            when (purchaseState) {
                BillState.Idle -> {}
                BillState.Loading -> {
                    Box(Modifier.fillMaxSize()) {
                        SimpleLoadingHint(modifier = Modifier.align(Alignment.Center))
                    }
                }

                is BillState.ProductsLoadFailed -> {
                    Text(
                        text = "Failed", style = MT.typography.titleMedium, modifier = Modifier
                            .padding(12.dp)
                            .background(MT.color.background)
                            .fillMaxWidth()
                            .padding(12.dp),
                        color = MT.color.onBackground,
                        textAlign = TextAlign.Center
                    )
                }

                is BillState.ProductsLoadSucceed -> {
                    LazyColumn {
                        purchaseState.products.forEach { product ->
                            item {
                                ProductItem(product) { productDetails, offer ->
                                    GlobalModule.toast("subscribe start")
                                    val pd = productDetails.productDetails
                                    val billingPeriod = offer?.pricingPhases?.pricingPhaseList?.first()?.billingPeriod ?: ""
                                    viewModel.billingPeriod = billingPeriod
                                    viewModel.process(BillIntent.PurchaseClick(from, pd, offer))
                                }
                            }
                        }
                        item {
                            Text(text = ResTools.str(R.string.subscribe_des), color = MT.color.onBackground, style = MT.typography.bodySmall, modifier = Modifier.padding(12.dp))
                        }
                        item {
                            Spacer(dpValue = 8)
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ProductItem(product: AppProductDetails, onBuyClick: (AppProductDetails, ProductDetails.SubscriptionOfferDetails?) -> Unit) {
    val allOffers = product.productDetails?.subscriptionOfferDetails
    if (allOffers == null) {
        Column(
            Modifier
                .wrapContentHeight()
                .fillMaxWidth()
                .padding(bottom = 24.dp)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(1f)
                    .aspectRatio(1.586f)
                    .clipBorder(1.dp, MT.color.onSurface, MT.shapes.large)
            ) {
                ImageApp(data = "res/bg_vip_card.jpg", Modifier.fillMaxSize(), contentScale = ContentScale.FillBounds)
//                Text(
//                    text = ResTools.str(R.string.subscribe_simple_des), color = Color.White, modifier = Modifier
//                        .align(Alignment.Center)
//                        .padding(8.dp)
//                        .fillMaxWidth()
//                        .clip(ms.medium)
//                        .background(Color.Black.copy(0.6f))
//                        .padding(mp.small), style = MT.typography.bodySmall
//                )

                Column(Modifier.align(Alignment.BottomCenter), horizontalAlignment = Alignment.CenterHorizontally) {
                    AppButton(text = "debug", onClick = {
                        onBuyClick.invoke(product, null)
                    })
                    Text(text = ResTools.str(R.string.subscribe_period), color = MT.color.primary, modifier = Modifier.padding(bottom = 8.dp), style = MT.typography.bodySmall)
                }
            }
        }
        return
    }

    product.productDetails?.subscriptionOfferDetails?.forEach { offer ->
        val pricingPhase = offer?.pricingPhases?.pricingPhaseList?.first() ?: return
        // 获取价格
        val priceAmountMicros = pricingPhase.priceAmountMicros
        val formattedPrice = pricingPhase.formattedPrice;
        val priceCurrencyCode = pricingPhase.priceCurrencyCode;

        // 获取订阅周期
        val billingPeriod = pricingPhase.billingPeriod;
        val startEndTime = BillingTools.getStartEndTime(billingPeriod)

        Column(
            Modifier
                .wrapContentHeight()
                .fillMaxWidth()
                .padding(bottom = 24.dp)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(1f)
                    .aspectRatio(1.586f)
                    .clipBorder(1.dp, MT.color.onSurface, MT.shapes.large)
            ) {
                ImageApp(data = "res/bg_vip_card.jpg", Modifier.fillMaxSize(), contentScale = ContentScale.FillBounds)
                Column(Modifier.align(Alignment.BottomCenter), horizontalAlignment = Alignment.CenterHorizontally) {
                    AppButton(text = formattedPrice + " " + ResTools.str(R.string.subscribe_start), onClick = {
                        onBuyClick.invoke(product, offer)
                    })
                    Text(text = ResTools.str(R.string.subscribe_period) + " : " + startEndTime.first + " - " + startEndTime.second, color = MT.color.primary, modifier = Modifier.padding(bottom = 8.dp), style = MT.typography.bodySmall)
                }
            }
        }
    }
}
package free.download.video.downloader.utils

import android.content.ContentUris
import android.content.Context
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.provider.MediaStore
import com.tiny.domain.util.MD5_32
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.GlobalModule.newLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

object VideoLoaderUtil {
    val log = newLog("VideoLoaderUtil")
//    suspend fun loadVideoFile(vararg dirs: String) = withContext(Dispatchers.IO){
//        mutableListOf<VideoLoaderCallbacks.VideoDetails>().apply {
//            log.i { "loadVideoFile start" }
//            val time = System.currentTimeMillis()
//            for (dir in dirs) {
//                addAll(getVideoFiles(File(dir)).map { file ->
//                    getVideoDetails(file)
//                })
//            }
//            log.i { "loadVideoFile cost ${System.currentTimeMillis() - time}ms" }
//        }
//    }

    suspend fun loadVideoFile(vararg dirs: String) = withContext(Dispatchers.IO) {
        mutableListOf<File>().apply {
            log.i { "loadVideoFile start" }
            val time = System.currentTimeMillis()
            for (dir in dirs) {
                addAll(getVideoFiles(File(dir)))
            }
            log.i { "loadVideoFile cost ${System.currentTimeMillis() - time}ms" }
        }
    }

    suspend fun loadMusicFile(vararg dirs: String) = withContext(Dispatchers.IO) {
        mutableListOf<File>().apply {
            log.i { "loadMusicFile start" }
            val time = System.currentTimeMillis()
            for (dir in dirs) {
                addAll(getMusicFiles(File(dir)))
            }
            log.i { "loadMusicFile cost ${System.currentTimeMillis() - time}ms" }
        }
    }


    fun getVideoFiles(directory: File): List<File> {
        val videoExtensions = arrayOf("mp4", "avi", "flv", "mkv")

        log.i { "getVideoFiles start ${directory.absolutePath},listFiles.size=${directory.listFiles()?.size}" }
        val result = directory.listFiles { _, name ->
            videoExtensions.any { extension ->
                name.endsWith(".$extension", ignoreCase = true)
            }
        }?.toList() ?: emptyList()
        log.i { "getVideoFiles cost ${result.size}ms" }
        return result
    }

    fun getMusicFiles(directory: File): List<File> {
        val videoExtensions = arrayOf("acc")

        log.i { "getMusicFiles start" }
        val result = directory.listFiles { _, name ->
            videoExtensions.any { extension ->
                name.endsWith(".$extension", ignoreCase = true)
            }
        }?.toList() ?: emptyList()
        log.i { "getMusicFiles cost ${result.size}ms" }
        return result
    }

    fun getVideoDetails(file: File): VideoLoaderCallbacks.VideoDetails {
        val retriever = MediaMetadataRetriever()
        retriever.setDataSource(file.path)
        val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull() ?: 0
        val size = file.length()

        val creationTime = file.lastModified()
        val ext = "jpeg"
        val cacheDir = GlobalModule.mApp.cacheDir
        val thumbnailFile = File(cacheDir, "${file.absolutePath.MD5_32()}.$ext")

        var thumbnailPath: String? = null
        if (thumbnailFile.exists()) {
            thumbnailPath = "file:/" + thumbnailFile.path
            log.i { "getVideoDetails exist $thumbnailPath" }
        } else {
            val originalThumbnail = retriever.frameAtTime
            if (originalThumbnail != null) {
                val width = originalThumbnail.width
                val height = originalThumbnail.height
                val maxSide = 300
                val maxDimension = maxOf(width, height)
                thumbnailPath = if (maxDimension > maxSide) {
                    val scaleFactor = maxSide.toFloat() / maxDimension
                    val newWidth = (width * scaleFactor).toInt()
                    val newHeight = (height * scaleFactor).toInt()
                    val thumbnail = Bitmap.createScaledBitmap(originalThumbnail, newWidth, newHeight, true)
                    thumbnail.compress(Bitmap.CompressFormat.JPEG, 60, FileOutputStream(thumbnailFile))
                    thumbnail.recycle()
                    log.i { "getVideoDetails new scale $thumbnailPath" }
                    "file:/" + thumbnailFile.path
                } else {
                    originalThumbnail.compress(Bitmap.CompressFormat.JPEG, 60, FileOutputStream(thumbnailFile))
                    originalThumbnail.recycle()
                    log.i { "getVideoDetails new $thumbnailPath" }
                    "file:/" + thumbnailFile.path
                }
            }
        }

        retriever.release()
        log.i { "getVideoDetails $thumbnailPath" }
        return VideoLoaderCallbacks.VideoDetails(
            uri = Uri.fromFile(file),
            path = file.path,
            duration = duration,
            creationTime = creationTime,
            cover = thumbnailPath ?: "",
            size = size
        )
    }

//    suspend fun getAllVideoFiles(context: Context): List<Uri> = withContext(Dispatchers.IO){
//        val videoFiles = mutableListOf<Uri>()
//
//        val projection = arrayOf(
//            MediaStore.Video.Media._ID,
//            MediaStore.Video.Media.DISPLAY_NAME,
//            MediaStore.Video.Media.BUCKET_DISPLAY_NAME
//        )
//
//        val selection = "${MediaStore.Video.Media.BUCKET_DISPLAY_NAME} = ?"
//        val selectionArgs = arrayOf(Constants.publicDownloadDir)
//
//        context.contentResolver.query(
//            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
//            projection,
//            selection,
//            selectionArgs,
//            null
//        )?.use { cursor ->
//            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media._ID)
//
//            while (cursor.moveToNext()) {
//                val id = cursor.getLong(idColumn)
//                val contentUri = ContentUris.withAppendedId(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, id)
//
//                videoFiles.add(contentUri)
//            }
//        }
//
//
//        renameDirectory(context.contentResolver, ".vdVideo", "vdVideo")
//        log.i { "getVideoDetailsUri SDK_INT=${Build.VERSION.SDK_INT} ${videoFiles.size} ${File(Constants.privateDownloadDir).exists()} " }
//        videoFiles
//    }

//
//    suspend fun renameDirectory(contentResolver: ContentResolver, oldDirectoryName: String, newDirectoryName: String) = withContext(Dispatchers.IO) {
//        val projection = arrayOf(
//            MediaStore.Files.FileColumns._ID,
//            MediaStore.Files.FileColumns.DISPLAY_NAME,
//            MediaStore.Files.FileColumns.RELATIVE_PATH
//        )
//
//        val selection = "${MediaStore.Files.FileColumns.RELATIVE_PATH} LIKE ?"
//        val selectionArgs = arrayOf("%$oldDirectoryName%")
//
//        contentResolver.query(
//            MediaStore.Files.getContentUri("external"),
//            projection,
//            selection,
//            selectionArgs,
//            null
//        )?.use { cursor ->
//            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID)
//            val displayNameColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DISPLAY_NAME)
//            val relativePathColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.RELATIVE_PATH)
//
//            log.i { "getVideoDetailsUri rename start ${cursor.count}" }
//            while (cursor.moveToNext()) {
//                log.i { "getVideoDetailsUri rename start 1" }
//                val id = cursor.getLong(idColumn)
//                val displayName = cursor.getString(displayNameColumn)
//                val relativePath = cursor.getString(relativePathColumn)
//
//                val newRelativePath = relativePath.replace(oldDirectoryName, newDirectoryName)
//
//                val contentValues = ContentValues().apply {
//                    put(MediaStore.Files.FileColumns.DISPLAY_NAME, displayName)
//                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
//                        put(MediaStore.Files.FileColumns.RELATIVE_PATH, newRelativePath)
//                    }
//                }
//
//                val fileUri = ContentUris.withAppendedId(MediaStore.Files.getContentUri("external"), id)
//                log.i { "getVideoDetailsUri rename $fileUri} " }
//                contentResolver.update(fileUri, contentValues, null, null)
//            }
//        }
//    }

    suspend fun getVideoFiles(context: Context): Map<String, List<File>> = withContext(Dispatchers.IO) {
        val videoList = mutableMapOf<String, MutableList<File>>()

        val projection = arrayOf(
            MediaStore.Video.Media._ID,
            MediaStore.Video.Media.DISPLAY_NAME,
            MediaStore.Video.Media.BUCKET_DISPLAY_NAME
        )

        val sortOrder = "${MediaStore.Video.Media.DISPLAY_NAME} ASC"

        context.contentResolver.query(
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
            projection,
            null,
            null,
            sortOrder
        )?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media._ID)
            val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DISPLAY_NAME)
            val bucketColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.BUCKET_DISPLAY_NAME)

            while (cursor.moveToNext()) {
                val id = cursor.getLong(idColumn)
                val name = cursor.getString(nameColumn)
                val bucket = cursor.getString(bucketColumn)

                val contentUri: Uri = ContentUris.withAppendedId(
                    MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                    id
                )

                val file = File(context.cacheDir, name).apply {
                    outputStream().use { output ->
                        context.contentResolver.openInputStream(contentUri)?.use { input ->
                            input.copyTo(output)
                        }
                    }
                }

                if (videoList.containsKey(bucket)) {
                    videoList[bucket]?.add(file)
                } else {
                    videoList[bucket] = mutableListOf(file)
                }
            }
        }

        videoList
    }
}

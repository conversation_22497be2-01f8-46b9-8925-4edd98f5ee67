package free.download.video.downloader.ui.screens.web

import CoroutineTask
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.tiny.domain.ext.toJson
import com.tiny.domain.util.MD5_16
import com.tiny.lib.web.view.sniffer.SnifferDelegate
import com.tiny.lib.web.view.sniffer.bean.SnifferData
import com.tiny.lib.web.view.sniffer.core.CookieUtil
import com.tiny.lib.web.view.sniffer.core.HttpUtil
import com.tiny.lib.web.view.sniffer.core.SnifferResult
import com.tiny.lib.web.view.sniffer.core.SnifferSingleResult
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.LimitedSizeMap
import com.tinypretty.component.validUrl
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.SpacerFix
import com.tinypretty.ui.container.ScrollableRow
import com.tinypretty.ui.dialogs.alertContent
import com.tinypretty.ui.dialogs.closeableDlg
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clipBorder
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.bean.ParseResultItemInfo
import free.download.video.downloader.ext.validFileName
import free.download.video.downloader.model.download.FileEntity
import free.download.video.downloader.repository.cache.CachedValue
import free.download.video.downloader.ui.components.RemoveAdButton
import free.download.video.downloader.ui.screens.downloader.DownloadTaskCreateScreen
import free.download.video.downloader.ui.screens.web.ParseResultUtil.toParseResultItemInfo
import free.download.video.downloader.ui.screens.web.ParseResultUtil.toParseResultItemInfoList
import free.download.video.downloader.utils.MemoryUtil
import free.download.video.downloader.viewmodel.app.AppPage
import kotlinx.coroutines.flow.MutableStateFlow

/**
 * <AUTHOR>
 * @Since 2024/03/14
 */

@Composable
fun ParseResultScreen(modifier: Modifier, appPageState: State<AppPage?>, scrollUp: MutableState<Boolean>, snifferResultCount: MutableIntState) {
    val appPage = appPageState.value
    if (appPage !is AppPage.Browser)
        return
    AnimatedVisibility(
        modifier = modifier,
        visible = scrollUp.value,
        enter = slideInVertically(initialOffsetY = { it }),
        exit = slideOutVertically(targetOffsetY = { it * 2 })
    ) {
        val pageUrl = appPage.tabInfo.snifferDelegate.url()
        if (pageUrl.contains(".youtube.")) {
            val forbidden = rememberSaveable { mutableStateOf(false) }
            alertContent(state = forbidden) {
                closeableDlg(contentState = forbidden) {
                    ImageApp(data = "res/hint_forbidden.png",
                        contentScale = ContentScale.FillWidth,
                        modifier = Modifier
                            .padding(6.dp)
                            .fillMaxWidth()
                            .wrapContentHeight()
                            .clickable {
                                forbidden.value = false
                            })
                }

            }
            ImageApp(data = "res/hint_forbidden_small.webp",
                contentScale = ContentScale.FillBounds,
                modifier = Modifier
                    .padding(6.dp)
                    .size(46.dp, 46.dp)
                    .clickable {
                        forbidden.value = true
                    })
        } else {
            ParseResult(appPage.tabInfo.snifferDelegate, scrollUp, snifferResultCount)
        }
    }
}

@Composable
private fun ParseResult(snifferVM: SnifferDelegate, scrollUp: MutableState<Boolean>, snifferResultCount: MutableIntState) {
    val snifferResult = snifferVM.jsInterface.snifferResult.collectAsState().value
    val snifferSingleResult = snifferVM.jsInterface.snifferSingleResult.collectAsState()
    val pageUrl = snifferVM.webView()?.url ?: ""

    val byJSList = if (snifferResult is SnifferResult.Succeed) {
        scrollUp.value = true
        try {
            snifferResult.data.toParseResultItemInfoList(pageUrl)
        } catch (e: Exception) {
            emptyList()
        }
    } else {
        emptyList()
    }

    val bySniffer = snifferSingleResult.value.mapNotNull { it.toParseResultItemInfo() }


    ConstraintLayout(Modifier.fillMaxWidth()) {
        val (fullScreen, downloader) = createRefs()
        val total = (byJSList + bySniffer)
        ScrollableRow(
            Modifier
                .constrainAs(downloader) {
                    end.linkTo(parent.end)
                    start.linkTo(parent.start)
                    width = Dimension.fillToConstraints
                }
        ) {
            total.forEach {
                ParseResultItem(it)
            }
            Box(modifier = Modifier.width(60.dp))
        }
        if (total.isNotEmpty()) {
            snifferResultCount.intValue = total.size

            PlayButton(modifier =
            Modifier
                .constrainAs(fullScreen) {
                    end.linkTo(parent.end)
                    top.linkTo(downloader.top)
                    bottom.linkTo(downloader.bottom)
                    height = Dimension.fillToConstraints
                }
                .aspectRatio(1f), list = (byJSList + bySniffer))
        } else {
            // 修复结果为空时，还显示播放按钮的bug
            snifferResultCount.intValue = 0
        }
    }
}

@Composable
fun PlayButton(modifier: Modifier, list: List<ParseResultItemInfo>) {
    val appVM = LocalAppViewModel.current
    Box(
        modifier.clickable { appVM.playVideo(list) },
        contentAlignment = Alignment.TopEnd
    ) {
        ImageApp(
            data = "res/ic_play.webp",
            Modifier
                .fillMaxSize()
                .padding(12.dp)
                .background(MT.color.primary, shape = CircleShape)
                .padding(6.dp),
            color = MT.color.onPrimary
        )
        Box(
            Modifier
                .padding(top = 6.dp, end = 6.dp)
                .size(20.dp)
                .clipBorder(1.dp, MT.color.primary, CircleShape)
                .background(MT.color.onPrimary),
            contentAlignment = Alignment.Center
        ) {
            Text(
                modifier = Modifier.wrapContentSize(),
                text = list.size.toString(),
                color = MT.color.primary,
                style = MT.typography.bodySmall
            )
        }

    }
}

@Composable
fun ParseResultItem(singleResult: ParseResultItemInfo) {
    val fe = singleResult.fileEntity ?: return
    ParseResultItem(fileEntity = fe, fileSizeFlow = singleResult.fileSize, head = singleResult.head, foot = singleResult.foot, fileSuffix = singleResult.fileSuffix, byJs = singleResult.byJs)
}

@Composable
fun ParseResultItem(fileEntity: FileEntity, fileSizeFlow: MutableStateFlow<Long>, head: String, foot: String, fileSuffix: String, byJs: Boolean = true) {
    val showNewTask = rememberSaveable { mutableStateOf(false) }
    val fontColor = if (byJs) MT.color.primary else MT.color.onBackground
    val fileSize = fileSizeFlow.collectAsState()
    alertContent(state = showNewTask) {
        DownloadTaskCreateScreen(showNewTask, fileEntity, fileSuffix)
    }

    Box(Modifier
        .widthIn(30.dp, 80.dp)
        .clickable {
            showNewTask.value = true
        }
        .padding(start = 2.dp, bottom = 2.dp)
        .clipBorder(1.dp, fontColor.copy(0.7f), MT.shapes.small)
        .background(MT.color.surface.copy(0.95f)), contentAlignment = Alignment.TopEnd) {
        Column(
            Modifier
                .padding(start = 12.dp, end = 12.dp, top = 2.dp, bottom = 2.dp), horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = head,
                style = MT.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                maxLines = 1,
                color = fontColor
            )
            Text(
                text = MemoryUtil().formatSize(fileSize.value),
                style = MT.typography.bodySmall,
                maxLines = 1,
                color = fontColor
            )
            Text(
                text = foot,
                maxLines = 1,
                fontWeight = FontWeight.Bold,
                style = MT.typography.labelSmall,
                color = fontColor
            )
        }
        ImageApp(
            data = "res/ic_download_flag.webp",
            Modifier
                .size(11.dp, 12.dp)
                .background(fontColor, shape = RoundedCornerShape(bottomStart = 6.dp))
                .padding(2.dp), color = MT.color.surface
        )
    }
}


fun formatUrl(url: String): String {
    return when {
        url.startsWith("https://") -> url
        url.startsWith("//") -> "https:$url"
        else -> ""
    }
}


private object ParseResultUtil {
    val contentLengthMap = LimitedSizeMap<String, Long>(20)

    fun SnifferData?.toParseResultItemInfoList(pageUrl: String?): MutableList<ParseResultItemInfo> {
        pageUrl ?: return mutableListOf()
        val sd = this ?: return mutableListOf()
        val result = mutableListOf<ParseResultItemInfo>()
        val header = CookieUtil.getRequestHeads(CachedValue.userAgent, pageUrl)
        this.urls.forEach { urlsItem ->
            if (urlsItem.isNotEmpty()) {
                val info = ParseResultItemInfo()
                result.add(info)
                val log = GlobalModule.newLog("ParseResultItem")
                val url = formatUrl(urlsItem[0])
                if (url.validUrl()) {
                    info.head = urlsItem[1]
                    info.foot = "mp4"//urls[2]
                    info.fileEntity = FileEntity(
                        title = sd.title ?: "",
                        url = url,
                        cover = sd.image ?: "",
                        website = pageUrl,
                        directory = CachedValue.downloadDir,
                        filename = (sd.title ?: "${System.currentTimeMillis()}").validFileName(),
                    ).apply {
                        tag = header.toJson() ?: ""
                    }
                    info.fileSize.value = 0L
                    info.byJs = true
                    info.fileSuffix = ".${info.head}.mp4"
                    CoroutineTask("httpContentLength").io().launch {
                        val key = url.MD5_16()
                        val cached = contentLengthMap.getOrDefault(key, 0L)

                        val len = if (cached == 0L) {
                            HttpUtil.httpContentLength(url, header).second
                        } else {
                            cached
                        }
                        contentLengthMap[key] = len
                        info.fileSize.value = len
                        log.i { "ParseResultItem -> cached=$cached,len=$len size = ${contentLengthMap.size} ${contentLengthMap[url]}" }
                    }
                }

            }
        }
        return result
    }

    fun SnifferSingleResult.toParseResultItemInfo(): ParseResultItemInfo? {
        val result = ParseResultItemInfo()
        val header = CookieUtil.getRequestHeads(CachedValue.userAgent, pageUrl)
        result.fileEntity = try {
            FileEntity(
                title = pageTitle,
                url = urlVideo,
                cover = "",
                website = pageUrl,
                directory = CachedValue.downloadDir,
                filename = (pageTitle.ifEmpty { urlVideo.MD5_16() }).validFileName()
            ).apply {
                tag = header.toJson() ?: ""
            }
        } catch (e: Exception) {
            null
        }

        result.fileSize.value = contentLen
        result.byJs = false
        result.head = "Video"
        result.foot = "mp4"
        result.fileSuffix = ".mp4"
        return if (result.fileEntity == null) null else result
    }
}

@Composable
fun ParseResultHint(modifier: Modifier, appPageState: State<AppPage?>, scrollUp: MutableState<Boolean>, snifferResultCount: MutableIntState) {
    if (appPageState.value !is AppPage.Browser) {
        return
    }

    if (snifferResultCount.intValue == 0) {
        return
    }

    AnimatedVisibility(
        modifier = modifier,
        visible = !scrollUp.value,
        enter = slideInVertically(initialOffsetY = { it }),
        exit = slideOutVertically(targetOffsetY = { it * 2 })
    ) {
        Row(
            Modifier
                .fillMaxWidth()
                .padding(6.dp), horizontalArrangement = Arrangement.End, verticalAlignment = Alignment.CenterVertically
        ) {
            RemoveAdButton(
                modifier = Modifier
                    .size(45.dp)
            )
            6.SpacerFix()
            Box(
                Modifier
                    .clickable {
                        scrollUp.value = true
                    }
                    .size(42.dp)
                    .clipBorder(3.dp, Color.White.copy(0.8f), CircleShape)
                    .background(MT.color.primary.copy(0.8f)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    modifier = Modifier.wrapContentSize(),
                    text = snifferResultCount.intValue.toString(),
                    color = Color.White,
                    style = MT.typography.headlineMedium
                )
            }
        }
    }
}

package free.download.video.downloader.ui.screens.web

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.mandi.common.ad.AdmobFactory
import com.tiny.ad.network.AdGroupType
import com.tiny.lib.web.view.sniffer.TabManager
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.component.validUrl
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.activity
import com.tinypretty.ui.componets.ad.AdUIFullScreen
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clipBorder
import com.tinypretty.ui.theme.verticalGradientBackground
import free.download.video.downloader.Constants
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.LocalFileDownloaderViewModel
import free.download.video.downloader.model.download.FileEntity
import free.download.video.downloader.viewmodel.app.AppPage

/**
 * <AUTHOR>
 * @Since 2024/01/19
 */
@Composable
fun AppBottomActionBar(modifier: Modifier, editingUrl: MutableState<TextFieldValue>, showTabSelected: MutableState<Boolean>) {
    val log = newLog("AppBottomActionBar")
    log.i { "AppBottomActionBar redraw" }
    val appVM = LocalAppViewModel.current
    val appPage = appVM.appPage.collectAsState()
    val tabInfo = TabManager.mTabInfo.collectAsState().value
    val snifferVM = tabInfo?.snifferDelegate
    val adShowing = remember { mutableStateOf(false) }

    Column(modifier) {
        Box(
            Modifier
                .fillMaxWidth()
                .height(1.dp)
                .verticalGradientBackground(MT.color.onSurface.copy(alpha = 0.3f), MT.color.background)
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(MT.color.background)
                .wrapContentHeight(), horizontalArrangement = Arrangement.SpaceEvenly, verticalAlignment = Alignment.CenterVertically
        ) {
            val btnEnable = editingUrl.value.text.validUrl()
            log.i { "AppBottomActionBar redraw url=${editingUrl.value},btnEnable=$btnEnable,canBack=${snifferVM?.canBack()}" }
            Row(modifier = Modifier.wrapContentWidth()) {
                WebButton("res/ic_left.webp", enable = btnEnable && snifferVM?.canBack() == true) {
                    log.i { "AppBottomActionBar redraw url=${editingUrl.value},btnEnable=$btnEnable,canBack=${snifferVM?.canBack()}" }
                    appVM.intoBrowser { snifferVM?.goBack() }
                }
                WebButton("res/ic_refresh.webp", enable = snifferVM?.canRefresh() == true) {
                    appVM.intoBrowser { snifferVM?.refresh() }
                }
                WebButton("res/ic_right.webp", enable = btnEnable && snifferVM?.canForward() == true) {
                    appVM.intoBrowser { snifferVM?.goForward() }
                }
                TabButton(showTabSelected)
            }

            NaviButton(Modifier.weight(1f), "res/ic_home.webp", enable = appPage.value !is AppPage.Home) {
                appVM.toHomePage()
            }

            Box(Modifier.weight(1f), contentAlignment = Alignment.Center) {
                val count: State<List<FileEntity>> = LocalFileDownloaderViewModel.current.downloading.collectAsState(listOf())

                NaviButton(Modifier, "res/ic_bottom_download.webp", enable = appPage.value !is AppPage.Download) {
                    // 点击下载按钮时，先显示插屏广告，然后再导航到下载页面
                    log.i { "点击下载按钮，准备显示插屏广告" }
                    adShowing.value = true
                }

                val list = count.value.filter { !it.done }
                if (list.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .scale(0.8f)
                            .alpha(0.8f)
                            .padding(end = 3.dp)
                            .wrapContentSize()
                            .background(MT.color.primary, shape = CircleShape)
                            .border(1.dp, MT.color.onPrimary, CircleShape)
                            .padding(start = 6.dp, end = 6.dp)
                            .align(Alignment.TopEnd)
                            .padding(2.dp), contentAlignment = Alignment.Center
                    ) {
                        Text(text = "${list.size}", style = MT.typography.titleSmall, color = MT.color.onPrimary, fontWeight = FontWeight.W700)
                    }
                }
            }
        }
    }

    // 显示插屏广告
    if (adShowing.value) {
        val activity = activity()
        if (activity != null) {
            AdUIFullScreen(
                ads = AdmobFactory.popAdGroup(),
                max = 0,
                onAdShowStart = {
                    log.i { "插屏广告开始显示" }
                }) {
                log.i { "插屏广告显示完成，导航到下载页面" }
                adShowing.value = false
                appVM.toDownloadedPage()
            }
        } else {
            // 如果获取不到Activity，直接导航到下载页面
            adShowing.value = false
            appVM.toDownloadedPage()
        }
    }
}


@Composable
private fun WebButton(
    icon: String,
    enable: Boolean,
    onClicked: (() -> Unit)? = null
) {
    newLog("WebButton").also { it.i { "redraw" } }
    val size = Constants.BOTTOM_BAR_HEIGHT
    val color = if (enable) MT.color.primary else MT.color.onSurface
    val click: (() -> Unit)? = if (enable) onClicked else null
    ImageApp(
        data = icon,
        modifier = Modifier
            .size(size)
            .background(MT.color.surface)
            .padding(12.dp)
            .clickable(enable) { click?.invoke() },
        color = color.copy(alpha = if (enable) 1f else 0.6f)
    )
}

@Composable
private fun NaviButton(modifier: Modifier, icon: String, enable: Boolean, onClicked: (() -> Unit)) {
    newLog("NaviButton").also { it.i { "redraw" } }
    val size = Constants.BOTTOM_BAR_HEIGHT
    val color = if (enable) MT.color.primary else MT.color.onSurface
    ImageApp(
        data = icon,
        color = color.copy(alpha = if (enable) 1f else 0.6f),
        modifier = modifier
            .size(size)
            .padding(12.dp)
            .clickable(enable) { onClicked() }
    )
}

@Composable
private fun TabButton(showTabSelected: MutableState<Boolean>) {
    val size = TabManager.mTabSize.collectAsState()
    Box(
        Modifier
            .size(Constants.BOTTOM_BAR_HEIGHT)
            .background(MT.color.surface, shape = RoundedCornerShape(topEndPercent = 50, bottomEndPercent = 50))
            .padding(12.dp)
            .clipBorder(width = 3.dp, color = MT.color.primary, shape = CircleShape)
            .clickable {
                showTabSelected.value = !showTabSelected.value
            }, contentAlignment = Alignment.Center
    ) {
        Text(
            text = size.value.toString(),
            textAlign = TextAlign.Center,
            color = MT.color.primary,
            style = MT.typography.titleSmall.copy(fontWeight = FontWeight.Companion.W700),
            modifier = Modifier
        )
    }
}
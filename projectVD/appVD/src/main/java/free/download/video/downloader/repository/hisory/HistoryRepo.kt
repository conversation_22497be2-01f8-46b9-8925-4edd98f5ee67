package free.download.video.downloader.repository.hisory

import androidx.room.Room
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.tinypretty.component.GlobalModule.mApp
import com.tinypretty.component.GlobalModule.newLog
import free.download.video.downloader.model.history.HistoryDao
import free.download.video.downloader.model.history.HistoryDatabase
import free.download.video.downloader.model.history.HistoryEntity
import free.download.video.downloader.utils.DatabaseManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @Since 2024/01/20
 */
object HistoryRepo {
    private val TAG: String = "HistoryRepo"
    private val log = newLog(TAG)

    // 数据库迁移策略（如果将来需要）
    val MIGRATION_1_2: Migration = object : Migration(1, 2) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 未来版本升级时添加迁移代码
        }
    }

    // 懒加载方式初始化数据库
    val historyDao by lazy {
        try {
            Room.databaseBuilder(
                mApp, HistoryDatabase::class.java,
                "database_history"
            )
                .addMigrations(MIGRATION_1_2) // 添加迁移策略
                .fallbackToDestructiveMigration() // 如果迁移失败，允许破坏性迁移
                .build().historyDao()
        } catch (e: Exception) {
            log.e { "$TAG -> Error initializing history database: ${e.message}" }

            // 尝试恢复数据库
            val restored = free.download.video.downloader.utils.DatabaseMigrationHelper.restoreDatabase("database_history")
            if (restored) {
                log.d { "$TAG -> History database restored from backup, trying to open again" }
                try {
                    return@lazy Room.databaseBuilder(
                        mApp, HistoryDatabase::class.java,
                        "database_history"
                    )
                        .fallbackToDestructiveMigration()
                        .build()
                        .historyDao()
                } catch (e2: Exception) {
                    log.e { "$TAG -> Error opening restored history database: ${e2.message}" }
                }
            }

            // 如果恢复失败，尝试重建数据库
            Room.databaseBuilder(
                mApp, HistoryDatabase::class.java,
                "database_history_new"
            )
                .fallbackToDestructiveMigration()
                .build()
                .historyDao()
        }
    }

    /**
     * 安全地插入历史记录，会先检查数据库记录数量
     * @param history 要插入的历史记录实体
     * @return 插入的记录ID
     */
    fun safeInsert(history: HistoryEntity) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 检查数据库记录数量
                DatabaseManager.checkBeforeInsert(historyDao)
                // 插入记录
                val id = historyDao.insert(history)
                log.i { "$TAG -> Inserted history with ID: $id, title: ${history.title}" }
            } catch (e: Exception) {
                log.e { "$TAG -> Error inserting history: ${e.message}" }
                e.printStackTrace()
            }
        }
    }

    /**
     * 获取所有历史记录（带错误处理）
     * @return 历史记录流
     */
    fun getAllHistory(): Flow<List<HistoryEntity>> {
        return historyDao.getAll()
            .catch { e ->
                log.e { "$TAG -> Error getting all history: ${e.message}" }
                emit(emptyList())
            }
            .flowOn(Dispatchers.IO)
    }

    /**
     * 分页获取历史记录，避免一次加载过多数据导致CursorWindow溢出
     * @param pageSize 每页记录数
     * @param page 页码（从0开始）
     * @return 历史记录流
     */
    fun getPagedHistory(pageSize: Int = 100, page: Int = 0): Flow<List<HistoryEntity>> {
        val offset = page * pageSize
        return historyDao.getPaged(pageSize, offset)
            .catch { e ->
                log.e { "$TAG -> Error getting paged history: ${e.message}" }
                emit(emptyList())
            }
            .flowOn(Dispatchers.IO)
    }

    /**
     * 清空所有历史记录
     */
    suspend fun clearAllHistory() {
        try {
            val count = historyDao.clearAll()
            log.i { "$TAG -> Cleared $count history records" }
        } catch (e: Exception) {
            log.e { "$TAG -> Error clearing history: ${e.message}" }
            e.printStackTrace()
        }
    }

    /**
     * 删除指定URL的历史记录
     * @param url 要删除的URL
     */
    suspend fun deleteHistoryByUrl(url: String) {
        try {
            val count = historyDao.deleteByUrl(url)
            log.i { "$TAG -> Deleted $count history records with URL: $url" }
        } catch (e: Exception) {
            log.e { "$TAG -> Error deleting history by URL: ${e.message}" }
            e.printStackTrace()
        }
    }

    /**
     * 获取所有历史记录（同步版本）
     * @return 历史记录列表
     */
    suspend fun getAllHistoryList(): List<HistoryEntity> = withContext(Dispatchers.IO) {
        try {
            val result = historyDao.getAll().first()
            log.i { "$TAG -> Got ${result.size} history records" }
            result
        } catch (e: Exception) {
            log.e { "$TAG -> Error getting history list: ${e.message}" }
            e.printStackTrace()
            emptyList()
        }
    }

    /**
     * 兼容旧版本的API，将被逐步淘汰
     * 获取历史记录列表
     */
    @Deprecated("使用 getAllHistory() 或 getAllHistoryList() 代替", ReplaceWith("getAllHistoryList()"))
    suspend fun getHistory(): MutableList<HistoryEntity> = withContext(Dispatchers.IO) {
        getAllHistoryList().toMutableList()
    }

    /**
     * 兼容旧版本的API，将被逐步淘汰
     * 设置历史记录列表
     */
    @Deprecated("使用 safeInsert() 代替", ReplaceWith("safeInsert(historyEntity)"))
    fun setHistory(historyList: MutableList<HistoryEntity>) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 清空现有记录
                historyDao.clearAll()

                // 过滤掉无效的历史记录
                val validEntries = historyList.filter { it.isValid() }

                // 插入新记录
                validEntries.forEach { history ->
                    safeInsert(history)
                }

                log.i { "$TAG -> Replaced history with ${validEntries.size} records" }
            } catch (e: Exception) {
                log.e { "$TAG -> Error setting history: ${e.message}" }
                e.printStackTrace()
            }
        }
    }
}
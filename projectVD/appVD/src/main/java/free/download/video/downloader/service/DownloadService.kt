package free.download.video.downloader.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.tiny.domain.util.CoroutineUtil
import com.tiny.ok.downloader.OkDownloadManager
import free.download.video.downloader.MainActivity
import free.download.video.downloader.R
import free.download.video.downloader.repository.download.DownloadRepo
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapMerge
import kotlinx.coroutines.flow.map

class DownloadService : Service() {
    companion object {
        private const val CHANNEL_ID = "DOWNLOAD_NOTIFY"
        private const val FOREGROUND_ID = "FOREGROUND_ID"
    }

    private fun createNotification(text: String): Notification {
        val notificationIntent = Intent(this, MainActivity::class.java)

        val pendingIntent = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            PendingIntent.getService(this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE)
        } else {
            PendingIntent.getService(this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE)
        }

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Notification.Builder(this, CHANNEL_ID)
                .setContentTitle("Download Service")
                .setContentText(text)
                .setSmallIcon(R.mipmap.ic_launcher_foreground)
                .setContentIntent(pendingIntent)
                .setPriority(Notification.PRIORITY_LOW) // 设置优先级
                .setSound(null) // 设置声音为null
                .build()
        } else {
            NotificationCompat.Builder(this)
                .setContentTitle("Download Service")
                .setContentText(text)
                .setPriority(Notification.PRIORITY_LOW) // 设置优先级
                .setSound(null) // 设置声音为null
                .setSmallIcon(R.mipmap.ic_launcher_foreground)
                .setContentIntent(pendingIntent)
                .build()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(CHANNEL_ID, "Download Service", NotificationManager.IMPORTANCE_DEFAULT)
            channel.setSound(null, null) //
            val manager = getSystemService(NotificationManager::class.java)
            manager.createNotificationChannel(channel)
        }

        val tasksEventFlow = OkDownloadManager.tasksEvent
        val fileListFlow = DownloadRepo.downloadTaskList.getAll()
        val mergedFlow = fileListFlow.flatMapMerge { fileList ->
            tasksEventFlow.map {
                Pair(fileList, it)
            }
        }

        CoroutineUtil.launch("DownloadService") {
            mergedFlow.collectLatest { pair ->
                val count = pair.first.filter { !it.done && !it.pause }.size
                // todo by shawn no important
                val text = if (count == 0) {
                    "AllTaskDone"
                } else {
                    "Downloading... $count ${pair.second.speed}%"
                }
                val updatedNotification = createNotification(text)
                startForeground(1, updatedNotification)
            }
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent): IBinder? {
        return null
    }
}
package free.download.video.downloader.ui.screens.web

import android.annotation.SuppressLint
import android.widget.TextView
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.lib.bill.manager.BillIntent
import com.tiny.lib.web.view.sniffer.SnifferDelegate
import com.tiny.lib.web.view.sniffer.core.JSConfigure
import com.tinypretty.component.GlobalModule.newLog
import free.download.video.downloader.LocalBillingViewModel
import free.download.video.downloader.repository.cache.CachedValue

/**
 * <AUTHOR>
 * @Since 2024/01/02
 */

@SuppressLint("SetTextI18s")
@Composable
fun SnifferWebView(modifier: Modifier, snifferVM: SnifferDelegate, scrolledUp: MutableState<Boolean>? = null) {
    LocalBillingViewModel.current.process(BillIntent.CheckUserInSubscriptionPeriod("web"))

    JSConfigure.userAgent = CachedValue.userAgent
    val log = newLog("SnifferWebView")

    AndroidView(
        modifier = modifier, factory = { context ->
            log.d { "AndroidView createWebView" }
            snifferVM.removeFromParent()

            // 检查WebView是否可用
            if (!com.tiny.lib.web.view.sniffer.core.WebViewSafeInitializer.isWebViewAvailable()) {
                // WebView不可用，显示友好提示
                return@AndroidView TextView(context).apply {
                    text = "WebView不可用，请检查系统WebView组件是否已更新"
                    textSize = 16f
                    gravity = android.view.Gravity.CENTER
                }
            }

            // 正常获取WebView
            snifferVM.webView() ?: TextView(context).apply {
                text = "WebView初始化失败，请重启应用"
                textSize = 16f
                gravity = android.view.Gravity.CENTER
            }
        }, update = {
        })
}

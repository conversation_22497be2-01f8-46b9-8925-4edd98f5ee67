package free.download.video.downloader.ui.screens.downloader

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.tiny.ok.downloader.DownloadState
import com.tiny.ok.downloader.OkDownloadManager
import com.tinypretty.component.GlobalModule
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.Spacer
import com.tinypretty.ui.componets.SpacerWeight
import com.tinypretty.ui.componets.sizePretty
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clipBorder
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.bean.ParseResultItemInfo
import free.download.video.downloader.model.download.FileEntity
import free.download.video.downloader.viewmodel.download.FileDownloaderViewModel

/**
 * <AUTHOR>
 * @Since 2024/02/07
 */

@Composable
fun DownloadingItem(vm: FileDownloaderViewModel, fileEntity: FileEntity) {
    val appVM = LocalAppViewModel.current
    val log = GlobalModule.newLog("DownloadingItem")
    log.i { "DownloadingItem redraw" }
    Row(Modifier) {
        Box(contentAlignment = Alignment.Center) {
            ImageApp(
                data = fileEntity.cover, contentScale = ContentScale.Crop, modifier = Modifier
                    .clickable {
                        appVM.playVideo(listOf(ParseResultItemInfo().let {
                            it.fileEntity = fileEntity
                            it.fileSize.value = fileEntity.length
                            it.byJs = true
                            it.foot = "MP4"
                            it
                        }))
                    }
                    .width(102.dp)
                    .height(60.dp)
                    .clipBorder(color = MT.color.background, shape = MT.shapes.small)
                    .background(MT.color.background)
            )

            Box(
                Modifier
                    .alpha(0.8f)
                    .background(MT.color.onPrimary, shape = CircleShape)
                    .padding(6.dp)
            ) {
                ImageApp(
                    data = "res/ic_play.webp", contentScale = ContentScale.Fit, modifier = Modifier
                        .width(18.dp)
                        .height(18.dp), color = MT.color.primary
                )
            }
        }

        Spacer(dpValue = 6)
        DownloadingButtons(vm, fileEntity)
    }
}

@Composable
private fun DownloadingButtons(vm: FileDownloaderViewModel, fileEntity: FileEntity) {
    val appVM = LocalAppViewModel.current
    val log = GlobalModule.newLog("DownloadingProgress").also { it.i { "DownloadingProgress redraw" } }

    val progress = remember { mutableStateOf(mapOf<Int, Long>()) }
    val totalSize = remember { mutableLongStateOf(0) }
    val downloadState = remember { mutableStateOf(DownloadState.UNKNOWN) }
    val speed = remember { mutableStateOf("") }
    val pausedIcon = remember { mutableStateOf("") }


    fun updateState(state: DownloadState) {
        log.i { "updateState -> pause=${fileEntity.pause},downloadState=$state" }
        when (state) {
            DownloadState.PAUSE_ING_OR_RESUME_ING -> {
                pausedIcon.value = "ic_downloading_or_resuming"
            }

            DownloadState.IDLE -> {
                if (fileEntity.pause) {
                    // 没有开始任务
                    pausedIcon.value = "ic_download_start"
                } else {
                    pausedIcon.value = "ic_download_unknown"
                }
            }

            DownloadState.PAUSED -> {
                pausedIcon.value = "ic_download_start"
            }

            DownloadState.PENDING -> {
                pausedIcon.value = "ic_download_pending"
            }

            DownloadState.DOWNLOADING -> {
                pausedIcon.value = "ic_download_pause"
            }

            DownloadState.COMPLETED -> {
                vm.updateTask(fileEntity.copy().apply {
                    uid = fileEntity.uid
                    done = true
                })
                pausedIcon.value = "ic_done"
            }

            DownloadState.ERROR -> {
                pausedIcon.value = "ic_download_error"
            }

            DownloadState.UNKNOWN -> {
                pausedIcon.value = "ic_download_unknown"
            }
        }
        downloadState.value = state
    }

    LaunchedEffect(fileEntity.url) {
        updateState(OkDownloadManager.downloaderImpl.taskInfo(fileEntity.url)?.state ?: DownloadState.IDLE)
        OkDownloadManager.tasksEvent.collect { task ->
            if (task.url == fileEntity.url) {
                log.i { "DownloadingProgress change -> state=${task.state} ${task.url}" }
                updateState(task.state)
                totalSize.longValue = task.totalLength
                progress.value = task.blockProgresses
                speed.value = task.speed
            }
        }
    }
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp)
            .clipBorder(color = MT.color.background, shape = MT.shapes.small)
            .background(MT.color.background)
    ) {
        DownloadingProgress(totalSize.longValue, progress.value)
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .padding(start = 6.dp, bottom = 6.dp, end = 3.dp)
        ) {
            Row {
                Text(
                    text = fileEntity.title, maxLines = 1, color = MT.color.onBackground, modifier = Modifier
                        .padding(top = 6.dp)
                        .weight(1f)
                )
                IconButton(src = pausedIcon.value) {
                    when (downloadState.value) {
                        DownloadState.UNKNOWN,
                        DownloadState.IDLE,
                        DownloadState.ERROR,
                        DownloadState.PAUSED -> {
                            vm.updateTask(fileEntity.copy().apply {
                                uid = fileEntity.uid
                                pause = false
                            })
                        }

                        DownloadState.DOWNLOADING -> {
                            vm.updateTask(fileEntity.copy().apply {
                                uid = fileEntity.uid
                                pause = true
                            })
                        }

                        DownloadState.PENDING,
                        DownloadState.PAUSE_ING_OR_RESUME_ING,
                        DownloadState.COMPLETED -> {
                        }
                    }
                }
                IconButton(src = "ic_download_link") {
                    appVM.toBrowserPage(fileEntity.website)

                }
                IconButton(src = "ic_download_delete") {
                    vm.delete(fileEntity)
                }
            }

            Row(Modifier.fillMaxWidth()) {
                Text(text = speed.value, color = MT.color.onBackground.copy(0.6f), modifier = Modifier, style = MT.typography.bodySmall)
                SpacerWeight()
                val downloadedSizeStr = progress.value.values.sum().sizePretty()
                val totalSizeStr = totalSize.longValue.sizePretty()
                Text(text = "$downloadedSizeStr/$totalSizeStr", color = MT.color.onBackground.copy(0.6f), modifier = Modifier, style = MT.typography.bodySmall)
//                Text(text = "${progress.value.keys} ${progress.value.values}", color = MT.color.onBackground.copy(0.6f), modifier = Modifier)
            }
        }
    }
}

@Composable
private fun DownloadingProgress(totalSize: Long, progress: Map<Int, Long>) {
    if (totalSize <= 0L) {
        return
    }

    Row(
        Modifier
            .fillMaxHeight()
            .fillMaxWidth()
    ) {
        progress.forEach { progressItem ->
            Row(
                Modifier
                    .fillMaxHeight()
                    .width(0.dp)
                    .weight(1f)
            ) {
                Box(
                    modifier = Modifier
                        .background(MT.color.primary)
                        .fillMaxHeight()
                        .fillMaxWidth(progressItem.value * progress.size.toFloat() / totalSize)
                )
            }
        }
    }
}

@Composable
private fun IconButton(src: String, enable: Boolean = true, onClick: () -> Unit = {}) {
    ImageApp(data = "res/$src.webp",
        color = MT.color.onBackground,
        modifier = Modifier
            .size(width = 24.dp, height = 30.dp)
            .padding(top = 6.dp, start = 3.dp, end = 3.dp, bottom = 6.dp)
            .clickable(enable) { onClick() })
}
package free.download.video.downloader.ui.screens.player

import android.view.WindowManager
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.tiny.domain.ext.fromJson
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.findActivity
import com.tinypretty.ui.componets.activity
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.bean.ParseResultItemInfo
import free.download.video.downloader.ui.components.AdUtil
import free.download.video.downloader.ui.components.RectScreen
import java.io.File

/**
 * <AUTHOR>
 * @Since 2024/02/13
 */

@Composable
fun PlayerScreen(file: File) {
    PlayerScreen(url = file.absolutePath, title = file.name, header = null, true)
}

@Composable
fun PlayerScreen(parseResultItemInfo: ParseResultItemInfo) {
    val file = parseResultItemInfo.fileEntity ?: return
    PlayerScreen(url = file.url, title = parseResultItemInfo.head + " " + file.title, header = file.tag.fromJson<HashMap<String, String>>())
}

@Composable
fun PlayerScreen(url: String, title: String, header: Map<String, String>?, isLocal: Boolean = false) {
    val log = GlobalModule.newLog("PlayerScreen")
    val app = LocalAppViewModel.current
    val player = remember { mutableStateOf<CustomGSYVideoPlayer?>(null) }
    log.i { "PlayerScreen url=$url $isLocal" }
    val activity = activity() ?: return
    Column {
        AndroidView(factory = { context ->
            log.i { "PlayerScreen AndroidView create" }
            CustomGSYVideoPlayer(context)
        }, modifier = Modifier
            .weight(1f)
            .height(0.dp), update = {
            log.i { "PlayerScreen AndroidView update" }
            it.run {
                onBackClickListener = {
                    app.mNavControl?.popBackStack()
                }

                if (isLocal) {
                    setUp(url, true, title)
                } else {
                    setUp(url, true, context.cacheDir, header, title)
                }
            }
            it.startPlayLogic()
            player.value = it
        })
        RectScreen()
    }


    BackHandler {
        if (!GSYVideoManager.backFromWindowFull(activity)) {
            app.mNavControl?.popBackStack()
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            log.i { "PlayerScreen AndroidView release" }
            player.value?.release()
            AdUtil.showCp()
        }
    }
}

@Composable
fun PlayerScreen(file: List<ParseResultItemInfo>) {
    PlayerOnLineScreen(file)
}

@Composable
fun KeepScreenOnComposable() {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    val observer = remember {
        LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_RESUME -> {
                    context.findActivity()?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                }

                Lifecycle.Event.ON_PAUSE -> {
                    context.findActivity()?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                }

                else -> {}
            }
        }
    }

    lifecycleOwner.lifecycle.addObserver(observer)
}

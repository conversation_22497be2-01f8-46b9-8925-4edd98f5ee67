package free.download.video.downloader.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.lib.bill.manager.BillingRepo
import com.mandi.common.ad.AdmobFactory
import com.tiny.ad.network.AdCachePool
import com.tiny.ad.network.AdRepo
import com.tiny.domain.util.TimeUtil
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.ResTools
import com.tinypretty.ui.componets.HtmlText
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.SpacerFix
import com.tinypretty.ui.componets.ad.AdUI
import com.tinypretty.ui.componets.ad.RewardContent
import com.tinypretty.ui.componets.ad.SelfAdBannerView
import com.tinypretty.ui.componets.ad.SelfAdNativeView
import com.tinypretty.ui.componets.lottie.AnimatedView
import com.tinypretty.ui.dialogs.AlertCloseAbleContent
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clip
import com.tinypretty.ui.theme.clipBorder
import com.tinypretty.ui.theme.small
import free.download.video.downloader.R
import free.download.video.downloader.analysis.ConfigKey
import free.download.video.downloader.analysis.EventKey
import free.download.video.downloader.analysis.EventName
import free.download.video.downloader.analysis.EventValue
import free.download.video.downloader.repository.cache.CachedValue
import free.download.video.downloader.ui.components.AdUtil.getModeKeepMinute
import free.download.video.downloader.viewmodel.app.AppPage
import kotlinx.coroutines.delay

/**
 * <AUTHOR>
 * @Since 2024/03/13
 */

object AdUtil {
    val log = GlobalModule.newLog("AdUtil")
    fun getModeKeepMinute(): Int {
        val result = try {
            GlobalModule.remoteConfig.value(ConfigKey.AD_MODE_KEEP_TIME, "10").toInt()
        } catch (e: Exception) {
            10
        }
        return if (result < 10) 10 else result
    }

    fun getModeKeepTime() = getModeKeepMinute() * 60 * 1000
    fun inNoAdMode(): Boolean {
        val result = rewardTimeUntilNow() < getModeKeepTime()
        log.i { "inNoAdMode $result" }
        return result
    }

    fun rewardTimeUntilNow(): Long {
        if (TimeUtil.elapsedRealtime() < CachedValue.gainRewardedTime) {
            // 重启后, 默认已经过了奖励时间
            CachedValue.gainRewardedTime = 0L - getModeKeepTime()
        }
        return TimeUtil.elapsedRealtime() - CachedValue.gainRewardedTime
    }

    fun onRewarded() {
        log.i { "onRewarded" }
        CachedValue.gainRewardedTime = TimeUtil.elapsedRealtime()
    }

    fun showCp() {
        if (!AdRepo.adEnable()) {
            return
        }

        AdRepo.showCachedFullScreenAd(AdmobFactory.popAdGroup())
    }
}

@Composable
fun RemoveAdAnimate(size: Dp = 48.dp) {
    AnimatedView(resource = R.raw.remove_ad, modifier = Modifier.size(size), padding = 0.dp)
}

@Composable
fun RewardNoADModeScreen(modifier: Modifier) {
    val loading = rememberSaveable { mutableStateOf(false) }
    val rewardTimeUntilNow = rememberSaveable { mutableLongStateOf(AdUtil.rewardTimeUntilNow()) }

    if (rewardTimeUntilNow.longValue < AdUtil.getModeKeepTime()) {
        LaunchedEffect(Unit) {
            while (true) {
                delay(1000)
                rewardTimeUntilNow.longValue = AdUtil.rewardTimeUntilNow()
            }
        }

        Row(
            modifier = modifier
                .wrapContentWidth()
                .padding(horizontal = 6.dp)
                .clipBorder(1.dp, MT.color.primary, shape = RoundedCornerShape(24.dp))
                .padding(6.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            ImageApp(data = "res/ic_remove_ad.png", Modifier.size(32.dp))
            val leftSecond = ((AdUtil.getModeKeepTime() - rewardTimeUntilNow.longValue) / 1000).toInt()
            6.SpacerFix()
            Text(text = TimeUtil.formatSecond(leftSecond.toLong()), color = MT.color.onBackground, textAlign = TextAlign.Center, style = MT.typography.titleSmall)
        }
    } else {
        RewardContent(ads = AdmobFactory.rewardAdGroup(), loading = loading, onShowEnd = { hasRewarded ->
            if (hasRewarded) {
                GlobalModule.eventLogger.logEvent(EventName.AD_GAIN_REWARD, Pair(EventKey.TYPE, EventValue.AD_TYPE_REWARD))
                AdUtil.onRewarded()
                rewardTimeUntilNow.longValue = AdUtil.rewardTimeUntilNow()
            } else {
                GlobalModule.toast(ResTools.str(R.string.ad_remove_ad_failed))
            }
        }) { loaded, onClick ->
            if (loaded) {
                GlobalModule.eventLogger.logEvent(EventName.AD_PLACEMENT_SHOW, Pair(EventKey.TYPE, EventValue.AD_TYPE_REWARD), Pair(EventKey.VALUE, getModeKeepMinute().toString()))
                val showRewardDlg = remember { mutableStateOf(false) }

                AlertCloseAbleContent(state = showRewardDlg) {
                    Column(
                        Modifier
                            .background(MT.color.background)
                            .padding(horizontal = 12.dp), horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Button(onClick = {
                            GlobalModule.eventLogger.logEvent(EventName.AD_CLICK, Pair(EventKey.TYPE, EventValue.AD_TYPE_REWARD))
                            onClick()
                        }, modifier = modifier, shape = MT.shapes.small, contentPadding = ButtonDefaults.ContentPadding) {
                            Box(contentAlignment = Alignment.TopEnd) {
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
                                        .padding(MT.pd.small())
                                        .align(Alignment.Center)
                                        .fillMaxWidth()
                                ) {
                                    val minute = "<font color=\"#fe0017\"><bold>${getModeKeepMinute()}</bold></font>"
                                    Text(
                                        text =
                                        ResTools.str(R.string.ad_remove_ad_for_free),
                                        color = MT.color.onPrimary,
                                        modifier = Modifier.fillMaxWidth(),
                                        style = MT.typography.titleMedium,
                                        textAlign = TextAlign.Center
                                    )
                                    HtmlText(text = ResTools.str(R.string.ad_remove_ad_for_minute, minute), style = MT.typography.bodySmall, color = MT.color.onPrimary.copy(0.5f), textAlign = TextAlign.Center)
                                }
                            }
                        }
                        24.SpacerFix()
                    }
                }
                Row(
                    modifier
                        .wrapContentSize()
                        .clickable {
                            showRewardDlg.value = true
                        }) {
                    RemoveAdAnimate()
                    Text(
                        text = ResTools.str(R.string.ad_remove_ad_for_free), style = MT.typography.bodySmall, color = MT.color.onPrimary, modifier = Modifier
                            .background(
                                MT.color.primary,
                                shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp, bottomEnd = 12.dp, bottomStart = 0.dp)
                            )
                            .padding(horizontal = 6.dp, vertical = 2.dp)
                            .wrapContentSize()
                    )
                }
            } else {
                Box(modifier = modifier.size(48.dp))
            }
        }
    }
}

@Composable
fun BannerScreen(modifier: Modifier, appPage: State<AppPage>) {
    val hasSubScribed = BillingRepo.subscribedStateFlow.collectAsState()
    if (!AdRepo.adEnable() || hasSubScribed.value) {
        Box(modifier = modifier)
        return
    }

    GlobalModule.newLog("BannerScreen").i { "BannerScreen Wrapped" }
    Box(
        modifier
            .fillMaxWidth()
            .height(50.dp)
    ) {
        when (appPage.value) {
            is AppPage.Browser -> BannerScreen()
            is AppPage.Home -> BannerScreen()
            AppPage.Download -> BannerScreen()
            AppPage.MUSIC -> BannerScreen()
        }
    }
}

@Composable
private fun BannerScreen() {
    GlobalModule.newLog("BannerScreen").i { "BannerScreen" }
    AdUI(ads = AdmobFactory.bannerAdGroup(), inViewGroup = true, cacheOnly = false) { SelfAdBannerView() }
}

@Composable
fun RectScreen(corner: Boolean = false) {
    val hasSubScribed = BillingRepo.subscribedStateFlow.collectAsState()
    if (!AdRepo.adEnable() || hasSubScribed.value) {
        return
    }
    if (!AdCachePool.isCached(AdmobFactory.nativeAdGroup())) {
        return BannerScreen()
    }
    Box(
        Modifier
            .height(320.dp)
            .fillMaxWidth(), contentAlignment = Alignment.Center
    ) {
        GlobalModule.newLog("RectScreen").i { "RectScreen" }
        AdUI(ads = AdmobFactory.nativeAdGroup(), inViewGroup = true, cacheOnly = false) { SelfAdNativeView((if (corner) Modifier.clip(MT.shapes.small) else Modifier)) }
    }
}
package free.download.video.downloader.model.download

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow

@Dao
abstract class DownloadDao {

    @Insert
    abstract fun insert(stories: ArrayList<FileEntity>)

    @Insert
    abstract fun insert(file: FileEntity): Long

    @Query("SELECT * FROM download WHERE done = :done")
    abstract fun getAll(done: Boolean): Flow<List<FileEntity>>

    @Query("SELECT * FROM download")
    abstract fun getAll(): Flow<List<FileEntity>>

    @Update
    abstract fun update(entity: FileEntity): Int

    @Query("SELECT * FROM download where filename = :filename ")
    abstract fun get(filename: String): Flow<FileEntity?>

    @Query("DELETE FROM download WHERE filename = :filename ")
    abstract fun delete(filename: String): Int

    /**
     * 获取数据库中记录的总数（同步版本）
     */
    @Query("SELECT COUNT(*) FROM download")
    abstract fun getCountSync(): Int

    /**
     * 获取数据库中记录的总数（协程版本）
     */
    suspend fun getCount(): Int {
        return getCountSync()
    }

    /**
     * 删除最旧的记录（按时间字段排序）（同步版本）
     * @param count 要删除的记录数量
     */
    @Query("DELETE FROM download WHERE uid IN (SELECT uid FROM download ORDER BY time ASC LIMIT :count)")
    abstract fun deleteOldestSync(count: Int): Int

    /**
     * 删除最旧的记录（按时间字段排序）（协程版本）
     * @param count 要删除的记录数量
     */
    suspend fun deleteOldest(count: Int): Int {
        return deleteOldestSync(count)
    }

    /**
     * 获取最旧的记录（用于日志记录）（同步版本）
     * @param count 要获取的记录数量
     */
    @Query("SELECT * FROM download ORDER BY time ASC LIMIT :count")
    abstract fun getOldestSync(count: Int): List<FileEntity>

    /**
     * 获取最旧的记录（用于日志记录）（协程版本）
     * @param count 要获取的记录数量
     */
    suspend fun getOldest(count: Int): List<FileEntity> {
        return getOldestSync(count)
    }
}
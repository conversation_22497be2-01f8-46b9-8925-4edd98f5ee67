package free.download.video.downloader.utils

import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteException
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.tinypretty.component.GlobalModule.mApp
import com.tinypretty.component.GlobalModule.mL
import free.download.video.downloader.model.download.FileEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.nio.channels.FileChannel
import java.util.Date

/**
 * 数据库迁移助手，用于处理数据库版本升级和兼容性问题
 */
object DatabaseMigrationHelper {
    private val TAG = "DatabaseMigrationHelper"
    
    // 数据库文件备份目录
    private val DB_BACKUP_DIR = "${mApp.filesDir.absolutePath}/db_backups"
    
    /**
     * 下载数据库从版本1到版本2的迁移
     */
    val DOWNLOAD_MIGRATION_1_2 = object : Migration(1, 2) {
        override fun migrate(database: SupportSQLiteDatabase) {
            try {
                // 添加新字段，如果已存在则忽略错误
                safeExecuteSQL(database, "ALTER TABLE download ADD COLUMN json TEXT DEFAULT '' NOT NULL")
                safeExecuteSQL(database, "ALTER TABLE download ADD COLUMN var1 TEXT DEFAULT '' NOT NULL")
                safeExecuteSQL(database, "ALTER TABLE download ADD COLUMN var2 TEXT DEFAULT '' NOT NULL")
                
                mL.d { "$TAG -> Successfully migrated download database from version 1 to 2" }
            } catch (e: Exception) {
                mL.e { "$TAG -> Error migrating download database: ${e.message}" }
            }
        }
    }
    
    /**
     * 安全地执行SQL语句，忽略特定错误
     */
    private fun safeExecuteSQL(database: SupportSQLiteDatabase, sql: String) {
        try {
            database.execSQL(sql)
        } catch (e: SQLiteException) {
            // 如果错误是"列已存在"，则忽略
            if (e.message?.contains("duplicate column name", ignoreCase = true) == true) {
                throw e
            }
            mL.d { "$TAG -> Ignored error: ${e.message}" }
        }
    }
    
    /**
     * 在应用启动时检查数据库健康状态
     */
    suspend fun checkDatabaseHealth() = withContext(Dispatchers.IO) {
        try {
            // 创建备份目录
            createBackupDirectory()
            
            // 备份现有数据库
            backupDatabases()
            
            // 验证数据库结构
            validateDatabases()
            
            mL.d { "$TAG -> Database health check completed successfully" }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error during database health check: ${e.message}" }
        }
    }
    
    /**
     * 创建备份目录
     */
    private fun createBackupDirectory() {
        try {
            val backupDir = File(DB_BACKUP_DIR)
            if (!backupDir.exists()) {
                backupDir.mkdirs()
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error creating backup directory: ${e.message}" }
        }
    }
    
    /**
     * 备份所有数据库文件
     */
    private fun backupDatabases() {
        val databasesDir = mApp.getDatabasePath("database_download").parentFile
        if (databasesDir?.exists() == true) {
            databasesDir.listFiles()?.forEach { dbFile ->
                if (dbFile.name.endsWith(".db")) {
                    backupDatabase(dbFile)
                }
            }
        }
    }
    
    /**
     * 备份单个数据库文件
     */
    private fun backupDatabase(dbFile: File) {
        try {
            val timestamp = Date().time
            val backupFile = File("$DB_BACKUP_DIR/${dbFile.name}_$timestamp.bak")
            
            if (dbFile.exists()) {
                val src = FileInputStream(dbFile).channel
                val dst = FileOutputStream(backupFile).channel
                dst.transferFrom(src, 0, src.size())
                src.close()
                dst.close()
                
                mL.d { "$TAG -> Database ${dbFile.name} backed up successfully" }
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error backing up database ${dbFile.name}: ${e.message}" }
        }
    }
    
    /**
     * 验证数据库结构
     */
    private fun validateDatabases() {
        // 这里可以添加验证数据库结构的代码
        // 例如，尝试打开数据库，检查表结构等
    }
    
    /**
     * 恢复数据库（如果需要）
     */
    fun restoreDatabase(dbName: String): Boolean {
        try {
            // 查找最新的备份
            val backupDir = File(DB_BACKUP_DIR)
            val backupFiles = backupDir.listFiles { file -> 
                file.name.startsWith(dbName) && file.name.endsWith(".bak") 
            }
            
            val latestBackup = backupFiles?.maxByOrNull { it.lastModified() }
            
            if (latestBackup != null) {
                val dbFile = mApp.getDatabasePath(dbName)
                
                // 删除损坏的数据库
                if (dbFile.exists()) {
                    dbFile.delete()
                }
                
                // 复制备份文件
                val src = FileInputStream(latestBackup).channel
                val dst = FileOutputStream(dbFile).channel
                dst.transferFrom(src, 0, src.size())
                src.close()
                dst.close()
                
                mL.d { "$TAG -> Database $dbName restored successfully from backup" }
                return true
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error restoring database $dbName: ${e.message}" }
        }
        return false
    }
}

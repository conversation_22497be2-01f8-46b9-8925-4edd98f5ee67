package free.download.video.downloader.utils

import com.tiny.domain.ext.Cache
import com.tiny.domain.util.MD5_16
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.component.validUrl
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.URL

/**
 * <AUTHOR>
 * @Since 2024/01/20
 */
object WebUtil {
    suspend fun getIconFromURL(url: String): String = withContext(Dispatchers.IO) {
        val log = newLog("getIconFromURL")
        val key = url.MD5_16()
//        Cache.getString(key)?.let {
//            log.i { "getIconFromURL by cached $it" }
//            return@withContext it
//        }
        val sourceCode = URL(url).readText()

//        val regex = Regex("<link.*?href=\"(.*?(favicon|ico|png).*?)\".*?/>")
//        val regex = Regex("""<link.*?href="(.*?(favicon|ico|png).*?)".*?/>""")
        val regex = Regex("""<link.*?href="(.*?)".*?/>""")
//        val regex = Regex("""<link(.*?)/>""")
        val matchResultAll = regex.findAll(sourceCode)
        log.i { "getIconFromURL matchResultAll=${matchResultAll.count()}" }
        var result = ""
        matchResultAll.forEach { matcher ->
            val matchGroup = matcher.value
            log.i { "getIconFromURL matchGroup=${matchGroup}" }
            val match = matcher.groupValues[1]
            val finder = if (sourceCode.contains("favicon")) {
                { matchGroup.contains("favicon") }
            } else {
                {
                    val finderResult = (matchGroup.contains("ico") || matchGroup.contains("png")) && matchGroup.contains("icon")
                    log.i { "finderResult = $finderResult,matchGroup=${matchGroup},${matchGroup.contains("icon")},${matchGroup.contains("icon")},${(matchGroup.contains("ico") || matchGroup.contains("png"))}" }
                    finderResult
                }
            }
            if (finder.invoke()) {
                result = if (!match.startsWith("http")) {
                    URL(URL(url), match).toString()  // add domain if missing
                } else {
                    match
                }
                return@forEach
            }
        }
        log.i { "getIconFromURL result = $result" }
        if (result.validUrl()) {
            Cache.saveString(key, result)
        }
        result
    }
}
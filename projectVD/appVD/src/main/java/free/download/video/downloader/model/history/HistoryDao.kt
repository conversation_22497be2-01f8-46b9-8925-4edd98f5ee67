package free.download.video.downloader.model.history

import androidx.room.*
import kotlinx.coroutines.flow.Flow

/**
 * <AUTHOR>
 * @Since 2024/01/20
 */
@Dao
abstract class HistoryDao {

    @Insert
    abstract fun insert(history: HistoryEntity): Long

    @Update
    abstract fun update(history: HistoryEntity): Int

    @Delete
    abstract fun delete(history: HistoryEntity): Int

    /**
     * 获取所有历史记录，按ID降序排序
     * 注意：这可能会返回大量数据，考虑使用分页查询
     */
    @Query("SELECT * FROM history ORDER BY id DESC")
    abstract fun getAll(): Flow<List<HistoryEntity>>

    /**
     * 分页获取历史记录，避免一次加载过多数据导致CursorWindow溢出
     * @param limit 每页记录数
     * @param offset 起始位置
     */
    @Query("SELECT * FROM history ORDER BY id DESC LIMIT :limit OFFSET :offset")
    abstract fun getPaged(limit: Int, offset: Int): Flow<List<HistoryEntity>>

    @Query("SELECT * FROM history WHERE id = :id")
    abstract fun get(id: Long): Flow<HistoryEntity?>

    /**
     * 获取数据库中记录的总数（同步版本）
     */
    @Query("SELECT COUNT(*) FROM history")
    abstract fun getCountSync(): Int

    /**
     * 获取数据库中记录的总数（协程版本）
     */
    suspend fun getCount(): Int {
        return getCountSync()
    }

    /**
     * 删除最旧的记录（按ID排序，ID是自增的）（同步版本）
     * @param count 要删除的记录数量
     */
    @Query("DELETE FROM history WHERE id IN (SELECT id FROM history ORDER BY id ASC LIMIT :count)")
    abstract fun deleteOldestSync(count: Int): Int

    /**
     * 删除最旧的记录（协程版本）
     * @param count 要删除的记录数量
     */
    suspend fun deleteOldest(count: Int): Int {
        return deleteOldestSync(count)
    }

    /**
     * 获取最旧的记录（用于日志记录）（同步版本）
     * @param count 要获取的记录数量
     */
    @Query("SELECT * FROM history ORDER BY id ASC LIMIT :count")
    abstract fun getOldestSync(count: Int): List<HistoryEntity>

    /**
     * 获取最旧的记录（协程版本）
     * @param count 要获取的记录数量
     */
    suspend fun getOldest(count: Int): List<HistoryEntity> {
        return getOldestSync(count)
    }

    /**
     * 清空历史记录
     */
    @Query("DELETE FROM history")
    abstract fun clearAllSync(): Int

    /**
     * 清空历史记录（协程版本）
     */
    suspend fun clearAll(): Int {
        return clearAllSync()
    }

    /**
     * 根据URL查找历史记录
     * @param url 要查找的URL
     */
    @Query("SELECT * FROM history WHERE url = :url LIMIT 1")
    abstract fun findByUrlSync(url: String): HistoryEntity?

    /**
     * 根据URL查找历史记录（协程版本）
     * @param url 要查找的URL
     */
    suspend fun findByUrl(url: String): HistoryEntity? {
        return findByUrlSync(url)
    }

    /**
     * 删除指定URL的历史记录
     * @param url 要删除的URL
     */
    @Query("DELETE FROM history WHERE url = :url")
    abstract fun deleteByUrlSync(url: String): Int

    /**
     * 删除指定URL的历史记录（协程版本）
     * @param url 要删除的URL
     */
    suspend fun deleteByUrl(url: String): Int {
        return deleteByUrlSync(url)
    }
}

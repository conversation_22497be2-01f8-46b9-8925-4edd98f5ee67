package free.download.video.downloader.utils

import android.content.SharedPreferences
import com.tinypretty.component.GlobalModule.mApp
import com.tinypretty.component.GlobalModule.mL
import free.download.video.downloader.model.bookmark.BookMarkDao
import free.download.video.downloader.model.download.DownloadDao
import free.download.video.downloader.model.history.HistoryDao
import free.download.video.downloader.repository.download.DownloadRepo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.tiny.lib.room.json.JsonDatabaseDao
import com.tiny.lib.room.json.JsonDatabase

/**
 * 数据库管理器，用于管理数据库记录数量
 */
object DatabaseManager {
    private val TAG = "DatabaseManager"

    // 数据库最大记录数
    private const val MAX_DOWNLOAD_RECORDS = 5000 // 下载记录最大数量
    private const val MAX_JSON_RECORDS = 1000 // JSON记录最大数量
    private const val MAX_BOOKMARK_RECORDS = 1000 // 书签记录最大数量
    private const val MAX_HISTORY_RECORDS = 1000 // 历史记录最大数量

    // 一次删除的记录数（当超过最大记录数时）
    private const val DELETE_BATCH_SIZE = 50

    // 首次运行标志的SharedPreferences
    private val preferences: SharedPreferences by lazy {
        mApp.getSharedPreferences("database_manager_prefs", 0)
    }

    // 是否是首次运行新版本
    private val isFirstRun: Boolean
        get() {
            val lastVersionCode = preferences.getInt("last_version_code", 0)
            val currentVersionCode = try {
                mApp.packageManager.getPackageInfo(mApp.packageName, 0).versionCode
            } catch (e: Exception) {
                0
            }

            return lastVersionCode < currentVersionCode
        }

    // 更新版本号
    private fun updateVersionCode() {
        try {
            val currentVersionCode = mApp.packageManager.getPackageInfo(mApp.packageName, 0).versionCode
            preferences.edit().putInt("last_version_code", currentVersionCode).apply()
        } catch (e: Exception) {
            mL.e { "$TAG -> Error updating version code: ${e.message}" }
        }
    }

    /**
     * 检查并清理下载数据库
     * @param dao 下载数据库DAO
     */
    suspend fun checkAndCleanDownloadDatabase(dao: DownloadDao) = withContext(Dispatchers.IO) {
        try {
            // 如果是首次运行新版本，采用更保守的清理策略
            if (isFirstRun) {
                mL.d { "$TAG -> First run after upgrade, using conservative cleaning strategy" }
                conservativeCleanDatabase(dao)
            } else {
                normalCleanDatabase(dao)
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error cleaning download database: ${e.message}" }
        }
    }

    /**
     * 正常清理数据库策略
     */
    private suspend fun normalCleanDatabase(dao: DownloadDao) {
        try {
            val count = dao.getCount()
            mL.d { "$TAG -> Download database record count: $count" }

            if (count > MAX_DOWNLOAD_RECORDS) {
                val deleteCount = count - MAX_DOWNLOAD_RECORDS + DELETE_BATCH_SIZE
                mL.d { "$TAG -> Download database exceeds limit, deleting $deleteCount oldest records" }

                // 获取将被删除的记录（用于日志）
                val oldestRecords = dao.getOldest(5)
                oldestRecords.forEach { record ->
                    mL.d { "$TAG -> Will delete old record: $record" }
                }

                // 删除最旧的记录
                val deletedCount = dao.deleteOldest(deleteCount)
                mL.d { "$TAG -> Deleted $deletedCount old records from download database" }
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error in normal database cleaning: ${e.message}" }
        }
    }

    /**
     * 保守清理数据库策略（用于版本升级后的首次运行）
     * 只删除少量记录，确保不会因大量删除操作导致问题
     */
    private suspend fun conservativeCleanDatabase(dao: DownloadDao) {
        try {
            val count = dao.getCount()
            mL.d { "$TAG -> Download database record count (conservative): $count" }

            // 只有当记录数远超限制时才删除
            if (count > MAX_DOWNLOAD_RECORDS * 1.2) { // 超过20%才删除
                // 每次只删除少量记录
                val deleteCount = minOf(50, (count - MAX_DOWNLOAD_RECORDS) / 2)
                mL.d { "$TAG -> Conservative cleaning: deleting $deleteCount oldest records" }

                // 获取将被删除的记录（用于日志）
                val oldestRecords = dao.getOldest(3)
                oldestRecords.forEach { record ->
                    mL.d { "$TAG -> Will delete old record (conservative): $record" }
                }

                // 删除最旧的记录
                val deletedCount = dao.deleteOldest(deleteCount)
                mL.d { "$TAG -> Conservatively deleted $deletedCount old records" }
            } else {
                mL.d { "$TAG -> Record count within acceptable range, no conservative cleaning needed" }
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error in conservative database cleaning: ${e.message}" }
        }
    }

    /**
     * 检查并清理JSON数据库
     * @param dao JSON数据库DAO
     */
    suspend fun checkAndCleanJsonDatabase(dao: JsonDatabaseDao) = withContext(Dispatchers.IO) {
        try {
            // 如果是首次运行新版本，采用更保守的清理策略
            if (isFirstRun) {
                mL.d { "$TAG -> First run after upgrade, using conservative cleaning strategy for JSON database" }
                conservativeCleanJsonDatabase(dao)
            } else {
                normalCleanJsonDatabase(dao)
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error cleaning JSON database: ${e.message}" }
        }
    }

    /**
     * 正常清理JSON数据库策略
     */
    private suspend fun normalCleanJsonDatabase(dao: JsonDatabaseDao) {
        try {
            val count = dao.getCount()
            mL.d { "$TAG -> JSON database record count: $count" }

            if (count > MAX_JSON_RECORDS) {
                val deleteCount = count - MAX_JSON_RECORDS + DELETE_BATCH_SIZE
                mL.d { "$TAG -> JSON database exceeds limit, deleting $deleteCount oldest records" }

                // 获取将被删除的记录（用于日志）
                val oldestRecords = dao.getOldest(5)
                oldestRecords.forEach { record ->
                    mL.d { "$TAG -> Will delete old JSON record: $record" }
                }

                // 删除最旧的记录
                val deletedCount = dao.deleteOldest(deleteCount)
                mL.d { "$TAG -> Deleted $deletedCount old records from JSON database" }
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error in normal JSON database cleaning: ${e.message}" }
        }
    }

    /**
     * 保守清理JSON数据库策略（用于版本升级后的首次运行）
     */
    private suspend fun conservativeCleanJsonDatabase(dao: JsonDatabaseDao) {
        try {
            val count = dao.getCount()
            mL.d { "$TAG -> JSON database record count (conservative): $count" }

            // 只有当记录数远超限制时才删除
            if (count > MAX_JSON_RECORDS * 1.2) { // 超过20%才删除
                // 每次只删除少量记录
                val deleteCount = minOf(50, (count - MAX_JSON_RECORDS) / 2)
                mL.d { "$TAG -> Conservative cleaning: deleting $deleteCount oldest JSON records" }

                // 获取将被删除的记录（用于日志）
                val oldestRecords = dao.getOldest(3)
                oldestRecords.forEach { record ->
                    mL.d { "$TAG -> Will delete old JSON record (conservative): $record" }
                }

                // 删除最旧的记录
                val deletedCount = dao.deleteOldest(deleteCount)
                mL.d { "$TAG -> Conservatively deleted $deletedCount old records from JSON database" }
            } else {
                mL.d { "$TAG -> JSON record count within acceptable range, no conservative cleaning needed" }
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error in conservative JSON database cleaning: ${e.message}" }
        }
    }

    /**
     * 在应用启动时检查所有数据库
     */
    fun checkAllDatabases() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 首先进行数据库健康检查
                DatabaseMigrationHelper.checkDatabaseHealth()

                // 检查下载数据库
                checkAndCleanDownloadDatabase(DownloadRepo.downloadTaskList)
                checkAndCleanDownloadDatabase(DownloadRepo.recent)

                // 检查书签数据库
                try {
                    val bookmarkDao = free.download.video.downloader.repository.browser.BookMarkRepo.bookMark
                    checkAndCleanBookmarkDatabase(bookmarkDao)
                } catch (e: Exception) {
                    mL.e { "$TAG -> Error accessing bookmark database: ${e.message}" }
                }

                // 检查JSON数据库
                try {
                    val jsonDatabase = JsonDatabase.getInstance()?.let {
                        checkAndCleanJsonDatabase(it.jsonDao())
                    }
                } catch (e: Exception) {
                    mL.e { "$TAG -> Error accessing JSON database: ${e.message}" }
                }

                // 检查历史记录数据库
                try {
                    val historyDao = free.download.video.downloader.repository.hisory.HistoryRepo.historyDao
                    checkAndCleanHistoryDatabase(historyDao)
                } catch (e: Exception) {
                    mL.e { "$TAG -> Error accessing history database: ${e.message}" }
                }

                // 如果是首次运行新版本，更新版本号
                if (isFirstRun) {
                    mL.d { "$TAG -> First run after upgrade, updating version code" }
                    updateVersionCode()
                }
            } catch (e: Exception) {
                mL.e { "$TAG -> Error during database check: ${e.message}" }
            }
        }
    }

    /**
     * 在插入新记录前检查数据库记录数
     * @param dao 下载数据库DAO
     */
    suspend fun checkBeforeInsert(dao: DownloadDao) {
        checkAndCleanDownloadDatabase(dao)
    }

    /**
     * 在插入新记录前检查数据库记录数
     * @param dao JSON数据库DAO
     */
    suspend fun checkBeforeInsert(dao: JsonDatabaseDao) {
        checkAndCleanJsonDatabase(dao)
    }

    /**
     * 检查并清理书签数据库
     * @param dao 书签数据库DAO
     */
    suspend fun checkAndCleanBookmarkDatabase(dao: BookMarkDao) = withContext(Dispatchers.IO) {
        try {
            // 如果是首次运行新版本，采用更保守的清理策略
            if (isFirstRun) {
                mL.d { "$TAG -> First run after upgrade, using conservative cleaning strategy for bookmarks" }
                conservativeCleanBookmarkDatabase(dao)
            } else {
                normalCleanBookmarkDatabase(dao)
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error cleaning bookmark database: ${e.message}" }
        }
    }

    /**
     * 正常清理书签数据库策略
     */
    private suspend fun normalCleanBookmarkDatabase(dao: BookMarkDao) {
        try {
            val count = dao.getCount()
            mL.d { "$TAG -> Bookmark database record count: $count" }

            if (count > MAX_BOOKMARK_RECORDS) {
                val deleteCount = count - MAX_BOOKMARK_RECORDS + DELETE_BATCH_SIZE
                mL.d { "$TAG -> Bookmark database exceeds limit, deleting $deleteCount oldest records" }

                // 获取将被删除的记录（用于日志）
                val oldestRecords = dao.getOldest(5)
                oldestRecords.forEach { record ->
                    mL.d { "$TAG -> Will delete old bookmark record: $record" }
                }

                // 删除最旧的记录
                val deletedCount = dao.deleteOldest(deleteCount)
                mL.d { "$TAG -> Deleted $deletedCount old records from bookmark database" }
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error in normal bookmark database cleaning: ${e.message}" }
        }
    }

    /**
     * 保守清理书签数据库策略（用于首次运行新版本）
     */
    private suspend fun conservativeCleanBookmarkDatabase(dao: BookMarkDao) {
        try {
            val count = dao.getCount()
            mL.d { "$TAG -> Bookmark database record count (conservative): $count" }

            // 只有当记录数远超限制时才删除
            if (count > MAX_BOOKMARK_RECORDS * 1.2) { // 超过20%才删除
                // 每次只删除少量记录
                val deleteCount = minOf(50, (count - MAX_BOOKMARK_RECORDS) / 2)
                mL.d { "$TAG -> Conservative cleaning: deleting $deleteCount oldest bookmark records" }

                // 获取将被删除的记录（用于日志）
                val oldestRecords = dao.getOldest(3)
                oldestRecords.forEach { record ->
                    mL.d { "$TAG -> Will delete old bookmark record (conservative): $record" }
                }

                // 删除最旧的记录
                val deletedCount = dao.deleteOldest(deleteCount)
                mL.d { "$TAG -> Conservatively deleted $deletedCount old records from bookmark database" }
            } else {
                mL.d { "$TAG -> Bookmark record count within acceptable range, no conservative cleaning needed" }
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error in conservative bookmark database cleaning: ${e.message}" }
        }
    }

    /**
     * 在插入新记录前检查书签数据库记录数
     * @param dao 书签数据库DAO
     */
    suspend fun checkBeforeInsert(dao: BookMarkDao) {
        checkAndCleanBookmarkDatabase(dao)
    }

    /**
     * 检查并清理历史记录数据库
     * @param dao 历史记录数据库DAO
     */
    suspend fun checkAndCleanHistoryDatabase(dao: HistoryDao) = withContext(Dispatchers.IO) {
        try {
            // 如果是首次运行新版本，采用更保守的清理策略
            if (isFirstRun) {
                mL.d { "$TAG -> First run after upgrade, using conservative cleaning strategy for history" }
                conservativeCleanHistoryDatabase(dao)
            } else {
                normalCleanHistoryDatabase(dao)
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error cleaning history database: ${e.message}" }
        }
    }

    /**
     * 正常清理历史记录数据库策略
     */
    private suspend fun normalCleanHistoryDatabase(dao: HistoryDao) {
        try {
            val count = dao.getCount()
            mL.d { "$TAG -> History database record count: $count" }

            if (count > MAX_HISTORY_RECORDS) {
                val deleteCount = count - MAX_HISTORY_RECORDS + DELETE_BATCH_SIZE
                mL.d { "$TAG -> History database exceeds limit, deleting $deleteCount oldest records" }

                // 获取将被删除的记录（用于日志）
                val oldestRecords = dao.getOldest(5)
                oldestRecords.forEach { record ->
                    mL.d { "$TAG -> Will delete old history record: $record" }
                }

                // 删除最旧的记录
                val deletedCount = dao.deleteOldest(deleteCount)
                mL.d { "$TAG -> Deleted $deletedCount old records from history database" }
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error in normal history database cleaning: ${e.message}" }
        }
    }

    /**
     * 保守清理历史记录数据库策略（用于首次运行新版本）
     */
    private suspend fun conservativeCleanHistoryDatabase(dao: HistoryDao) {
        try {
            val count = dao.getCount()
            mL.d { "$TAG -> History database record count: $count" }

            // 只有当记录数远超限制时才删除
            if (count > MAX_HISTORY_RECORDS * 1.2) { // 超过20%才删除
                // 每次只删除少量记录
                val deleteCount = minOf(50, (count - MAX_HISTORY_RECORDS) / 2)
                mL.d { "$TAG -> Conservative cleaning: deleting $deleteCount oldest history records" }

                // 获取将被删除的记录（用于日志）
                val oldestRecords = dao.getOldest(3)
                oldestRecords.forEach { record ->
                    mL.d { "$TAG -> Will delete old history record (conservative): $record" }
                }

                // 删除最旧的记录
                val deletedCount = dao.deleteOldest(deleteCount)
                mL.d { "$TAG -> Conservatively deleted $deletedCount old records from history database" }
            } else {
                mL.d { "$TAG -> History record count within acceptable range, no conservative cleaning needed" }
            }
        } catch (e: Exception) {
            mL.e { "$TAG -> Error in conservative history database cleaning: ${e.message}" }
        }
    }

    /**
     * 在插入新记录前检查历史记录数据库记录数
     * @param dao 历史记录数据库DAO
     */
    suspend fun checkBeforeInsert(dao: HistoryDao) {
        checkAndCleanHistoryDatabase(dao)
    }
}

package free.download.video.downloader.ui.screens.music

import android.content.Context
import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.FileProvider
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.SpacerFix
import com.tinypretty.ui.componets.SpacerWeight
import com.tinypretty.ui.theme.MT
import free.download.video.downloader.Constants
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.ui.screens.downloader.downloadGuideItem
import free.download.video.downloader.utils.VideoLoaderUtil
import java.io.File

@Composable
fun MusicScreen() {
    val appVM = LocalAppViewModel.current
    val videoDetails = remember { mutableStateListOf<File>() }
    LaunchedEffect(key1 = System.currentTimeMillis()) {
        videoDetails.clear()
        videoDetails.addAll(VideoLoaderUtil.loadMusicFile(Constants.musicDownloadIir).sortedByDescending { it.lastModified() })
    }
    Column(
        Modifier
            .fillMaxSize()
            .background(MT.color.background)
    ) {
//        AppTitleBar(R.string.tab_music, true)
        LazyColumn(
            Modifier
                .weight(1f)
                .padding(6.dp)
        ) {
            videoDetails.forEach {
                item {
                    Button(onClick = {
                        appVM.playVideo(it)
                    }) {
                        Row(Modifier.fillMaxWidth()) {
                            ImageApp(data = "res/ic_clear_all.webp", color = MT.color.onPrimary, modifier = Modifier
                                .size(24.dp)
                                .padding(2.dp)
                                .clickable {
                                    it.delete()
                                    videoDetails.remove(it)
                                })
                            6.SpacerFix()
                            Text(text = it.name)
                            SpacerWeight()
                            ImageApp(data = "res/ic_music.webp", color = MT.color.onPrimary, modifier = Modifier.size(24.dp))
                        }
                    }
                }
                item { Text(text = it.absolutePath, color = MT.color.onBackground, style = MT.typography.bodySmall, textAlign = TextAlign.Center) }
                item {
                    12.SpacerFix()
                }

                downloadGuideItem()
            }
        }
    }
}

fun playMusic(context: Context, musicFilePath: String) {
    val musicFile = File(musicFilePath)
    val musicUri = FileProvider.getUriForFile(context, "${context.packageName}.provider", musicFile)

    val intent = Intent(Intent.ACTION_VIEW).apply {
        data = musicUri
        type = "audio/*"
        flags = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_ACTIVITY_NEW_TASK
    }

    context.startActivity(intent)
}
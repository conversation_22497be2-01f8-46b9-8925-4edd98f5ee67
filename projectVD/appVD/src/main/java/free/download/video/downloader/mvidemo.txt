sealed class MainState {
    object Idle : MainState()
    data class Loading(val message: String) : MainState()
    data class Content(val data: String) : MainState()
    data class Error(val error: Throwable) : MainState()
}

sealed class MainIntent {
    object LoadData : MainIntent()
}

sealed class MainResult {
    object Loading : MainResult()
    data class Success(val data: String) : MainResult()
    data class Failure(val error: Throwable) : MainResult()
}


class MainViewModel : ViewModel() {
    private val _state = MutableStateFlow<MainState>(MainState.Idle)
    val state: StateFlow<MainState> get() = _state

    fun processIntent(intent: MainIntent) {
        when (intent) {
            is MainIntent.LoadData -> loadData()
        }
    }

    private fun loadData() {
        viewModelScope.launch {
            _state.value = MainState.Loading("Loading data...")
            delay(2000) // simulate network delay
            _state.value = try {
                val data = "Hello, <PERSON>!" // replace with actual data loading
                MainState.Content(data)
            } catch (e: Exception) {
                MainState.Error(e)
            }
        }
    }
}
@Composable
fun MainScreen(viewModel: MainViewModel = viewModel()) {
    val state by viewModel.state.collectAsState()

    when (state) {
        is MainState.Idle -> Text("Idle")
        is MainState.Loading -> CircularProgressIndicator()
        is MainState.Content -> Text((state as MainState.Content).data)
        is MainState.Error -> Text((state as MainState.Error).error.message ?: "Unknown error")
    }

    Button(onClick = { viewModel.processIntent(MainIntent.LoadData) }) {
        Text("Load Data")
    }
}
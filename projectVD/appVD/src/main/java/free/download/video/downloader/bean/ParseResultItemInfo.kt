package free.download.video.downloader.bean

import free.download.video.downloader.model.download.FileEntity
import kotlinx.coroutines.flow.MutableStateFlow

/**
 * <AUTHOR>
 * @Since 2024/03/15
 */


class ParseResultItemInfo {
    var fileEntity: FileEntity? = null
    var fileSize: MutableStateFlow<Long> = MutableStateFlow(0L)
    var head: String = ""
    var foot: String = ""
    var fileSuffix: String = ""
    var byJs: Boolean = true
}
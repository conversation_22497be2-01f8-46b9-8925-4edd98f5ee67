package free.download.video.downloader.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.unit.dp
import com.lib.bill.manager.BillingRepo
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.ResTools
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.dialogs.alertContent
import free.download.video.downloader.LocalBillingViewModel
import free.download.video.downloader.R
import free.download.video.downloader.ui.screens.bill.BillingScreen

/**
 * <AUTHOR>
 * @Since 2024/03/16
 */

@Composable
fun VipButton(form: String, modifier: Modifier) {
    val subs = BillingRepo.subscribedStateFlow.collectAsState()
    val goSubs = remember { mutableStateOf(false) }
    alertContent(state = goSubs) {
        BillingScreen(form, goSubs, LocalBillingViewModel.current)
    }

    ImageApp(data = "res/ic_vip.webp", modifier = modifier
        .padding(6.dp)
        .alpha(if (subs.value) 1f else 0.36f)
        .clickable {
            if (subs.value) {
                GlobalModule.toast(ResTools.str(R.string.subscribed_successfully))
            } else {
                goSubs.value = true
            }
        })
}

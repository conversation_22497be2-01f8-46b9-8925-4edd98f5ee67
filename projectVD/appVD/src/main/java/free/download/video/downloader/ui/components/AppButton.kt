package free.download.video.downloader.ui.components

import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.tinypretty.ui.theme.MT

/**
 * <AUTHOR>
 * @Since 2024/01/20
 */

@Composable
fun AppButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    Button(onClick = onClick, modifier = modifier, enabled = enabled, shape = MT.shapes.small, contentPadding = ButtonDefaults.ContentPadding) {
        Text(text = text, color = MT.color.onPrimary)
    }
}

@Preview
@Composable
fun AppButtonPreview() {
    AppButton("test", onClick = {})
}

package free.download.video.downloader.ui.screens.tab

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tiny.domain.ext.str
import com.tiny.domain.util.copyToClipboard
import com.tiny.lib.web.view.sniffer.TabManager
import com.tiny.lib.web.view.sniffer.TabManager.selected
import com.tinypretty.ui.componets.activity
import com.tinypretty.ui.container.CloseableBox
import com.tinypretty.ui.container.TouchToCloseBox
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.R
import free.download.video.downloader.ui.components.AppButton

/**
 * <AUTHOR>
 * @Since 2024/03/08
 */
@Composable
fun TabNewScreen(longPressUrl: MutableState<String>) {
    val activity = activity() ?: return
    val appVM = LocalAppViewModel.current
    val onDismiss = { longPressUrl.value = "" }
    if (longPressUrl.value.isNotEmpty()) {
        TouchToCloseBox(onDismiss = onDismiss) {
            CloseableBox(
                modifier = Modifier
                    .wrapContentHeight()
                    .fillMaxWidth(),
                onDismiss = onDismiss
            ) {
                Column(Modifier.padding(20.dp), verticalArrangement = androidx.compose.foundation.layout.Arrangement.spacedBy(20.dp)) {
                    AppButton(
                        text = R.string.book_mark_open_in_new_tab.str(), onClick = {
                            appVM.toBrowserPage(TabManager.newTab(activity).selected(), longPressUrl.value, changeTab = true)
                            onDismiss.invoke()
                        }, modifier = Modifier
                            .fillMaxWidth()
                    )

                    AppButton(
                        text = R.string.book_mark_copy_link.str(), onClick = {
                            copyToClipboard(activity, longPressUrl.value)
                            onDismiss.invoke()
                        }, modifier = Modifier
                            .fillMaxWidth()
                    )
                }

            }
        }
    }
}
package free.download.video.downloader

import android.os.Environment
import androidx.compose.ui.unit.dp

/**
 * <AUTHOR>
 * @Since 2024/01/09
 */
object Constants {
    /**
     * 保存下载文件的目录
     */
    val publicDownloadDir by lazy {
        Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOWNLOADS
        ).path + "/vdVideo"
    }

    val privateDownloadDir by lazy {
        Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOWNLOADS
        ).path + "/.vdVideo"
    }

    val musicDownloadIir by lazy {
        Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOWNLOADS
        ).path + "/vdMusic"
    }

    val NAVI_BAR_HEIGHT = 46.dp
    val BOTTOM_BAR_HEIGHT = 50.dp
}
package com.tinypretty.common.utils

import com.tinypretty.component.ResTools
import free.download.video.downloader.R

private const val SECOND_MILLIS = 1000L
private const val MINUTE_MILLIS = 60 * SECOND_MILLIS
private const val HOUR_MILLIS = 60 * MINUTE_MILLIS
private const val DAY_MILLIS = 24 * HOUR_MILLIS

object TimeDiff {
    private var MOMENTS_AGO = "Now"
    private var A_MINUTE_AGO = "A minute ago"
    private var MINUTES_AGO = "minutes ago"
    private var AN_HOUR_AGO = "An hour ago"
    private var HOURS_AGO = "hours ago"
    private var YESTERDAY = "Yesterday"
    private var DAYS_AGO = "days ago"

    val timeLanguage by lazy { setTimeLanguage() }
    private fun setTimeLanguage() {
        MOMENTS_AGO = ResTools.str(R.string.time_now)
        A_MINUTE_AGO = ResTools.str(R.string.time_an_minute_ago)
        MINUTES_AGO = ResTools.str(R.string.time_minutes_ago)
        AN_HOUR_AGO = ResTools.str(R.string.time_an_hour_ago)
        HOURS_AGO = ResTools.str(R.string.time_hours_ago)
        YESTERDAY = ResTools.str(R.string.time_yesterday)
        DAYS_AGO = ResTools.str(R.string.time_days_ago)
    }

    fun getTimeAgo(time: Long): String {
        timeLanguage
        try {
//        TODO: un-comment/remove after checking values returned from backend are in seconds or milliseconds
//        if (time < 1000000000000L) {
//            // if timestamp given in seconds, convert to millis
//            time *= 1000
//        }

            val now = System.currentTimeMillis()
            if (time > now || time <= 0) {
                return ""
            }

            val diff = now - time
            return when {
                diff < MINUTE_MILLIS -> MOMENTS_AGO
                diff < 2 * MINUTE_MILLIS -> A_MINUTE_AGO
                diff < 50 * MINUTE_MILLIS -> "${diff / MINUTE_MILLIS} $MINUTES_AGO"
                diff < 90 * MINUTE_MILLIS -> AN_HOUR_AGO
                diff < 24 * HOUR_MILLIS -> "${diff / HOUR_MILLIS} $HOURS_AGO"
                diff < 48 * HOUR_MILLIS -> YESTERDAY
                else -> "${diff / DAY_MILLIS} $DAYS_AGO"
            }
        } catch (e: Exception) {
            return ""
        }
    }

    fun getTimeInHourMinuteFormat(timeStamp: Long): String {
        val floatHourDiff = timeStamp.toFloat() / HOUR_MILLIS
        val intHourDiff = floatHourDiff.toInt()
        val minuteDiff = ((floatHourDiff - intHourDiff) * 60).toInt()
        return "$intHourDiff hrs $minuteDiff mins"
    }

    fun getFootballTimeElapsed(timeStamp: Long): String {
        val floatMinuteDiff = timeStamp.toFloat() / MINUTE_MILLIS
        val intMinuteDiff = floatMinuteDiff.toInt()
        val secondDiff = ((floatMinuteDiff - intMinuteDiff) * 60).toInt()
        return "$intMinuteDiff : $secondDiff"
    }

    fun getDurationString(duration: Long): String {
//        long days = duration / (1000 * 60 * 60 * 24);
        val hours = duration % (1000 * 60 * 60 * 24) / (1000 * 60 * 60)
        val minutes = duration % (1000 * 60 * 60) / (1000 * 60)
        val seconds = duration % (1000 * 60) / 1000
        val hourStr = if (hours < 10) "0$hours" else hours.toString() + ""
        val minuteStr = if (minutes < 10) "0$minutes" else minutes.toString() + ""
        val secondStr = if (seconds < 10) "0$seconds" else seconds.toString() + ""
        return if (hours != 0L) {
            "$hourStr:$minuteStr:$secondStr"
        } else {
            "$minuteStr:$secondStr"
        }
    }

}

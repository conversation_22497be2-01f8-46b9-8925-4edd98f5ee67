package free.download.video.downloader.viewmodel.history

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tinypretty.component.GlobalModule.newLog
import free.download.video.downloader.model.history.HistoryEntity
import free.download.video.downloader.repository.hisory.HistoryRepo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @Since 2024/01/20
 */
class HistoryViewModel : ViewModel() {
    private val TAG = "HistoryViewModel"
    val history: MutableStateFlow<MutableList<HistoryEntity>> = MutableStateFlow(mutableListOf())
    private val log = newLog(TAG)

    init {
        viewModelScope.launch {
            try {
                // 从数据库获取历史记录并监听变化
                HistoryRepo.getAllHistory().collectLatest { historyList ->
                    history.value = historyList.toMutableList()
                    log.i { "$TAG -> history updated, size: ${history.value.size}" }
                }
            } catch (e: Exception) {
                log.e { "$TAG -> init error: ${e.message}" }
                e.printStackTrace()
            }
        }
    }

    fun addHistory(title: String, url: String) {
        if (title.isBlank() || url.isBlank()) {
            log.e { "$TAG -> addHistory with blank title or url" }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // 先删除相同URL的记录
                HistoryRepo.deleteHistoryByUrl(url)

                // 插入新记录
                val historyEntity = HistoryEntity(title, url)
                HistoryRepo.safeInsert(historyEntity)

                log.i { "$TAG -> addHistory: $title, $url" }
            } catch (e: Exception) {
                log.e { "$TAG -> addHistory error: ${e.message}" }
                e.printStackTrace()
            }
        }
    }

    fun clearHistory() {
        viewModelScope.launch {
            try {
                HistoryRepo.clearAllHistory()
                log.i { "$TAG -> clearHistory" }
            } catch (e: Exception) {
                log.e { "$TAG -> clearHistory error: ${e.message}" }
                e.printStackTrace()
            }
        }
    }
}
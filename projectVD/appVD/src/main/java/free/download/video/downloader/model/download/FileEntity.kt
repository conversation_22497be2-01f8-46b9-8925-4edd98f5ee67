package free.download.video.downloader.model.download

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "download")
data class FileEntity(
    @ColumnInfo(name = "title") var title: String,
    @ColumnInfo(name = "url") var url: String,
    @ColumnInfo(name = "cover") var cover: String,
    @ColumnInfo(name = "website") var website: String,
    @ColumnInfo(name = "directory") var directory: String,
    @ColumnInfo(name = "filename") var filename: String
) {

    @PrimaryKey(autoGenerate = true)
    var uid: Long = 0

    @ColumnInfo(name = "done")
    var done: Boolean = false

    @ColumnInfo(name = "pause")
    var pause: Boolean = false

    @ColumnInfo(name = "length")
    var length: Long = -1

    @ColumnInfo(name = "time")
    var time: Long = -1

    @ColumnInfo(name = "duration")
    var duration: Long = 0

    @ColumnInfo(name = "flag")
    var flag: String = ""

    @ColumnInfo(name = "tag")
    var tag: String = ""

    @ColumnInfo(name = "json")
    var json: String = ""

    // download Protocols
    @ColumnInfo(name = "var1")
    var var1: String = ""

    @ColumnInfo(name = "var2")
    var var2: String = ""

    override fun toString(): String {
        return "uid=$uid,title=$title,cover=${cover},pause=$pause,done=$done,json=$json,tag=$tag"
    }
}
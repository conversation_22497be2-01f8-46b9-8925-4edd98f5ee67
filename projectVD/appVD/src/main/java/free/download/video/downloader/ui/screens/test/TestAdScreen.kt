package free.download.video.downloader.ui.screens.test

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.mandi.common.ad.AdmobFactory
import com.tiny.ad.network.AdCachePool
import com.tinypretty.component.GlobalModule.mDebugUtil
import com.tinypretty.ui.componets.activity
import com.tinypretty.ui.componets.ad.AdUI

/**
 * <AUTHOR>
 * @Since 2023/12/30
 */
@Composable
fun TestAdScreen() {
    val activity = activity() ?: return

    val time = remember {
        mutableStateOf(false)
    }
    val showCp = remember {
        mutableStateOf(false)
    }

    Column {
        Text(text = "isDebug:${mDebugUtil}")
        Button(onClick = {
            time.value = !time.value
        }) {
            Text(text = "click")
        }
        Button(onClick = {
            AdCachePool.loadAdAsync(activity, AdmobFactory.nativeAdGroup())
            AdCachePool.loadAdAsync(activity, AdmobFactory.popAdGroup())
        }) {
            Text(text = "load")
        }

        Button(onClick = {
            showCp.value = !showCp.value
        }) {
            Text(text = "show cp")
        }

        Box(Modifier.height(120.dp)) {
            if (time.value) {
                AdUI(ads = AdmobFactory.nativeAdGroup(), true)
            }
            if (showCp.value) {
                AdUI(ads = AdmobFactory.popAdGroup(), false)
            }
        }
//                AdCachePool.loadAd(this@MainActivity,AdFactory.bannerAdGroup())
    }
}
package free.download.video.downloader.ui.screens.downloader

import androidx.activity.ComponentActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tiny.domain.util.forEachGroup
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.component.ResTools
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.RowSplit
import com.tinypretty.ui.componets.Spacer
import com.tinypretty.ui.componets.activity
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.MT.typography
import com.tinypretty.ui.utils.ContentRequiredMultiplePermissions
import com.tinypretty.ui.utils.PermissionUtil
import free.download.video.downloader.Constants
import free.download.video.downloader.R
import free.download.video.downloader.model.download.FileEntity
import free.download.video.downloader.ui.components.AppTitleBar
import free.download.video.downloader.ui.screens.music.MusicScreen
import free.download.video.downloader.utils.VideoLoaderUtil
import free.download.video.downloader.viewmodel.download.FileDownloaderViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * <AUTHOR>
 * @Since 2023/12/30
 */
@Composable
fun DownloadedScreen() {
    ContentRequiredMultiplePermissions(
        requestPermissionGap = 2,
        ignoreAble = false,
        permissions = PermissionUtil.permissionReadWrite.toTypedArray()
    ) {
        DownloadedScreenContent()
    }
}

@Composable
fun DownloadedScreenContent() {
    val activity = activity() ?: return
    val log = newLog("DownloadedScreen")

    val vm = free.download.video.downloader.LocalFileDownloaderViewModel.current
    val tasksDownloading = vm.downloading.collectAsState().value
    val tasksDownloaded = vm.downloaded.collectAsState().value
    val videoDetails = remember { mutableStateListOf<File>() }
    log.i { "DownloadingScreen redraw -> tasksDownloaded.size=${tasksDownloaded.size}" }

    LaunchedEffect(tasksDownloading.size, tasksDownloaded.size) {
        withContext(Dispatchers.IO) {
            log.i { "DownloadingScreen loadVideoFile" }
            if (activity is ComponentActivity) {
                videoDetails.clear()
                videoDetails.addAll(VideoLoaderUtil.loadVideoFile(Constants.privateDownloadDir, Constants.publicDownloadDir)
                    .filter {
                        tasksDownloading.find { downloading -> downloading.filename == it.name } == null
                    }.sortedByDescending { it.lastModified() })
            }
            val size = videoDetails.size
            log.i { "DownloadingScreen loadVideoFile end $size" }
        }
    }

    val selectedTabIndex = remember { mutableStateOf(0) }
    val tabList = listOf("res/ic_play.webp", "res/ic_music.webp")

    Column(
        Modifier
            .background(MT.color.background)
            .fillMaxSize()
    ) {
        AppTitleBar(R.string.download_task_management, true)

        TabRow(selectedTabIndex = selectedTabIndex.value) {
            // 根据标签列表创建Tab
            tabList.forEachIndexed { index, title ->
                val selected = selectedTabIndex.value == index
                Tab(
                    text = {
                        ImageApp(
                            data = title,
                            color = MT.color.primary.copy(alpha = if (selected) 1f else 0.3f),
                            modifier = Modifier
                                .size(32.dp)
                                .padding(6.dp)
                        )
                    },
                    modifier = Modifier.height(32.dp),
                    selected = selected,
                    onClick = { selectedTabIndex.value = index }
                )
            }
        }

        when (selectedTabIndex.value) {
            0 -> DownloadingList(vm, tasksDownloading, videoDetails.toMutableList(), tasksDownloaded)
            1 -> MusicScreen()
        }
    }
}

@Composable
private fun ColumnScope.DownloadingList(vm: FileDownloaderViewModel, tasksDownloading: List<FileEntity>, videoDetails: MutableList<File>, tasksDownloaded: List<FileEntity>) {
    val lazyList = rememberLazyListState()
    LazyColumn(Modifier.weight(1f), state = lazyList) {
        downloadingList(vm, tasksDownloading)
        downloadedList(videoDetails = videoDetails, tasksDownloaded = tasksDownloaded)
        downloadGuideItem()
    }
}

fun LazyListScope.downloadingList(vm: FileDownloaderViewModel, tasksDownloading: List<FileEntity>) {
    if (tasksDownloading.isNotEmpty()) {
        item {
            DownloadTitleBox(title = ResTools.str(R.string.download_task_downloading)) {
                tasksDownloading.forEach {
                    Spacer(dpValue = 6)
                    DownloadingItem(vm, it)
                }
            }
        }
    }
}

fun LazyListScope.downloadedList(videoDetails: MutableList<File>, tasksDownloaded: List<FileEntity>) {
    if (videoDetails.isNotEmpty()) {
        item {
            Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.padding(start = 6.dp, end = 6.dp, top = 6.dp)) {
                Box(
                    Modifier
                        .size(8.dp)
                        .background(color = MT.color.onBackground.copy(0.6f), CircleShape)
                )
                Spacer(dpValue = 6)
                Text(text = ResTools.str(R.string.download_task_downloaded), style = typography.titleSmall, color = MT.color.onBackground.copy(0.6f))
            }
        }
    }

    videoDetails.forEachGroup(3) { items ->
        item {
            RowSplit(columnCount = 3, columnEvenlyPadding = 6) { index ->
                items.getOrNull(index)?.let { file ->
                    Row {
                        DownloadedVideoItem(
                            Modifier
                                .padding(top = 6.dp)
                                .fillMaxWidth(), file, tasksDownloaded
                        ) {
                            videoDetails.remove(file)
                        }
                    }
                }
            }
        }
    }
}
package free.download.video.downloader.ui.screens.downloader

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.lib.bill.manager.BillingRepo
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.component.ResTools
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.activity
import com.tinypretty.ui.dialogs.alertContent
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clip
import com.tinypretty.ui.theme.clipBorderSmall
import com.tinypretty.ui.theme.verticalGradientBackground
import com.tinypretty.ui.utils.ContentRequiredMultiplePermissions
import com.tinypretty.ui.utils.PermissionUtil
import free.download.video.downloader.Constants
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.LocalBillingViewModel
import free.download.video.downloader.LocalFileDownloaderViewModel
import free.download.video.downloader.R
import free.download.video.downloader.analysis.EventValue
import free.download.video.downloader.ext.validFileName
import free.download.video.downloader.model.download.FileEntity
import free.download.video.downloader.repository.cache.CachedValue
import free.download.video.downloader.ui.components.AdUtil
import free.download.video.downloader.ui.components.RectScreen
import free.download.video.downloader.ui.screens.bill.BillingScreen
import java.io.File

/**
 * <AUTHOR>
 * @Since 2024/01/21
 */

@Composable
fun DownloadTaskCreateScreen(state: MutableState<Boolean>, fileEntity: FileEntity, ext: String) {
    ContentRequiredMultiplePermissions(
        requestPermissionGap = 2,
        ignoreAble = false,
        permissions = PermissionUtil.permissionReadWrite.toTypedArray()
    ) {
        DownloadTaskCreateScreenContent(state, fileEntity, ext)
    }
}

@Composable
fun DownloadTaskCreateScreenContent(state: MutableState<Boolean>, fileEntity: FileEntity, ext: String) {
    val log = newLog("DownloadTaskCreateScreen")
    log.i { "DownloadTaskCreateScreenContent ext=$ext" }
    val activity = activity() ?: return
    val vm = LocalFileDownloaderViewModel.current
    val appVM = LocalAppViewModel.current
    val hasSubScribed = BillingRepo.subscribedStateFlow.collectAsState()
    val showSubscribed = rememberSaveable { mutableStateOf(false) }
    CachedValue.showGuide = false

    alertContent(state = showSubscribed) {
        BillingScreen(EventValue.SUBS_FROM_NEW_TASK, showSubscribed, LocalBillingViewModel.current)
    }

    val fileName = rememberSaveable { mutableStateOf(fileEntity.title.validFileName()) }
    Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Column(
            Modifier
                .fillMaxWidth(0.9f)
                .clipBorderSmall(color = MT.color.onBackground)
                .background(Color.White)
        ) {
            Row(
                Modifier
                    .padding(start = 12.dp)
                    .fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = ResTools.str(R.string.download_task_new), modifier = Modifier
                        .width(0.dp)
                        .weight(1f), color = MT.color.primary, style = MT.typography.titleMedium
                )

                ImageApp(data = "res/ic_close.webp",
                    color = MT.color.background,
                    modifier = Modifier
                        .size(36.dp)
                        .padding(12.dp)
                        .clickable {
                            state.value = false
                        })
            }
            Box(
                Modifier
                    .fillMaxWidth()
                    .height(6.dp)
                    .verticalGradientBackground(MT.color.background.copy(0.4f), Color.Transparent)
            )
            Column(Modifier.padding(horizontal = 12.dp)) {
                fun startDownload(threadCount: Int) {
                    if (vm.existTask(fileEntity)) {
                        log.i { "addTask fileEntity=$fileEntity already exists" }
                        Toast.makeText(activity, ResTools.str(R.string.download_task_already_exists), Toast.LENGTH_SHORT).show()
                    } else {
                        log.i { "addTask fileEntity=$fileEntity" }
                        vm.addTask(fileEntity.apply {
                            filename = fileName.value + ext
                            var index = 1
                            while (File(CachedValue.downloadDir, filename).exists()) {
                                filename = "${fileName.value}($index)$ext"
                                index++
                            }
                        }, threadCount)
                    }
                    state.value = false
                    appVM.toDownloadedPage()
                }

                fileEntity.filename = fileName.value
                fileEntity.title = fileName.value

                val color = MT.color.background
                val downloadDir = rememberSaveable { mutableStateOf(CachedValue.downloadDir) }
                Title(id = R.string.download_task_new_filename)
                BasicTextField(
                    value = fileName.value,
                    onValueChange = {
                        fileName.value = it.validFileName()
                        fileEntity.filename = fileName.value
                        fileEntity.title = fileName.value
                    },
                    textStyle = TextStyle(color = color),
                    modifier = Modifier
                        .padding(bottom = 6.dp)
                        .drawWithContent {
                            drawContent()
                            drawLine(
                                color = color.copy(0.6f),
                                start = Offset(2f, size.height),
                                end = Offset(size.width - 4f, size.height),
                                strokeWidth = 1.dp.toPx(),
                                cap = StrokeCap.Round
                            )
                        }
                )


                Title(id = R.string.download_task_new_storage_path)

                val dirColor = if (downloadDir.value == Constants.privateDownloadDir) MT.color.primary else color
                Row(verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.clickable {
                        downloadDir.value = if (CachedValue.downloadDir == Constants.privateDownloadDir) {
                            Constants.publicDownloadDir
                        } else {
                            Constants.privateDownloadDir
                        }
                        CachedValue.downloadDir = downloadDir.value
                    }
                ) {
                    Text(text = downloadDir.value,
                        style = MT.typography.bodyMedium,
                        color = dirColor,
                        modifier = Modifier
                            .width(0.dp)
                            .padding(top = 6.dp, bottom = 6.dp)
                            .weight(1f)
                            .drawWithContent {
                                drawContent()
                                drawLine(
                                    color = dirColor.copy(0.6f),
                                    start = Offset(2f, size.height),
                                    end = Offset(size.width - 4f, size.height),
                                    strokeWidth = 1.dp.toPx(),
                                    cap = StrokeCap.Round
                                )
                            })
                    ImageApp(data = "res/ic_hide.webp", color = dirColor, modifier = Modifier.size(24.dp))
                }
                if (downloadDir.value == Constants.privateDownloadDir) {
                    Text(
                        text = ResTools.str(R.string.download_dir_hidden_hint),
                        style = MT.typography.bodySmall,
                        color = MT.color.onBackground,
                        modifier = Modifier
                            .alpha(0.8f)
                            .fillMaxWidth()
                            .clip(MT.shapes.small)
                            .background(MT.color.background)
                            .padding(6.dp)
                    )
                }

                Title(id = R.string.download_task_new_threads_number)

                val threadCount = rememberSaveable { mutableIntStateOf(0) }
                val threadNames = listOf(ResTools.str(R.string.download_task_new_threads_auto), "1", "2", "3", "4", "5", "6")

                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                    threadNames.forEachIndexed { index, item ->
                        val selected = threadCount.intValue == index
                        val enable = index <= 2 || hasSubScribed.value
                        Box(modifier = Modifier
                            .width(0.dp)
                            .clickable { if (enable) threadCount.intValue = index else showSubscribed.value = true }
                            .weight(1f)) {
                            Text(
                                text = item,
                                color = MT.color.onPrimary,
                                style = MT.typography.bodyMedium,
                                textAlign = TextAlign.Center,
                                fontWeight = FontWeight.Black,
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .fillMaxWidth()
                                    .padding(top = 6.dp, bottom = 6.dp)
                                    .clip(MT.shapes.small)
                                    .background(if (selected) MT.color.primary else Color.Transparent)
                                    .padding(top = 6.dp, bottom = 6.dp)
                            )
                            if (!enable) {
                                ImageApp(
                                    data = "res/ic_vip.webp", modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .size(24.dp)
                                        .padding(top = 6.dp, start = 3.dp, end = 3.dp)
                                )
                            }
                        }
                    }
                }

                Button(modifier = Modifier.fillMaxWidth(), enabled = fileName.value.isNotBlank(), onClick = {
                    startDownload(threadCount.intValue)
                    AdUtil.showCp()
                }) {
                    Text(text = ResTools.str(R.string.download_task_download_start), color = MT.color.onPrimary, fontWeight = FontWeight.Black)
                }

                RectScreen()

//                Box(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .padding(top = 12.dp, bottom = 12.dp)
//                ) {
//                    if (!hasSubScribed.value) {
//                        ImageApp(
//                            data = "res/ic_subscription.webp",
//                            Modifier
//                                .clickable { showSubscribed.value = true }
//                                .fillMaxWidth()
//                                .aspectRatio(800f / 180f), contentScale = ContentScale.FillWidth
//                        )
//                    }
//                }
            }
        }
    }
}

@Composable
private fun Title(id: Int) {
    Text(modifier = Modifier.padding(top = 12.dp), text = ResTools.str(id), color = MT.color.background.copy(0.6f), style = MT.typography.titleSmall)
}
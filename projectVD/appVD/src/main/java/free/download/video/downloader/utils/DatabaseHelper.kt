package free.download.video.downloader.utils

import android.database.Cursor
import android.database.CursorWindow
import android.os.Build
import com.tinypretty.component.GlobalModule.mL
import java.lang.reflect.Field

/**
 * 数据库辅助工具类，用于解决数据库查询相关的问题
 */
object DatabaseHelper {
    private val TAG = "DatabaseHelper"

    /**
     * 增加 CursorWindow 的大小，解决 "Couldn't read row X, col Y from CursorWindow" 错误
     * 默认的 CursorWindow 大小为 2MB，这里增加到 20MB
     */
    fun increaseCursorWindowSize() {
        try {
            val field: Field = CursorWindow::class.java.getDeclaredField("sCursorWindowSize")
            field.isAccessible = true
            // 将 CursorWindow 大小设置为 20MB (默认是 2MB)
            field.set(null, 20 * 1024 * 1024)
            mL.d { "$TAG -> CursorWindow size increased to 20MB" }
        } catch (e: Exception) {
            mL.e { "$TAG -> Failed to increase CursorWindow size: ${e.message}" }
        }
    }

    /**
     * 安全地从 Cursor 中读取字符串，避免 "Couldn't read row X, col Y from CursorWindow" 错误
     * @param cursor 数据库游标
     * @param columnIndex 列索引
     * @return 读取到的字符串，如果读取失败则返回空字符串
     */
    fun safeGetString(cursor: Cursor, columnIndex: Int): String {
        return try {
            cursor.getString(columnIndex) ?: ""
        } catch (e: Exception) {
            mL.e { "$TAG -> Error reading string from cursor: ${e.message}" }
            ""
        }
    }

    /**
     * 安全地从 Cursor 中读取长整型，避免 "Couldn't read row X, col Y from CursorWindow" 错误
     * @param cursor 数据库游标
     * @param columnIndex 列索引
     * @return 读取到的长整型，如果读取失败则返回 0
     */
    fun safeGetLong(cursor: Cursor, columnIndex: Int): Long {
        return try {
            cursor.getLong(columnIndex)
        } catch (e: Exception) {
            mL.e { "$TAG -> Error reading long from cursor: ${e.message}" }
            0L
        }
    }

    /**
     * 安全地从 Cursor 中读取整型，避免 "Couldn't read row X, col Y from CursorWindow" 错误
     * @param cursor 数据库游标
     * @param columnIndex 列索引
     * @return 读取到的整型，如果读取失败则返回 0
     */
    fun safeGetInt(cursor: Cursor, columnIndex: Int): Int {
        return try {
            cursor.getInt(columnIndex)
        } catch (e: Exception) {
            mL.e { "$TAG -> Error reading int from cursor: ${e.message}" }
            0
        }
    }

    /**
     * 安全地从 Cursor 中读取布尔型，避免 "Couldn't read row X, col Y from CursorWindow" 错误
     * @param cursor 数据库游标
     * @param columnIndex 列索引
     * @return 读取到的布尔型，如果读取失败则返回 false
     */
    fun safeGetBoolean(cursor: Cursor, columnIndex: Int): Boolean {
        return try {
            cursor.getInt(columnIndex) == 1
        } catch (e: Exception) {
            mL.e { "$TAG -> Error reading boolean from cursor: ${e.message}" }
            false
        }
    }
}

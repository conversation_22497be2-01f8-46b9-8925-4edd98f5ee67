package free.download.video.downloader.ui.screens.downloader

import CoroutineTask
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.tiny.domain.util.FileUtil
import com.tiny.lib.web.view.sniffer.TabManager
import com.tiny.lib.web.view.sniffer.TabManager.selected
import com.tinypretty.common.utils.TimeDiff
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.ResTools
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.LoadingScreen
import com.tinypretty.ui.componets.Spacer
import com.tinypretty.ui.componets.SpacerFix
import com.tinypretty.ui.componets.SpacerWeight
import com.tinypretty.ui.componets.activity
import com.tinypretty.ui.dialogs.alertContent
import com.tinypretty.ui.dialogs.closeButton
import com.tinypretty.ui.theme.C
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clipBorder
import com.tinypretty.ui.theme.clipBorderSmall
import free.download.video.downloader.Constants
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.LocalFileDownloaderViewModel
import free.download.video.downloader.R
import free.download.video.downloader.model.download.FileEntity
import free.download.video.downloader.utils.MediaExtractorUtils
import free.download.video.downloader.utils.MemoryUtil
import free.download.video.downloader.utils.VideoLoaderCallbacks
import free.download.video.downloader.utils.VideoLoaderUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * <AUTHOR>
 * @Since 2024/02/07
 */


@Composable
fun DownloadedVideoItem(modifier: Modifier, file: File, tasksDownloaded: List<FileEntity>, onFileDelete: (File) -> Unit) {
    val activity = activity() ?: return
    val appVM = LocalAppViewModel.current
    val log = GlobalModule.newLog("DownloadingItem")
    log.i { "DownloadingItem redraw" }
    val fileDownloaderViewModel = LocalFileDownloaderViewModel.current
    val videoDetails: MutableState<VideoLoaderCallbacks.VideoDetails?> = remember { mutableStateOf(null) }
    val inHide = remember { mutableStateOf(file.absolutePath.contains(Constants.privateDownloadDir)) }
    val fileEntity: MutableState<FileEntity?> = remember {
        mutableStateOf(null)
    }

    LaunchedEffect(file.absolutePath) {
        withContext(Dispatchers.IO) {
            try {
                videoDetails.value = VideoLoaderUtil.getVideoDetails(file)
                fileEntity.value = tasksDownloaded.find { File(it.directory, it.filename).absolutePath == file.absolutePath }
            } catch (e: Exception) {
                log.e { "getVideoDetails error ${e.message}" }
            }
        }
    }

    Column(
        modifier
            .fillMaxWidth()
            .clickable {
                appVM.playVideo(file)
            }
            .aspectRatio(3f / 4f)
    ) {
        Box(Modifier.clipBorder(color = MT.color.background, shape = MT.shapes.small)) {
            ImageApp(
                data = if (fileEntity.value?.cover.isNullOrBlank()) (videoDetails.value?.cover ?: "") else (fileEntity.value?.cover ?: ""), contentScale = ContentScale.Crop, modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(4f / 3f)
                    .background(MT.color.background)
            )
            ImageApp(
                data = if (inHide.value) "res/ic_hide.webp" else "res/ic_visible.webp", color = if (inHide.value) MT.color.primary else MT.color.background, modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(32.dp)
                    .padding(start = 12.dp, bottom = 12.dp, top = 2.dp, end = 2.dp)
            )
            Text(
                text = TimeDiff.getDurationString(videoDetails.value?.duration ?: 0),

                color = MT.C().onBackground,
                style = MT.typography.bodySmall,
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .background(MT.color.background.copy(0.8f), RoundedCornerShape(topEnd = 6.dp))
            )
            val showMenu = remember { mutableStateOf(false) }
            alertContent(state = showMenu) {
                Column(
                    Modifier
                        .padding(12.dp)
                        .clipBorderSmall()
                        .background(MT.color.background.copy(0.8f))
                        .padding(12.dp)
                ) {
                    val str = if (inHide.value) {
                        ResTools.str(R.string.menu_move_out)
                    } else {
                        ResTools.str(R.string.menu_move_in)
                    } + ResTools.str(R.string.download_dir_hidden)

                    fun rename(name: String, suffix: String): String {
                        var result = name
                        var index = name.lastIndexOf(".")
                        if (index > 0) {
                            result = name.substring(0, index)
                        }
                        return result + suffix
                    }

                    val process = remember { mutableStateOf(false) }
                    if (process.value) {
                        Box(modifier = Modifier.fillMaxWidth()) {
                            LoadingScreen()
                            Text(
                                modifier = Modifier
                                    .padding(top = 20.dp)
                                    .fillMaxWidth(),
                                text = "${ResTools.str(R.string.menu_convert_to_audio)} ... ",
                                color = MT.color.onPrimary,
                                style = MT.typography.titleLarge,
                                textAlign = TextAlign.Center
                            )
                        }
                    } else {
                        Button(onClick = {
                            process.value = true
                            CoroutineTask("detachMediaAudio").io().launch {
                                val target = File(File(Constants.musicDownloadIir), rename(file.name, ".acc"))
                                MediaExtractorUtils().detachMediaAudio(file.absolutePath, target.absolutePath, succees = {
                                    log.i { "detachMediaAudio success" }
                                    process.value = false
                                    showMenu.value = false
                                    appVM.toMusic()
                                }, fail = {
                                    log.e { "detachMediaAudio fail $it" }
                                    GlobalModule.toast("${ResTools.str(R.string.menu_convert_to_audio)} failed")
                                    process.value = false
                                })
                            }
                        }, modifier.fillMaxWidth()) {
                            Text(text = ResTools.str(R.string.menu_convert_to_audio))
                        }
                        32.SpacerFix()


                        if (fileEntity.value != null) {
                            Button(onClick = {
                                appVM.toBrowserPage(TabManager.newTab(activity).selected(), fileEntity.value?.website)
                                showMenu.value = false
                            }, modifier.fillMaxWidth()) {
                                Text(text = ResTools.str(R.string.book_mark_open_in_new_tab))
                            }
                            32.SpacerFix()
                        }

                        Button(onClick = {
                            val targetDir = if (inHide.value) Constants.publicDownloadDir else Constants.privateDownloadDir
                            file.renameTo(File(targetDir, file.name))
                            fileEntity.value?.let {
                                it.directory = targetDir
                                fileDownloaderViewModel.updateTask(it)
                            }
                            inHide.value = !inHide.value
                            showMenu.value = false
                        }, modifier.fillMaxWidth()) {
                            Text(text = str)
                        }
                        Button(onClick = {
                            CoroutineTask("deleteFile").io().launch {
                                val result = FileUtil.deleteFile(activity = activity, file)
                                withContext(Dispatchers.Main) {
                                    result.onSuccess {
                                        onFileDelete.invoke(file)
                                    }.onFailure {
                                        GlobalModule.toast(it.message ?: "")
                                    }
                                }
                            }
                            showMenu.value = false
                        }, modifier.fillMaxWidth()) {
                            Text(text = ResTools.str(R.string.menu_file_delete))
                        }

                        Spacer(dpValue = 6)
                        closeButton(modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                            .clickable {
                                showMenu.value = false
                            })
                    }
                }
            }

            ImageApp(data = "res/ic_file_menu.webp", color = MT.color.onBackground, modifier = Modifier
                .align(Alignment.BottomEnd)
                .size(32.dp)
                .padding(start = 12.dp, top = 12.dp)
                .background(MT.color.background.copy(0.8f), RoundedCornerShape(topStart = 6.dp))
                .padding(2.dp)
                .clickable {
                    showMenu.value = true
                })
        }

        Text(
            text = file.name,
            color = MT.C().onBackground,
            style = MT.typography.bodySmall,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )

        videoDetails.value?.also { video ->
            Row(Modifier.alpha(0.6f)) {
                Text(text = MemoryUtil().formatSize(video.size), color = MT.C().onBackground, style = MT.typography.bodySmall, maxLines = 1)
                SpacerWeight()
                Spacer(dpValue = 6)
                Text(text = TimeDiff.getTimeAgo(video.creationTime), color = MT.C().onBackground, style = MT.typography.bodySmall, maxLines = 1)
            }
        }
    }

}

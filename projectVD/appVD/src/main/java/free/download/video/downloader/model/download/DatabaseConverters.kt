package free.download.video.downloader.model.download

import androidx.room.TypeConverter
import com.tinypretty.component.GlobalModule.mL

/**
 * Room 数据库类型转换器，用于处理大型 JSON 字符串
 */
class DatabaseConverters {
    private val TAG = "DatabaseConverters"

    /**
     * 将可空字符串转换为非空字符串（用于数据库存储）
     */
    @TypeConverter
    fun nullableStringToString(value: String?): String {
        return value ?: ""
    }

    /**
     * 将非空字符串转换为可空字符串（用于应用使用）
     */
    @TypeConverter
    fun stringToNullableString(value: String): String? {
        return if (value.isEmpty()) null else value
    }
}

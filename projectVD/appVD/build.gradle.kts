import com.google.samples.apps.nowinandroid.configureAndroidSign

plugins {
    alias(libs.plugins.nowinandroid.android.application)
    alias(libs.plugins.nowinandroid.android.application.compose)
    //<todo replace>alias(libs.plugins.nowinandroid.android.application.firebase)</todo replace>
    alias(libs.plugins.nowinandroid.android.room)
    id("org.jetbrains.kotlin.plugin.compose") version "2.1.0"
}

android {
    namespace = "free.download.video.downloader"
    defaultConfig {
        applicationId = "free.test.vd"//<todo replace>applicationId = "free.download.video.downloader"</todo replace>
        versionCode = 397
        versionName = "3.0.0"

        // Add manifest placeholders
        manifestPlaceholders["MANDI_CHANNEL_VALUE"] = "google"
        manifestPlaceholders["MANDI_PUBLISH_TIME_VALUE"] = "20240601"
    }
    configureAndroidSign(
        "",
        "../keystores/key_for_dvd_test.jks;111111;download",//<todo replace>"../keystores/key_for_dvd.jks;111111;download",</todo replace>
        this
    )

    // 确保使用Java 17
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
}

dependencies {
    implementation(libs.androidx.compose.runtime)
    implementation(libs.okhttp)

    implementation("com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer-java:v10.0.0")
    implementation("com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer-exo2:v10.0.0")

    api(project(":libDomain"))
    api(project(":libRoom"))
    api(project(":libBill"))
    api(project(":libFirebase"))
    api(project(":libAdAdmob"))
    api(project(":libWebViewSniffer"))
    api(project(":libOkDownload"))
    api(project(":libM3UI"))
    annotationProcessor(libs.room.compiler)
    implementation(libs.gson)
}

// 确保使用Java 17进行编译
tasks.withType<JavaCompile> {
    sourceCompatibility = JavaVersion.VERSION_17.toString()
    targetCompatibility = JavaVersion.VERSION_17.toString()
}

// 确保Kotlin编译使用Java 17
tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }
}

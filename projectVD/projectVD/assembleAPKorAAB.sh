#!/bin/bash

echo "Please choose an option:"
echo "1. Build APK (assembleGoogleRelease)"
echo "2. Build AAB (bundleGoogleRelease)"
read -p "Enter your choice (1 or 2): " choice
cd /Users/<USER>/CaProjects/AppVD/AppVD/app_vd/projectVD/projectVD/
case "$choice" in
  1)
    echo "Building APK..."
    gradle :appVD:assembleGoogleRelease
    ;;
  2)
    echo "Building AAB..."
    gradle :appVD:bundleGoogleRelease
    ;;
  *)
    echo "Invalid choice. Exiting."
    exit 1
    ;;
esac

echo
echo "Build process completed."
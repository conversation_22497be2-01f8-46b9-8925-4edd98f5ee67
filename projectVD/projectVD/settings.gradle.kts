pluginManagement {
    includeBuild("../../modules/build-logic")
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    versionCatalogs { // 已经放在gradle目录下, 会自动from
        create("libs") {
            from(files("../../modules/build-logic/gradle/libs.versions.toml"))
        }
    }

    println("dependencyResolutionManagement")
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven(url = "https://jitpack.io")
        maven(url = "https://artifact.bytedance.com/repository/pangle")

        mavenCentral()
        google()
        flatDir {
            dirs = setOf(file("libs"), file("../../modules/libAdGoMore/src/libs"))
        }
    }
}
//enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

rootProject.name = "projectVD"
fun includeApp(name: String, include: String) {
    includeModuleList.add(name)
    if (include.isNotBlank()) {
        includeModuleList.addAll("libM3UI;libDomain;$include".split(';'))
    }
    includeModule(name, "../")
}

var includeModuleList = ArrayList<String>()

fun includeModule(name: String, path: String = "../../modules/", dirName: String = "") {
    if (name !in includeModuleList) {
        println("------------------includeModule ignore $name")
        return
    }

    println("+++includeModule $name")

    include(name)

    rootProject.children.forEach {
        if (it.name == name) {
            it.projectDir = file("$path${dirName.ifBlank { name }}")
        }
    }
}

// ============================== APP - START ===========================
includeApp("appVD", "libRoom;libOkDownload;libWebViewSniffer;libFirebase;libBill;libAdAdmob")
// ============================== APP - END ===========================

// ============================== MODULE - START ===========================
includeModule("libSpider")
includeModule("libGSYVD")
includeModule("libAdAdmob")
includeModule("libCameraX")
includeModule("libCameraOCR")
includeModule("libAnalysisUmeng")
includeModule("libDownloader")
includeModule("libKeepAlive")
includeModule("libTask")
includeModule("libOkDownload")
includeModule("libWebViewSniffer")
includeModule("libFirebase")

// ============================== MODULE - END ===========================

// ============================== AD - START ===========================
includeModule("libAdAdmob")

// ============================== AD - END ===========================
// ==============================Base===========================
includeModule("libRoom")
includeModule("libWikiUI")
includeModule("libM3UI")
includeModule("libDomain")
includeModule("libCiperKS")
includeModule("libCiperKSDebug")
includeModule("libDebugUtil")
includeModule("libBill")
includeModule("libTes")
@echo off
echo Please choose an option:
echo 1. Build APK (assembleGoogleRelease)
echo 2. Build AAB (bundleGoogleRelease)
choice /C 12 /N /M "Enter your choice (1 or 2): "

if errorlevel 2 goto Bundle
if errorlevel 1 goto Assemble

:Assemble
echo Building APK...
./gradlew :appVD:assembleGoogleRelease
goto End

:Bundle
echo Building AAB...
./gradlew :appVD:bundleGoogleRelease
goto End

:End
echo.
echo Build process completed.
pause
